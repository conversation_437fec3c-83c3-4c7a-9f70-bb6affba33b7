<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.facishare</groupId>
    <artifactId>fs-fsc</artifactId>
    <version>2.2.0-SNAPSHOT</version>
  </parent>
  <artifactId>fs-fsc-cgi</artifactId>
  <packaging>war</packaging>
  
  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <java.version>21</java.version>
    <jdk.version>21</jdk.version>
    <spring.version>4.3.21.RELEASE</spring.version>
  </properties>

  <dependencies>
    <!-- <PERSON>bo定义及实现，后期直接由Provider暴露服务-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsc-api</artifactId>
      <version>${project.parent.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>fs-enterprise-id-account-converter</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>core-filter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>jaxb-runtime</artifactId>
          <groupId>org.glassfish.jaxb</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsc-core</artifactId>
      <version>${project.parent.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-context</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>javassist-3.14.0-GA</artifactId>
          <groupId>org.ow2.util.bundles</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>curator-client</artifactId>
          <groupId>org.apache.curator</groupId>
        </exclusion>
        <exclusion>
          <artifactId>curator-framework</artifactId>
          <groupId>org.apache.curator</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.activation</artifactId>
          <groupId>com.sun.activation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.activation-api</artifactId>
          <groupId>jakarta.activation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.validation-api</artifactId>
          <groupId>jakarta.validation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xercesImpl</artifactId>
          <groupId>xerces</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 文件上传 -->
    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
      <version>3.0.2</version>
    </dependency>
    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
      <version>${commons-fileupload.version}</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>1.0-DEC-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>jakarta.validation-api</artifactId>
          <groupId>jakarta.validation</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.fmcg</groupId>
      <artifactId>fs-fmcg-sdk-ai</artifactId>
      <version>2.0.0-SNAPSHOT</version>
    </dependency>
    <!--智能路由-->
    <dependency>
      <groupId>com.fxiaoke.service</groupId>
      <artifactId>self-registry-service</artifactId>
    </dependency>

    <!--SpringMvc-->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>
    <!--RestEasy-->
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-multipart-provider</artifactId>
      <version>${resteasy.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>activation</artifactId>
          <groupId>javax.activation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.activation</artifactId>
          <groupId>com.sun.activation</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-spring</artifactId>
      <version>${resteasy.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--组件-Spring 身份-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-active-session-manage-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-id-account-converter</artifactId>
    </dependency>
    <!-- Dubbo -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>jakarta.validation-api</artifactId>
          <groupId>jakarta.validation</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-enterpriserelation-rest-api2</artifactId>
      <version>2.0.2-SNAPSHOT</version>
    </dependency>
    <!--log-->
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>log4j-over-slf4j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jcl-over-slf4j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jul-to-slf4j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-core</artifactId>
      <version>5.8.35</version>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsi-proxy</artifactId>
      <version>${fsi.proxy.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>
  </dependencies>
</project>