package com.fxiaoke.fsc.test;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date : 2024/9/18
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
public class TestBase {

  @BeforeClass
  public static void init() {
    System.setProperty("spring.profiles.debug", "1");
  }

  @Test
  public void test() {
    System.out.println("hello world");
  }
}
