package com.facishare.fsc.utils;

import org.junit.Test;
import static org.junit.Assert.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Test class for FilenameUtil
 */
public class FilenameUtilTest {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Test
    public void testGenerateFileNameWithPrefixAndSuffix() {
        String prefix = "test";
        String suffix = "txt";
        String fileName = FilenameUtil.generateFileName(prefix, suffix);
        
        assertNotNull(fileName);
        assertTrue(fileName.startsWith(prefix + "-"));
        assertTrue(fileName.endsWith("." + suffix));
        
        // Check format: prefix-YYYY-MM-dd.suffix
        String expectedPattern = prefix + "-\\d{4}-\\d{2}-\\d{2}\\." + suffix;
        assertTrue("Filename should match pattern: " + expectedPattern, 
                   fileName.matches(expectedPattern));
    }

    @Test
    public void testGenerateFileNameWithSuffixOnly() {
        String suffix = "log";
        String fileName = FilenameUtil.generateFileName(suffix);
        
        assertNotNull(fileName);
        assertTrue(fileName.endsWith("." + suffix));
        
        // Check format: YYYY-MM-dd.suffix
        String expectedPattern = "\\d{4}-\\d{2}-\\d{2}\\." + suffix;
        assertTrue("Filename should match pattern: " + expectedPattern, 
                   fileName.matches(expectedPattern));
    }

    @Test
    public void testGenerateFileNameContainsCurrentDate() {
        String prefix = "data";
        String suffix = "csv";
        String fileName = FilenameUtil.generateFileName(prefix, suffix);
        
        String currentDate = LocalDateTime.now().format(formatter);
        assertTrue("Filename should contain current date", 
                   fileName.contains(currentDate));
    }

    @Test
    public void testGenerateFileNameWithEmptyPrefix() {
        String prefix = "";
        String suffix = "txt";
        String fileName = FilenameUtil.generateFileName(prefix, suffix);
        
        assertNotNull(fileName);
        assertTrue(fileName.startsWith("-")); // Should start with dash since prefix is empty
        assertTrue(fileName.endsWith("." + suffix));
    }

    @Test
    public void testGenerateFileNameWithEmptySuffix() {
        String prefix = "test";
        String suffix = "";
        String fileName = FilenameUtil.generateFileName(prefix, suffix);
        
        assertNotNull(fileName);
        assertTrue(fileName.startsWith(prefix + "-"));
        assertTrue(fileName.endsWith(".")); // Should end with dot since suffix is empty
    }

    @Test
    public void testGenerateFileNameWithSpecialCharacters() {
        String prefix = "test@file";
        String suffix = "tmp";
        String fileName = FilenameUtil.generateFileName(prefix, suffix);
        
        assertNotNull(fileName);
        assertTrue(fileName.contains(prefix));
        assertTrue(fileName.endsWith("." + suffix));
    }

    @Test
    public void testGenerateFileNameConsistentFormat() {
        String suffix = "log";
        String fileName1 = FilenameUtil.generateFileName(suffix);
        String fileName2 = FilenameUtil.generateFileName(suffix);
        
        // Both should have the same date format (assuming they're generated on the same day)
        String currentDate = LocalDateTime.now().format(formatter);
        assertTrue(fileName1.contains(currentDate));
        assertTrue(fileName2.contains(currentDate));
        
        // Both should have the same structure
        assertTrue(fileName1.matches("\\d{4}-\\d{2}-\\d{2}\\." + suffix));
        assertTrue(fileName2.matches("\\d{4}-\\d{2}-\\d{2}\\." + suffix));
    }

    @Test
    public void testGenerateFileNameWithLongPrefix() {
        StringBuilder longPrefix = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longPrefix.append("a");
        }
        String prefix = longPrefix.toString();
        String suffix = "txt";
        String fileName = FilenameUtil.generateFileName(prefix, suffix);
        
        assertNotNull(fileName);
        assertTrue(fileName.startsWith(prefix + "-"));
        assertTrue(fileName.endsWith("." + suffix));
    }

    @Test
    public void testGenerateFileNameWithLongSuffix() {
        String prefix = "test";
        StringBuilder longSuffix = new StringBuilder();
        for (int i = 0; i < 50; i++) {
            longSuffix.append("x");
        }
        String suffix = longSuffix.toString();
        String fileName = FilenameUtil.generateFileName(prefix, suffix);
        
        assertNotNull(fileName);
        assertTrue(fileName.startsWith(prefix + "-"));
        assertTrue(fileName.endsWith("." + suffix));
    }

    @Test
    public void testGenerateFileNameDateFormat() {
        String suffix = "txt";
        String fileName = FilenameUtil.generateFileName(suffix);
        
        // Extract the date part (everything before the last dot)
        String datePart = fileName.substring(0, fileName.lastIndexOf('.'));
        
        // Verify it matches YYYY-MM-dd format
        assertTrue("Date part should match YYYY-MM-dd format", 
                   datePart.matches("\\d{4}-\\d{2}-\\d{2}"));
        
        // Verify it's a valid date by parsing it
        try {
            LocalDateTime.parse(datePart + "T00:00:00");
        } catch (Exception e) {
            fail("Generated date should be valid: " + datePart);
        }
    }

    @Test
    public void testGenerateFileNameMultipleCalls() {
        String prefix = "test";
        String suffix = "log";
        
        // Generate multiple filenames and verify they all have the correct format
        for (int i = 0; i < 10; i++) {
            String fileName = FilenameUtil.generateFileName(prefix, suffix);
            assertNotNull(fileName);
            assertTrue(fileName.startsWith(prefix + "-"));
            assertTrue(fileName.endsWith("." + suffix));
            assertTrue(fileName.matches(prefix + "-\\d{4}-\\d{2}-\\d{2}\\." + suffix));
        }
    }

    @Test
    public void testGenerateFileNameWithNullPrefix() {
        String suffix = "txt";
        try {
            String fileName = FilenameUtil.generateFileName(null, suffix);
            // If it doesn't throw an exception, check the result
            assertNotNull(fileName);
            assertTrue(fileName.startsWith("null-") || fileName.contains("null"));
        } catch (NullPointerException e) {
            // This is also acceptable behavior
        }
    }

    @Test
    public void testGenerateFileNameWithNullSuffix() {
        String prefix = "test";
        try {
            String fileName = FilenameUtil.generateFileName(prefix, null);
            // If it doesn't throw an exception, check the result
            assertNotNull(fileName);
            assertTrue(fileName.startsWith(prefix + "-"));
        } catch (NullPointerException e) {
            // This is also acceptable behavior
        }
    }

    @Test
    public void testGenerateFileNameSuffixOnlyWithNull() {
        try {
            String fileName = FilenameUtil.generateFileName(null);
            // If it doesn't throw an exception, check the result
            assertNotNull(fileName);
        } catch (NullPointerException e) {
            // This is also acceptable behavior
        }
    }
}
