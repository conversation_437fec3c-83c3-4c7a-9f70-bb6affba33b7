<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
  <dubbo:application name="${dubbo.application.name}"/>

  <dubbo:registry address="${dubbo.registry.address}"/>

  <dubbo:consumer filter="tracerpc"/>

  <dubbo:reference id="filePackedService" interface="com.facishare.warehouse.api.dubbo.FilePackedService"
                   version="1.0" retries="0" timeout="15000" lazy="true" check="false"/>

  <dubbo:reference id="captchaService" interface="com.facishare.fsc.api.service.CaptchaService"  retries="0" timeout="15000" lazy="true" check="false"/>

  <dubbo:reference id="activeSessionAuthorizeService" interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService"  retries="0" timeout="5000" lazy="true" check="false"/>
</beans>
