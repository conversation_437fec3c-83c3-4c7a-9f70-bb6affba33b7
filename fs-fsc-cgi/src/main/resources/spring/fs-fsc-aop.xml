<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd     http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

  <bean id="serviceProfiler" class="com.facishare.fsc.aop.FSCTraceServiceProfiler"
        init-method="initIgnoreExceptionMap" p:configName="${fsc_ignore_exception_config}"/>

  <aop:config proxy-target-class="true">
    <aop:aspect ref="serviceProfiler">
      <aop:around method="profile" pointcut="execution(* com.facishare.fsc.core.fascade.impl.*.*(..))"/>
      <aop:around method="profile" pointcut="execution(* com.facishare.fsc.service.impl.*.*(..))"/>
    </aop:aspect>
  </aop:config>


</beans>