<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
  <bean id="fscMongoDBTemplate" class="com.github.mongo.support.MongoDataStoreFactoryBean"
        p:configName="${fsc_mongo}">
  </bean>
  <bean id="qrImageMongo" class="com.github.mongo.support.MongoDataStoreFactoryBean"
        p:configName="fs-fsc-cgi-config">
  </bean>
  <bean id="contractZipDealTaskManager"
        class="com.facishare.contract.core.service.impl.ContractZipDealTaskManagerImpl"
        p:configName="${fsc_contract_zip_deal_task_config}">
  </bean>
  <bean id="contractAction" class="com.facishare.contract.core.facade.ContractActionImpl">
  </bean>
</beans>