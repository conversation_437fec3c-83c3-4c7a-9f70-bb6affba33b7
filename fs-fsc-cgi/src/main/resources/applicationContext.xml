<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop.xsd">

    <context:component-scan base-package="com.facishare.fsc"/>
    <import resource="classpath:springmvc-resteasy.xml"/>
    <context:annotation-config/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <import resource="classpath:spring/spring-cms.xml"/>
    <import resource="classpath:spring/fs-fsc-aop.xml"/>
    <import resource="classpath:spring/fs-fsc-remote.xml"/>
    <import resource="classpath:spring/fs-fsc-core.xml"/>
    <import resource="classpath:spring/fs-fsc-dubbo.xml"/>
    <import resource="classpath*:spring/ei-ea-converter.xml"/>
    <import resource="classpath:fmcg-sdk-ai.xml"/>
    <bean id="avatarCache" class="com.github.jedis.support.JedisFactoryBean" p:configName="avatar-kafka-redis"/>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
</beans>
