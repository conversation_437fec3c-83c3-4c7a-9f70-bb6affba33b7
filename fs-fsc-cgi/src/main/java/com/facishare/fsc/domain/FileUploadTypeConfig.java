package com.facishare.fsc.domain;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FileUploadTypeConfig {
  private String enterpriseAccount;
  private List<String> supportTypeList;
  private List<String> notCheckTypeList;
  private boolean exactCheck;
  private boolean typeMatching;
}
