package com.facishare.fsc.domain;

import lombok.Getter;

@Getter
public class BaseException extends RuntimeException{

  private final int code;
  private final String message;
  public BaseException(ErrorInfoEnum errorInfo) {
    super(errorInfo.getMessage());
    this.code = errorInfo.getCode();
    this.message = errorInfo.getMessage();
  }

  public BaseException(int code,String message){
    super(message);
    this.code = code;
    this.message = message;
  }

}
