package com.facishare.fsc.domain;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import java.util.Set;
import lombok.Getter;
import lombok.ToString;

@ToString
public class FileCheckConfig {

  // 企业账号
  private final String enterpriseAccount;
  // 支持上传的类型
  private final Set<String> whiteList;
  // 不支持上传的类型
  private final Set<String> blackList;
  // 检查模型 黑名单或白名单模式
  @Getter
  private final boolean whiteModel;
  // 是否精确检查文件实际类型
  private final boolean ableExactCheck;
  // 是否开启严格模式
  private final boolean ableStrictMode;
  // 哪些类型不进行精确检查
  private final Set<String> ignoreExactCheckList;
  // 是否要求文件扩展名与文件实际类型匹配
  private final boolean ableTypeMatching;

  public FileCheckConfig(String enterpriseAccount,String whiteList, String blackList,
      boolean whiteModel, boolean ableExactCheck,boolean ableStrictMode,
      String ignoreExactCheckList, boolean ableTypeMatching) {
    this.enterpriseAccount = enterpriseAccount;
    this.whiteList = splitToSet(whiteList);
    this.blackList = splitToSet(blackList);
    this.whiteModel = whiteModel;
    this.ableExactCheck = ableExactCheck;
    this.ableStrictMode = ableStrictMode;
    this.ignoreExactCheckList = splitToSet(ignoreExactCheckList);
    this.ableTypeMatching = ableTypeMatching;
  }

  public Set<String> splitToSet(String input) {
    if (input == null || input.isEmpty()) {
      return Sets.newHashSet(); // 返回空的 HashSet
    }
    return Sets.newHashSet(
        Splitter.on(",")
            .trimResults()
            .omitEmptyStrings()
            .split(input)
    );
  }

  public boolean ableExactCheck() {
    return ableExactCheck;
  }

  public boolean ableStrictMode() {
    return ableStrictMode;
  }

  public  boolean ableTypeMatching() {
    return ableTypeMatching;
  }

  public boolean isInBlackList(String fileType) {
    return blackList.contains(fileType);
  }

  public boolean isInWhiteList(String fileType) {
    return whiteList.contains(fileType);
  }

  public boolean isIgnoreExactCheckList(String fileType) {
    return ignoreExactCheckList.contains(fileType);
  }

  public boolean check() {
    // 检查模型 黑名单或白名单模式
    if (whiteModel) {
      return whiteList != null && !whiteList.isEmpty();
    } else {
      return blackList != null && !blackList.isEmpty();
    }
  }
}
