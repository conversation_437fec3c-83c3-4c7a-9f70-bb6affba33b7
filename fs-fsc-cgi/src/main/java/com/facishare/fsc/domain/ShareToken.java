package com.facishare.fsc.domain;

import com.facishare.fsc.common.utils.AES256Utils;
import com.google.common.base.Splitter;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ShareToken {

  private String shareToken;

  private String ea;
  private Integer employeeId;
  private String path;
  private String securityGroup;
  private long createTime;
  private long expireTime;
  private String fileName;

  public static Optional<ShareToken> parseAndInit(String shareTokenStr,String fileName){
    try {
      if (shareTokenStr!=null && !shareTokenStr.isEmpty()){
        String plainText = AES256Utils.decode(shareTokenStr);
        List<String> fileInfo = Splitter.on(":").splitToList(plainText);
        if (fileInfo.size() >= 3) {
          ShareToken shareToken = new ShareToken();
          shareToken.setShareToken(shareTokenStr);
          shareToken.setFileName(fileName);
          shareToken.setEa(fileInfo.get(0));
          shareToken.setEmployeeId(Integer.parseInt(fileInfo.get(1)));
          shareToken.setPath(fileInfo.get(2));
          if (fileInfo.size() > 3) {
            shareToken.setSecurityGroup(fileInfo.get(3));
          }
          if (fileInfo.size() > 4) {
            long createTime = Long.parseLong(fileInfo.get(4));
            long expireTime = fileInfo.size() > 5 ? Long.parseLong(fileInfo.get(5)) : 0;
            shareToken.setCreateTime(createTime);
            shareToken.setExpireTime(expireTime);
          }
          return Optional.of(shareToken);
        }
      }
    } catch (Exception e) {
      log.warn("parseSharedToken token:{} decode error", shareTokenStr, e);
    }
    return Optional.empty();
  }

  public boolean isExpired(long defaultExpireTime) {
    return System.currentTimeMillis() - createTime > (expireTime > 0 ? expireTime : defaultExpireTime);
  }

  public boolean isInvalidPATH(){
    if (path == null || path.isEmpty()) {
      return true;
    }
    return !isRegisterPathType();
  }

  private boolean isRegisterPathType() {
    return path.startsWith("N_") || path.startsWith("TN_")
        || path.startsWith("A_") || path.startsWith("TA_")
        || path.startsWith("TC_") || path.startsWith("C_");
  }

  public boolean isCrossFile() {
    return path.startsWith("A_") || path.startsWith("TA_");
  }

  public String getFileName() {
    if (fileName == null || fileName.isEmpty()) {
      return path;
    }
    return fileName;
  }

  public String getUser() {
    return "E." + employeeId;
  }
}
