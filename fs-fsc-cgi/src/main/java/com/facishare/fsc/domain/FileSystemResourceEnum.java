package com.facishare.fsc.domain;

import lombok.Getter;

@Getter
public enum FileSystemResourceEnum {
  N("N"),
  TN("TN"),
  C("C"),
  TC("TC"),
  A("A"),
  TA("TA"),
  G("G");
  private final String resource;
  FileSystemResourceEnum(String resources) {
    this.resource = resources;
  }
  public static FileSystemResourceEnum of(String resources) {
    for (FileSystemResourceEnum value : FileSystemResourceEnum.values()) {
      if (resources.startsWith(value.resource)) {
        return value;
      }
    }
    throw new BaseException(ErrorInfoEnum.INVALID_PARAMETER);
  }
}
