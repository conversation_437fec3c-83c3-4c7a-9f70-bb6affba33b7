package com.facishare.fsc.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@Data
@NoArgsConstructor
@Accessors(chain = true)
public class Result<T>{
  private Integer FailureCode;
  private String  FailureMessage;
  private Integer StatusCode;
  private T Value;
  private Result(Integer FailureCode, String FailureMessage,Integer StatusCode) {
    this.FailureCode = FailureCode;
    this.StatusCode = StatusCode;
    this.FailureMessage = FailureMessage;
  }
  private Result(Integer FailureCode,Integer StatusCode, T Value) {
    this.FailureCode = FailureCode;
    this.StatusCode = StatusCode;
    this.Value = Value;
  }
  public static <T> Result<T> ok(T data) {
    return new Result<>(0,0, data);
  }

  public static Result error(Integer FailureCode, String FailureMessage,Integer StatusCode) {
    return new Result(FailureCode, FailureMessage,StatusCode);
  }

}
