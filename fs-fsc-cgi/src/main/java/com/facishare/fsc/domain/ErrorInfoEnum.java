package com.facishare.fsc.domain;

public enum ErrorInfoEnum {

  SUCCESS(0, "success request"),
  ERROR(-1, "request failed"),

  FSC_DOC_PRE_VIEW(20001, "file preview error"),
  FSC_IMAGE_CODE(20002, "Image verification code error"),
  FSC_FILE_CODE(20003, "File verification code exception"),
  FSC_GDS(20004, "GDS exception"),
  FSC_HTTP_REMOTE_REQUEST_CODE(20005, "HTTP remote request exception"),
  DUBBO_ARG_ERROR(20006, "Dubbo param error"),
  FORM_ARG_ERROR(20007, "form param error"),

  INVALID_PARAMETER(20008, "invalid parameter"),
  INVALID_EXTENSION(20009, "Unauthorized file upload types"),
  FILE_UPLOAD_IO_ERROR(20010, "The client uploads and actively disconnects"),
  FILE_TYPE_MISMATCH(20011, "The actual file type does not match the extension"),



  ;

  private final Integer code;
  private final String message;

  ErrorInfoEnum(Integer code, String message) {
    this.code = code;
    this.message = message;
  }

  public Integer getCode() {
    return code;
  }

  public String getMessage() {
    return message;
  }
}
