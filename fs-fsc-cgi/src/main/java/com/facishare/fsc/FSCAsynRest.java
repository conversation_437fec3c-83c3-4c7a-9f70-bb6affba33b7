package com.facishare.fsc;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.handler.async.AsyncUserServletHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.container.AsyncResponse;
import javax.ws.rs.container.Suspended;
import javax.ws.rs.core.Context;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
@Path("/FSC")
public class FSCAsynRest {
    @Autowired
    private AsyncUserServletHandler servletHandler;
    @Path("/ASYN/{area}/{servlet}/{action}")
    @POST
    public void post(@Suspended final AsyncResponse response,@PathParam("area") String area,@PathParam("servlet") String servlet,@PathParam("action") String action, @Context AuthInfo authInfo){
        servletHandler.process(response,area,servlet,action,authInfo);
    }
    @Path("/ASYN/{area}/{servlet}/{action}")
    @GET
    public void get(@Suspended final AsyncResponse response,@PathParam("area") String area,@PathParam("servlet") String servlet,@PathParam("action") String action, @Context AuthInfo authInfo){
        servletHandler.process(response,area,servlet,action,authInfo);
    }

    @Path("APL/ASYNC/{area}/{servlet}/{action}")
    @POST
    public void aplPost(@Suspended final AsyncResponse response,@PathParam("area") String area,@PathParam("servlet") String servlet,@PathParam("action") String action, @Context AuthInfo authInfo){
        servletHandler.process(response,area,servlet,action,authInfo);
    }
}
