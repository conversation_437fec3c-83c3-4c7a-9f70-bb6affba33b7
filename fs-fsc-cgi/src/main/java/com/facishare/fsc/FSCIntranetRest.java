package com.facishare.fsc;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.intranet.IntranetUserServletHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
@Path("/IntranetApi")
@Slf4j
public class FSCIntranetRest{
    @Autowired
    private IntranetUserServletHandler servletHandler;
    @Path("/{servlet}/{action}")
    @POST
    public Response post(@PathParam("servlet") String servlet, @PathParam("action") String action, @Context AuthInfo authInfo){
        log.info("FSCIntranetRest......{},{}",servlet,action);
        return servletHandler.process(servlet,action,authInfo);
    }
    @Path("/{servlet}/{action}")
    @GET
    public Response get(@PathParam("servlet") String servlet,@PathParam("action") String action, @Context AuthInfo authInfo){
        log.info("FSCIntranetRest......{},{}",servlet,action);
        return servletHandler.process(servlet,action,authInfo);
    }

    @Path("/File/GetByPath/{path}/{ea}/{employeeId}")
    @GET
    public Response get(@PathParam("path") String path,@PathParam("ea") String ea,@PathParam("employeeId") String employeeId, @Context AuthInfo authInfo){
        log.info("FSCIntranetRest......{},{},{}",ea,path,employeeId);
        return servletHandler.getByPath(ea,path,employeeId);
    }

    @Path("/File/DownloadPath/{path}/{ea}/{employeeId}/{sg}/{name}")
    @GET
    public Response get(@PathParam("path") String path,@PathParam("ea") String ea,
                        @PathParam("employeeId") String employeeId,@PathParam("sg") String sg,@PathParam("name") String name, @Context AuthInfo authInfo){
        log.info("FSCIntranetRest......{},{},{},{},{}",ea,path,employeeId,sg,name);
        return servletHandler.downloadByPath(ea,path,employeeId,sg,name);
    }
}
