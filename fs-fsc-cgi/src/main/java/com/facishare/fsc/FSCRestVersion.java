package com.facishare.fsc;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.FSCVersion;
import com.facishare.fsc.handler.UserServletHandler;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
@Path("/FSC")
public class FSCRestVersion {

  @Autowired
  private UserServletHandler servletHandler;
  List<String> availableAreas = Lists.newArrayList(RouteAreas.BaiChuanUser, RouteAreas.GlobalAccessUser, RouteAreas.XiaoKeOrBaiChuanUser, RouteAreas.XiaoKeOrBaiChuanUserNotNeedAuth, RouteAreas.XiaoKeUser);

  @GET
  @Path("/{version:(V|v)[0-9]}/{area}/{servlet}/{action}")
  public Response getHandler(@PathParam("version") String version,
                             @PathParam("area") String area,
                             @PathParam("servlet") String servlet,
                             @PathParam("action") String action,
                             @Context HttpServletRequest request,
                             @Context AuthInfo authInfo) {
    FSCContext.getLocal().setServletRequest(request);
    if (!EnumUtils.isValidEnum(FSCVersion.class, version)) {
      return Response.status(Response.Status.BAD_REQUEST)
                     .entity("UserRouteHandler:Invalid version of " + version)
                     .build();
    }
    FSCContext.getLocal().setFscVersion(Enum.valueOf(FSCVersion.class, version));
    
    if (!availableAreas.contains(area)) {
      return Response.status(Response.Status.BAD_REQUEST).entity("UserRouteHandler:Invalid Area of " + area).build();
    }
    return servletHandler.process(area, servlet, action, authInfo);
  }

  @POST
  @Path("/{version:(V|v)[0-9]}/{area}/{servlet}/{action}")
  public Response postHandler(@PathParam("version") String version,
                              @PathParam("area") String area,
                              @PathParam("servlet") String servlet,
                              @PathParam("action") String action,
                              @Context HttpServletRequest request,
                              @Context AuthInfo authInfo) {
    return getHandler(version, area, servlet, action, request, authInfo);
  }
}
