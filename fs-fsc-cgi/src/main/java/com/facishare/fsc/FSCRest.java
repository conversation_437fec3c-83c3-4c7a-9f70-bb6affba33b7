package com.facishare.fsc;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.handler.UserServletHandler;
import com.google.common.collect.Lists;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
@Path("/FSC")
public class FSCRest {

    @Autowired
    private UserServletHandler servletHandler;
    List<String> availableAreas= Lists.newArrayList(RouteAreas.BaiChuanUser,RouteAreas.GlobalAccessUser,RouteAreas.XiaoKeOrBaiChuanUser,RouteAreas.XiaoKeOrBaiChuanUserNotNeedAuth,RouteAreas.XiaoKeUser);
    @GET
    @Path("/{area}/{servlet}/{action}")
    public Response getHandler(@PathParam("area") String area, @PathParam("servlet") String servlet, @PathParam("action") String action, @Context HttpServletRequest request, @Context AuthInfo authInfo) {
        FSCContext.getLocal().setServletRequest(request);
        if(!availableAreas.contains(area)){
            return Response.status(Response.Status.BAD_REQUEST).entity("UserRouteHandler:Invalid Area of "+area).build();
        }
        return servletHandler.process(area,servlet,action,authInfo);
    }

    @POST
    @Path("/{area}/{servlet}/{action}")
    public Response postHandler(@PathParam("area") String area,@PathParam("servlet") String servlet,@PathParam("action") String action,@Context HttpServletRequest request, @Context AuthInfo authInfo) {
        return getHandler(area,servlet,action,request,authInfo);
    }
}
