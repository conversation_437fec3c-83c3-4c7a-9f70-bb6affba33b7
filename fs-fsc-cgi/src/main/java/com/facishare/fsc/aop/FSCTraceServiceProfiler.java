package com.facishare.fsc.aop;

import com.github.autoconf.ConfigFactory;
import com.github.trace.aop.ServiceProfiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by <PERSON> on 15/12/9.
 */
public class FSCTraceServiceProfiler extends ServiceProfiler {
    public static final Logger logger = LoggerFactory.getLogger(FSCTraceServiceProfiler.class);
    private String configName = "fs-fsc-ignore-exception";

    private Set<Class> classSet = new HashSet<>();

    public FSCTraceServiceProfiler() {
    }

    @PostConstruct
    private void initIgnoreExceptionMap() {
        ConfigFactory.getInstance().getConfig(configName, config -> {
            classSet.clear();
            config.getLines().forEach(className -> {
                        try {
                            classSet.add(Class.forName(className));
                            logger.info("ignore exception item:{}",className);
                        } catch (Exception e) {
                            logger.error("{} class {} not found!", configName, className);
                        }
                    }
            );
        });
    }

    @Override
    protected boolean isFail(Throwable e) {
        if(classSet.contains(e.getClass())|| isCauseClassInIgnoreSet(e)){
            logger.info("ignore {} ",e.getClass().getName());
            return false;
        }
        return super.isFail(e);
    }

    private boolean isCauseClassInIgnoreSet(Throwable e) {
        return e.getCause()!=null&&classSet.contains(e.getCause().getClass());
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

}
