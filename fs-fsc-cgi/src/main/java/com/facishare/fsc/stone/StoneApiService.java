package com.facishare.fsc.stone;

import com.facishare.fsc.core.exceptions.FSCException;
import com.facishare.fsc.domain.FileSystemResourceEnum;
import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile.Arg;
import com.facishare.fsi.proxy.model.warehouse.a.ATempFileUpload;
import com.facishare.fsi.proxy.model.warehouse.a.AUploadFileDirect;
import com.facishare.fsi.proxy.model.warehouse.a.User;
import com.facishare.fsi.proxy.model.warehouse.g.GFileUpload;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileChunkUploadDataRequest;
import com.facishare.stone.sdk.request.StoneFileGetMetaDataRequest;
import com.facishare.stone.sdk.request.StoneFileRangeDownloadRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileGetFileMetaResponse;
import com.facishare.stone.sdk.response.StoneFileRangeDownloadResponse;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.github.autoconf.ConfigFactory;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.ws.rs.core.StreamingOutput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "StoneApiService")
public class StoneApiService {

  @Resource
  private StoneProxyApi stoneProxyApi;

  @Resource
  private AFileStorageService aFileStorageService;

  @Resource
  private GFileStorageService gFileStorageService;

  private long chunkSizeInBytes;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-fsc-cgi-config", config -> {
      chunkSizeInBytes = config.getLong("chunkSizeInBytes", 2 * 1024 * 1024L);
    });
  }

  public StoneFileUploadResponse uploadByStream(FileSystemResourceEnum resourceType,StoneFileUploadRequest request,InputStream stream)
      throws FRestClientException, IOException {
   switch (resourceType){
     case TN:
     case TC:
       return stoneProxyApi.tempFileUploadByStream("n", request, stream);
     case N:
     case C:
       return stoneProxyApi.uploadByStream("n", request, stream);
     case TA:
       return uploadTempAFile(request.getEa(), request.getEmployeeId(), request.getBusiness(),request.getExtensionName(), stream);
     case A:
       return uploadAFile(request.getEa(), request.getEmployeeId(), request.getBusiness(),request.getExtensionName(), stream);
     case G:
       return uploadGFile(request.getEa(), request.getEmployeeId(), request.getExtensionName(), stream);
     default:
       throw new IllegalArgumentException("Unsupported resource type: " + resourceType);
   }
  }

  public StoneFileUploadResponse uploadForKuaiXiaoBySync(StoneFileUploadRequest stoneFileUploadRequest,byte[] bytes) throws IOException, FRestClientException {
    // 由于数据体量较小,且该数据已经实质性的在内存中驻留，因此直接使用ByteArrayInputStream更为高效
    try(InputStream stream = new ByteArrayInputStream(bytes)){
      return stoneProxyApi.uploadByStream("n",stoneFileUploadRequest, stream);
    }
  }

  /**
   * 分片上传文件-上传分片
   *
   * @param ea         企业账号
   * @param employeeId 员工id
   * @param business   业务线
   * @param path       文件Path
   * @param chunkIndex 分片索引
   * @param md5Digest  分片MD5
   * @param stream     分片流
   * @return 是否上传成功
   */
  public boolean chunkUploadDataByStream(String ea, int employeeId, String business, String path, int chunkIndex, String md5Digest, InputStream stream) {
    StoneFileChunkUploadDataRequest stoneFileChunkUploadDataRequest = new StoneFileChunkUploadDataRequest();
    stoneFileChunkUploadDataRequest.setEa(ea);
    stoneFileChunkUploadDataRequest.setEmployeeId(employeeId);
    stoneFileChunkUploadDataRequest.setBusiness(business);
    stoneFileChunkUploadDataRequest.setPath(path);
    stoneFileChunkUploadDataRequest.setChunkIndex(chunkIndex);
    stoneFileChunkUploadDataRequest.setMd5Digest(md5Digest);
    try {
      return stoneProxyApi.chunkUploadDataByStream("n", stoneFileChunkUploadDataRequest, stream);
    } catch (FRestClientException e) {
      if (e.getCode().equalsIgnoreCase("400")) {
        log.warn("chunkUploadDataByStream error,arg={}", stoneFileChunkUploadDataRequest, e);
      } else {
        log.error("chunkUploadDataByStream error,arg={}", stoneFileChunkUploadDataRequest, e);
      }
    }
    return false;
  }

  public Optional<StoneFileGetFileMetaResponse> getFileMeta(String ea, int employeeId, String business, String path, String securityGroup) {
    StoneFileGetMetaDataRequest stoneFileGetMetaDataRequest = new StoneFileGetMetaDataRequest();
    stoneFileGetMetaDataRequest.setEa(ea);
    stoneFileGetMetaDataRequest.setEmployeeId(employeeId);
    stoneFileGetMetaDataRequest.setBusiness(business);
    stoneFileGetMetaDataRequest.setPath(path);
    stoneFileGetMetaDataRequest.setSecurityGroup(securityGroup);
    stoneFileGetMetaDataRequest.setNeedSha256(false);
    try {
      return Optional.ofNullable(stoneProxyApi.getFileMetaData(stoneFileGetMetaDataRequest));
    } catch (FRestClientException e) {
      log.warn("getFileMeta error,arg={}", stoneFileGetMetaDataRequest, e);
    }
    return Optional.empty();
  }

  /**
   * @param ea            企业账号
   * @param employeeId    员工id
   * @param business      业务线
   * @param path          文件Path
   * @param securityGroup 安全组
   * @param fileSize      文件大小
   * @return 文件流 throw FSCException 400 IO异常 500 服务端异常
   */
  public StreamingOutput downloadByRangeStream(String ea, int employeeId, String business, String path, String securityGroup, long fileSize) throws FSCException {
    StoneFileRangeDownloadRequest stoneFileRangeDownloadRequest = new StoneFileRangeDownloadRequest();
    stoneFileRangeDownloadRequest.setEa(ea);
    stoneFileRangeDownloadRequest.setEmployeeId(employeeId);
    stoneFileRangeDownloadRequest.setBusiness(business);
    stoneFileRangeDownloadRequest.setPath(path);
    stoneFileRangeDownloadRequest.setSecurityGroup(securityGroup);
    List<Pair<Long, Long>> chunks = initializationChunks(fileSize);
    // 模拟逐块生成数据并写入输出流
    return outputStream -> {
      try {
        // 使用try-catch确保自动关闭输出流
        chunks.forEach(chunk -> {
          try {
            stoneFileRangeDownloadRequest.setStartPos(chunk.getLeft());
            stoneFileRangeDownloadRequest.setEndPos(chunk.getRight());
            StoneFileRangeDownloadResponse chunkData = stoneProxyApi.downloadByRange(stoneFileRangeDownloadRequest);
            outputStream.write(chunkData.getData());
          } catch (FRestClientException e) {
            log.warn("Range Download error,args={}", stoneFileRangeDownloadRequest, e);
            throw new FSCException("Range Download error,args={}", e, 500);
          } catch (IOException e) {
            log.warn("IO Stream error,args={}", stoneFileRangeDownloadRequest, e);
            throw new FSCException("IO Stream error", e, 400);
          }
        });
        // 在所有数据写入后调用flush确保数据全部写入
        outputStream.flush();
      } finally {
        try {
          outputStream.close();
        } catch (IOException e) {
          log.warn("Failed to close output stream", e);
        }
      }
    };
  }

  //todo: stoneProxyApi.downloadByRange 方法具有兼容性错误,在该方法中会对 EndPos的值-1,所以我们这里就不能-1
  // 正确的代码应为long end = Math.min((index + 1) * chunkSize-1, fileSize-1);
  // 正确的代码需要stoneProxyApi.downloadByRange 修复兼容性错误
  private List<Pair<Long, Long>> initializationChunks(long fileSize) {
    List<Pair<Long, Long>> chunks = new ArrayList<>();
    // 计算分片数
    int numChunks = (int) Math.ceil((double) fileSize / chunkSizeInBytes);
    // 计算分片区间
    for (int index = 0; index < numChunks; index++) {
      long start = index * chunkSizeInBytes;
      long end = Math.min((index + 1) * chunkSizeInBytes, fileSize);
      chunks.add(Pair.of(start, end));
    }
    return chunks;
  }

  public byte[] getAFileByte(String ea,Integer employeeId,String business,String path,String securityGroup){
    Arg arg = new Arg();
    User user = new User();
    user.setEnterpriseAccount(ea);
    user.setEmployId(employeeId);
    arg.setUser(user);
    arg.setBusiness(business);
    arg.setaPath(path);
    arg.setFileSecurityGroup(securityGroup);
    return aFileStorageService.downloadFile(arg).getData();
  }

  public StoneFileUploadResponse uploadTempAFile(String ea, Integer employeeId, String business,String ext,InputStream stream) throws IOException {
    ATempFileUpload.Arg arg = new ATempFileUpload.Arg();
    User user = new User();
    user.setEnterpriseAccount(ea);
    user.setEmployId(employeeId);
    arg.setUser(user);
    arg.setBusiness(business);
    byte[] byteArray = IOUtils.toByteArray(stream);
    long fileSize = byteArray.length;
    arg.setData(byteArray);
    ATempFileUpload.Result result = aFileStorageService.tempFileUpload(arg);
    String aPath = result.getTempFileName();
    StoneFileUploadResponse response = new StoneFileUploadResponse();
    response.setPath(aPath);
    response.setSize(fileSize);
    response.setExtensionName(ext);
    return response;
  }

  public StoneFileUploadResponse uploadAFile(String ea, Integer employeeId, String business,String ext, InputStream stream) throws IOException {
    AUploadFileDirect.Arg arg = new AUploadFileDirect.Arg();
    User user = new User();
    user.setEnterpriseAccount(ea);
    user.setEmployId(employeeId);
    arg.setUser(user);
    arg.setBusiness(business);
    arg.setData(IOUtils.toByteArray(stream));
    AUploadFileDirect.Result result = aFileStorageService.uploadFileDirect(arg);
    String aPath = result.getFinalAPath();
    StoneFileUploadResponse response = new StoneFileUploadResponse();
    response.setPath(aPath);
    response.setSize(result.getFileSize());
    response.setExtensionName(ext);
    return response;
  }

  public StoneFileUploadResponse uploadGFile(String ea, Integer employeeId, String ext, InputStream stream) throws IOException {
    GFileUpload.Arg arg = new GFileUpload.Arg();
    arg.sourceUser = ea + "." + employeeId;
    arg.fileExt = ext;
    arg.IsNeedCache = false;
    arg.data =IOUtils.toByteArray(stream);
    GFileUpload.Result result = gFileStorageService.uploadFile(arg);
    StoneFileUploadResponse response = new StoneFileUploadResponse();
    response.setPath( result.gPath);
    response.setSize(result.fileSize);
    response.setExtensionName(ext);
    return response;
  }

}
