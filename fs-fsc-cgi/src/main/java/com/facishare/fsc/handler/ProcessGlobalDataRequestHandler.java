package com.facishare.fsc.handler;

import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.fascade.GlobalResponseFascade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Response;
import java.util.Map;

/**
 * Created by <PERSON> on 16/5/16.
 */
@Component
public class ProcessGlobalDataRequestHandler extends AbstractProcessHandler implements NoAuthProcessHandler{
    @Autowired
    private GlobalResponseFascade globalResponseFascade;
    @Override
    public Response process(String area, String servlet, String action) {
        Response.ResponseBuilder builder = Response.status(Response.Status.OK);
        ContainerRequestContext context = FSCContext.getCurrentRequestContext();
        switch ( action )
        {
            case "PRSignUp":
                builder=processGlobalDataActionPRSignUp(builder, context );
                break;
            case "PartnerRegister":
                builder=processGlobalDataActionPartnerRegister(builder, context );
                break;
            case "AddIndustryContact":
                builder=processGlobalDataAddIndustryContact(builder,context);
                break;
            default:builder.status(Response.Status.NOT_FOUND);
        }
        return builder.build();
    }

    private Response.ResponseBuilder processGlobalDataAddIndustryContact(Response.ResponseBuilder builder, ContainerRequestContext context) {
        Map<String,String> formParams=getFormParams();
        String phone = formParams.getOrDefault("mobile","").toString();
        String name = formParams.getOrDefault("name","").toString();
        if(context.getRequest().getMethod().equalsIgnoreCase("POST")) {
            return globalResponseFascade.addIndustryContact(name,phone);
        }else{
            return builder.status(Response.Status.FORBIDDEN);
        }
    }

    private Response.ResponseBuilder processGlobalDataActionPartnerRegister(Response.ResponseBuilder builder, ContainerRequestContext context) {
        Map<String,String> formParams=getFormParams();
        String mobile = formParams.getOrDefault("mobile","").toString();
        String name = formParams.getOrDefault("name","").toString();
        String company = formParams.getOrDefault("company","").toString();
        String post = formParams.getOrDefault("post","").toString();
        String intention = formParams.getOrDefault("intention","").toString();
        String product = formParams.getOrDefault("product","").toString();
        if(context.getRequest().getMethod().equalsIgnoreCase("POST")) {
            return globalResponseFascade.partnerRegister(company,name,post,mobile,product,intention);
        }else{
            return builder.status(Response.Status.FORBIDDEN);
        }
    }

    private Response.ResponseBuilder processGlobalDataActionPRSignUp(Response.ResponseBuilder builder, ContainerRequestContext context) {
        Map<String,String> formParams=getFormParams();
        String mobile = formParams.getOrDefault("mobile","").toString();
        String name = formParams.getOrDefault("name","").toString();
        String company = formParams.getOrDefault("company","").toString();
        String post = formParams.getOrDefault("post","").toString();
        if(context.getRequest().getMethod().equalsIgnoreCase("POST")) {
            return globalResponseFascade.prSignUp(mobile, name, company, post);
        }else{
            return builder.status(Response.Status.FORBIDDEN);
        }
    }
}
