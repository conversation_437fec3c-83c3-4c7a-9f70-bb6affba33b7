package com.facishare.fsc.handler;

import com.facishare.fsc.RouteAreas;
import com.facishare.fsc.common.authenticate.AuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
@Slf4j
public class UserServletHandler implements ProcessHandler {
    @Autowired
    private XiaoKeOrBaiXuanNoNeedAuthServletHandler xiaokeOrBaiXuanHandler;
    @Autowired
    private GlobalAccessServletHandler globalAccessHandler;
    @Autowired
    private NeedAuthServletHandler needAuthServletHandler;

    public Response process(String area, String servlet, String action, AuthInfo authInfo) {
        switch (area.toUpperCase()) {
            case RouteAreas.XiaoKeOrBaiChuanUserNotNeedAuth:
                return xiaokeOrBaiXuanHandler.process(area, servlet, action);
            case RouteAreas.GlobalAccessUser:
                return globalAccessHandler.process(area, servlet, action);
            default:
                if (authInfo == null) {
                    log.warn("没有AuthInfo信息:area={},servlet={},action={}", area, servlet, action);
                    return Response.status(Response.Status.FORBIDDEN).build();
                }
                return needAuthServletHandler.process(area, servlet, action, authInfo);
        }
    }
}
