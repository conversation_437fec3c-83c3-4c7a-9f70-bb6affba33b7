package com.facishare.fsc.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.common.utils.QRCodeUtils;
import com.facishare.fsc.common.utils.ValidateCodeUtils;
import com.facishare.fsc.core.model.QRCode;
import com.facishare.fsc.core.model.Result;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.NumberUtils;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Created by Aaron on 16/6/15.
 */
@Component
@Slf4j
public class ProcessCreateImageHandler extends AbstractProcessHandler implements ProcessHandler, NoAuthProcessHandler {
  @Autowired
  private StoneProxyApi stoneProxyApi;

  @Override
  public Response process(String area, String servlet, String action, AuthInfo authInfo) {
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    switch (action) {
      //            FSC/(EM|N)/CreateImage/QRCode
      case "QRCode":
        builder = createQRCodeImage(builder);
        break;
      case "ValidateCode":
        //                FSC/N/CreateImage/ValidateCode
        builder = createValidateCode(builder);
        break;
      case "QRCodeFromString":
        builder = createQRCodeFromString(builder);
        break;
      default:
        builder.status(Response.Status.NOT_FOUND);
    }
    return builder.build();
  }

  @Override
  public Response process(String area, String servlet, String action) {
    return process(area, servlet, action, null);
  }

  private Response.ResponseBuilder createValidateCode(Response.ResponseBuilder builder) {
    try {
      String code = getQueryParam("code");
      if (!Strings.isNullOrEmpty(code)) {
        String content = URLDecoder.decode(code, "UTF-8");
        byte[] data = ValidateCodeUtils.createCodeBytes(28, 25, 1, 1, content);
        return Response.status(Response.Status.OK).header(HttpHeaders.CONTENT_TYPE, "image/jpeg")
            .entity(data);
      } else {
        return builder.status(Response.Status.BAD_REQUEST).entity("QRCode param not exists");
      }
    } catch (Throwable e) {
      log.error( "createQRCodeImage", e);
      return builder.status(Response.Status.BAD_REQUEST).entity("invalid param");
    }
  }

  private Response.ResponseBuilder createQRCodeImage(Response.ResponseBuilder builder) {
    try {
      String qrCode = getQueryParam("Content");
      String type = getQueryParam("type");
      int size = parseInt(getQueryParam("Size"),DEFAULT_SIZE);// a*b格式
      if (!Strings.isNullOrEmpty(qrCode)) {
        String content =
            Strings.isNullOrEmpty(type) ? new String(Base64.getDecoder().decode(qrCode), StandardCharsets.UTF_8)
                : qrCode;
        return builder.entity(QRCodeUtils.encodeToQRCode(content, size, size))
            .header(HttpHeaders.CONTENT_TYPE, "image/jpeg");
      } else {
        return builder.status(Response.Status.BAD_REQUEST).entity("QRCode param not exists");
      }
    } catch (Throwable e) {
      log.error( "createQRCodeImage", e);
      return builder.status(Response.Status.INTERNAL_SERVER_ERROR).entity(e.getMessage());
    }
  }

  private static final Integer DEFAULT_EMPLOYEE_ID = 10000;
  private static final Integer DEFAULT_SIZE = 400;
  private static final String DEFAULT_FILE_NAME = "QRCode";
  private static final String DEFAULT_IMAGE_SUFFIX = "jpeg";
  private static final String DEFAULT_BUSINESS = "createQRCode";
  private static final String DEFAULT_STORAGE_TYPE="n";
  public static final Integer DEFAULT_FONT_SIZE = 30;

  public Response.ResponseBuilder createQRCodeFromString(Response.ResponseBuilder builder) {
    try {
      String ea = getQueryParam("ea");
      String content = getQueryParam("content");
      String model = getQueryParam("codeModel"); // 二维码类型
      if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(content)) {
        return builder.status(Response.Status.BAD_REQUEST).entity(badRequest());
      }
      int employeeId = parseInt(getQueryParam("employeeId"), DEFAULT_EMPLOYEE_ID);
      String fileName = parseString(getQueryParam("fileName"), DEFAULT_FILE_NAME);
      String size = getQueryParam("size"); // a*b格式
      boolean needCdn = parseBoolean(getQueryParam("needCdn"), false);
      boolean encode = parseBoolean(getQueryParam("encode"), false);
      content = encode ? decodeBase64(content) : content;
      byte[] image = QRCodeUtils.encodeByCodeModel(content, size, model);
      String qRCodePath = uploadFileByStream(ea, employeeId, needCdn, image, fileName);
      String result = rightRequest(new QRCode(ea, employeeId, content, size, qRCodePath, fileName, encode, needCdn));
      return builder.entity(result).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
    } catch (Exception e) {
      return builder.status(Response.Status.INTERNAL_SERVER_ERROR).entity(unknownError(e));
    }
  }

  /**
   * 上传文件获取npath或cpath (文件以N或C开头)
   *
   * @param ea         企业EA
   * @param employeeId 员工ID
   * @param needCdn  是否需要Cdn加速
   * @param fileBytes  文件Data
   * @param fileName   文件名
   * @return npath或cpath (根据是否需要cdn加速 即setNeedCdn(true)返回cpath or setNeedCdn(false)返回npath)
   */
  private String uploadFileByStream(String ea, Integer employeeId, boolean needCdn, byte[] fileBytes, String fileName) {
    String nOrcFilePath = "";
    try (ByteArrayInputStream stream = new ByteArrayInputStream(fileBytes)) {
      StoneFileUploadRequest uploadFileArg = new StoneFileUploadRequest();
      uploadFileArg.setEa(ea);
      uploadFileArg.setEmployeeId(employeeId);
      uploadFileArg.setOriginName(fileName);
      uploadFileArg.setFileSize(fileBytes.length);
      uploadFileArg.setExtensionName(DEFAULT_IMAGE_SUFFIX);
      uploadFileArg.setBusiness(DEFAULT_BUSINESS);
      uploadFileArg.setNeedCdn(needCdn);
      nOrcFilePath = stoneProxyApi.uploadByStream(DEFAULT_STORAGE_TYPE, uploadFileArg, stream).getPath();
    } catch (FRestClientException | IOException e) {
      log.error( "createQRCodeFromString-UploadFileStream", e);
    }
    return nOrcFilePath;
  }

  private int parseInt(String str, Integer defaultValue) {
    try {
      return NumberUtils.parseNumber(str, Integer.class);
    } catch (Exception e) {
      return defaultValue;
    }
  }

  private boolean parseBoolean(String str, boolean defaultValue) {
    try {
      if (Strings.isNullOrEmpty(str)) {
        return defaultValue;
      }
      return Boolean.parseBoolean(str);
    } catch (Exception e) {
      return defaultValue;
    }
  }

  private String parseString(String str, String defaultValue) {
    try {
      if (Strings.isNullOrEmpty(str)) {
        return defaultValue;
      }
      return str;
    } catch (Exception e) {
      return defaultValue;
    }
  }

  private String decodeBase64(String content) {
    try {
      return new String(Base64.getDecoder().decode(content), StandardCharsets.UTF_8);
    } catch (Exception e) {
      return content;
    }
  }

  private String rightRequest(QRCode qrCode) {
    Result<QRCode> result = Result.ok(qrCode);
    return JSON.toJSONString(result);
  }
  private String badRequest() {
    Result<String> result = Result.error(400, "parameter error");
    return JSON.toJSONString(result);
  }
  private String unknownError(Exception e) {
    log.error( "createQRCodeFromString", e);
    Result<String> result = Result.error(500,"unknown exception,Please contact the facishare administrator");
    return JSON.toJSONString(result);
  }
}
