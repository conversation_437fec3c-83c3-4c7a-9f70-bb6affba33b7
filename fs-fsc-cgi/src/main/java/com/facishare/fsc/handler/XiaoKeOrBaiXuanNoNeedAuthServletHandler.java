package com.facishare.fsc.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
public class XiaoKeOrBaiXuanNoNeedAuthServletHandler implements NoAuthProcessHandler {
    @Autowired
    private ProcessCaptchaHandler processCaptchaHandler;
    @Autowired
    private ProcessFileShareHandler processFileShareHandler;
    @Autowired
    private ProcessIOSClientRequestHandler processIOSClientRequestHandler;
    @Autowired
    private ProcessGlobalDataRequestHandler processGlobalDataRequestHandler;
    @Autowired
    private ProcessQRLoginHandler processQRLoginHandler;
    @Autowired
    ProcessCreateImageHandler processCreateImageHandler;

    @Override
    public Response process(String area, String servlet, String action) {
        Response.ResponseBuilder builder = Response.status(Response.Status.OK);
        switch (servlet) {
            case "Captcha":
                return processCaptchaHandler.process(area,servlet,action);
            case "RichText":
            case "FileShare":
                return processFileShareHandler.process(area,servlet,action);
            case "iOSVersion":
                return processIOSClientRequestHandler.process(area,servlet,action);
            case "Standalone":
                return processGlobalDataRequestHandler.process(area,servlet,action);
            case "CreateImage":
                return processCreateImageHandler.process(area,servlet,action);
            case "QRLogin":
                return processQRLoginHandler.process(area,servlet,action);
            default:
                builder.status(Response.Status.NOT_FOUND).entity("ServletHandler: Invalid Action of " + action + ", Servlet: " + servlet);
        }
        return builder.build();
    }
}
