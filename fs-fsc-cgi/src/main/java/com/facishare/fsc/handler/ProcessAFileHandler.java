package com.facishare.fsc.handler;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.common.utils.ImageUtils;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.fascade.impl.FileResponseFascadeImpl;
import com.fxiaoke.metrics.CounterService;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadBase;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Response;
import java.io.EOFException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by Aaron on 16/5/14.
 */
@Component
@Slf4j
public class ProcessAFileHandler extends AbstractProcessHandler implements ProcessHandler {
  @Autowired
  private FileResponseFascadeImpl fileResponseFascade;
  @Autowired
  private CounterService counterService;

  @Override
  public Response process(String area, String servlet, String action, AuthInfo authInfo) {
    ContainerRequestContext context = FSCContext.getCurrentRequestContext();
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    switch (action) {
      case "UploadByStream":
        builder = uploadByStream(builder, context, authInfo);
        break;
      case "UploadByForm":
        builder = uploadByForm(builder, context, authInfo);
        break;
      case "UploadByBase64":
        builder = uploadByBase64(builder, context, authInfo);
        break;
      default:
        builder.status(Response.Status.NOT_FOUND);
    }
    return builder.build();
  }

  private Response.ResponseBuilder uploadByStream(Response.ResponseBuilder builder,
                                                  ContainerRequestContext context,
                                                  AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String extension = getRequestHeader("extension");
    String business = getRequestHeader("business");
    byte[] data = null;
    try {
      data = IOUtils.toByteArray(context.getEntityStream());
    } catch (Exception e) {
      if (e instanceof EOFException) {
        log.warn( "uploadByStream", e);
      } else {
        log.error( "uploadByStream", e);
      }
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
    }
    if (data == null || data.length == 0) {
      counterService.inc("upload.file.empty");
      log.error( "uploadByStream:file field not found");
      return builder.status(Response.Status.BAD_REQUEST).entity("file field not exist");
    }
    return fileResponseFascade.aTempFileUpload(authInfo.getEnterpriseAccount(), authInfo.getEmployeeId(), business, data,
      extension == null ?
        "" :
        extension);
  }

  private Response.ResponseBuilder uploadByForm(Response.ResponseBuilder builder,
                                                ContainerRequestContext context,
                                                AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    byte[] data = null;
    DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
    ServletFileUpload upload = new ServletFileUpload(diskFileItemFactory);
    String business = null;
    String extension = null;
    try {
      List<FileItem> items = upload.parseRequest((HttpServletRequest) FSCContext.getRequest());
      Iterator iter = items.iterator();
      while (iter.hasNext()) {
        FileItem item = (FileItem) iter.next();
        if (item.isFormField()) {
          if ("business".equals(item.getFieldName())) {
            business = item.getString();
          }
          if ("extension".equals(item.getFieldName())) {
            extension = item.getString();
          }
        } else {
          data = item.get();
        }
      }
      if (data == null || data.length == 0) {
        counterService.inc("upload.file.empty");
        log.error( "UploadByForm:file field not found");
        return builder.status(Response.Status.BAD_REQUEST).entity("file field not exist ");
      }
      return fileResponseFascade.aTempFileUpload(authInfo.getEnterpriseAccount(), authInfo.getEmployeeId(), business, data,
        extension == null ?
          "" :
          extension);
    } catch (Exception e) {
      if (e instanceof EOFException || e instanceof IOException || e instanceof FileUploadBase.IOFileUploadException) {
        log.warn( "UploadByForm:file field deal error", e);
      } else {
        log.error( "UploadByForm:file field deal error", e);
      }
      return builder.status(Response.Status.BAD_REQUEST).entity("file field deal error");
    }
  }

  private Response.ResponseBuilder uploadByBase64(Response.ResponseBuilder builder,
                                                  ContainerRequestContext context,
                                                  AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    byte[] data = null;
    Map<String, String> requestMap = null;
    String jsongContent = null;
    try {
      jsongContent = new String(IOUtils.toByteArray(context.getEntityStream()), StandardCharsets.UTF_8);
      if (Strings.isNullOrEmpty(jsongContent)) {
        return builder.status(Response.Status.BAD_REQUEST);
      }
      requestMap = JsonUtils.fromJson(jsongContent, HashedMap.class);
      String content = requestMap.get("content");
      data = ImageUtils.fromBase64ToBytes(content);
    } catch (Exception e) {
      log.error( "uploadByBase64:content:{}", jsongContent, e);
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
    }
    if (data == null || data.length == 0) {
      counterService.inc("upload.file.empty");
      log.error( "uploadByBase64:file field not found");
      return builder.status(Response.Status.BAD_REQUEST).entity("file field not exist ");
    }
    return fileResponseFascade.aTempFileUpload(authInfo.getEnterpriseAccount(), authInfo.getEmployeeId(), requestMap.get("business"), data,
      requestMap.get("extension") == null ?
        "" :
        requestMap.get("extension"));
  }


}
