package com.facishare.fsc.handler;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.common.utils.ImageUtils;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.FSCVersion;
import com.facishare.fsc.core.fascade.FileResponseFascade;
import com.facishare.fsc.core.model.SecurityGroups;
import com.facishare.fsc.core.model.vo.FileDownloadQueryVo;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadBase;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Response;
import java.io.EOFException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by Aaron on 16/5/14.
 */
@Component
@Slf4j
public class ProcessAvatarHandler extends AbstractProcessHandler implements ProcessHandler {
  private long avatarSize;
  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-fsc-cgi-config", config -> {
      avatarSize=config.getLong("avatarSize",2097152);
    });
  }

  @Autowired
  private FileResponseFascade fileResponseFascade;

  @Override
  public Response process(String area, String servlet, String action, AuthInfo authInfo) {
    Response.ResponseBuilder builder = Response.status(200);
    ContainerRequestContext context = FSCContext.getCurrentRequestContext();
    switch (action) {
      case "View":
        builder = processAvatarActionView(builder, authInfo);
        break;
      case "Get":
        builder = processAvatarActionGet(builder, authInfo);
        break;
      case "GetAvatar":
        builder = processAvatarActionGetAvatar(builder, authInfo);
        break;
      case "UploadAvatarByStream":
        builder = processAvatarActionUploadByStream(builder, context, authInfo);
        break;
      case "UploadAvatarByBase64":
        builder = processAvatarActionUploadByBase64(builder, context, authInfo);
        break;
      case "UploadAvatarByForm":
        builder = processAvatarActionUploadByForm(builder, context, authInfo);
        break;
      default:
        builder.status(Response.Status.BAD_REQUEST)
               .entity("ServletHandler: Invalid Action of " + action + ", Servlet: " + servlet);
    }
    return builder.build();
  }

  private Response.ResponseBuilder processAvatarActionGetAvatar(Response.ResponseBuilder builder, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    String size = getQueryParam("Size");
    return fileResponseFascade.getByAvatarAndSize(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), path, size, getQueryParam("filetype"), "");
  }

  private Response.ResponseBuilder processAvatarActionGet(Response.ResponseBuilder builder, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    return fileResponseFascade.getByPath(FileDownloadQueryVo.builder()
                                                            .ea(authInfo.getEnterpriseAccount())
                                                            .sourceUser(authInfo.getTicketUserId())
                                                            .path(path)
                                                            .build());
  }

  private Response.ResponseBuilder processAvatarActionView(Response.ResponseBuilder builder, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    String thumbIndex = getQueryParam("ThumbIndex");
    if (!Strings.isNullOrEmpty(thumbIndex)) {
      path = String.format("%s.thumb.%s.jpg", path.split("\\.")[0], thumbIndex);
    }
    return fileResponseFascade.getByPath(FileDownloadQueryVo.builder()
                                                            .ea(authInfo.getEnterpriseAccount())
                                                            .sourceUser(authInfo.getTicketUserId())
                                                            .path(path)
                                                            .securityGroup(SecurityGroups.BaichuanCrossAvatar)
                                                            .build());
  }

  private Response.ResponseBuilder processAvatarActionUploadByStream(Response.ResponseBuilder builder,
                                                                     ContainerRequestContext context,
                                                                     AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST")) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String extension = getRequestHeader("Extension");
    String business = getRequestHeader("Business");
    if (Strings.isNullOrEmpty(business) || Strings.isNullOrEmpty(extension)) {
      return builder.status(Response.Status.BAD_REQUEST).entity("Business or Extension cannot be null");
    }
    byte[] data;
    try {
      data = IOUtils.toByteArray(context.getEntityStream());
      if (data==null||data.length==0||data.length>avatarSize){
        return builder.status(Response.Status.BAD_REQUEST).entity("Head size must be within the specified range"+ avatarSize +" bytes");
      }
    } catch (Exception e) {
      log.warn( "avatar upload stream eof", e);
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
    }
    String headerPersonAvatar = getRequestHeader("PersonAvatar");
    boolean rpcAble;
    if ((FSCVersion.V2 == FSCContext.getLocal().getFscVersion() ||
      (!Strings.isNullOrEmpty(headerPersonAvatar) && headerPersonAvatar.equals("0")))) {
      rpcAble = false;
    } else {
      rpcAble = true;
    }

    return fileResponseFascade.avatarFileUpload(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), data, extension, business, rpcAble);
  }

  private Response.ResponseBuilder processAvatarActionUploadByForm(Response.ResponseBuilder builder,
                                                                     ContainerRequestContext context,
                                                                     AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST")) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String extension = getRequestHeader("Extension");
    String business = getRequestHeader("Business");
    if (Strings.isNullOrEmpty(business) || Strings.isNullOrEmpty(extension)) {
      log.error( "processAvatarActionUploadByStream 参数验证失败");
      return builder.status(Response.Status.BAD_REQUEST);
    }
    boolean rpcAble;
    String headerPersonAvatar = getRequestHeader("PersonAvatar");
    rpcAble = FSCVersion.V2 != FSCContext.getLocal().getFscVersion() &&
        (Strings.isNullOrEmpty(headerPersonAvatar) || !headerPersonAvatar.equals("0"));
    byte[] data = null;
    DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
    ServletFileUpload upload = new ServletFileUpload(diskFileItemFactory);
    try {
      List<FileItem> items = upload.parseRequest((HttpServletRequest) FSCContext.getRequest());
      for (FileItem item : items) {
        if (item.isFormField()) {
          if (Strings.isNullOrEmpty(extension) && "extension".equals(item.getFieldName())) {
            extension = item.getString();
          }
        } else {
          data = item.get();
        }
      }
      if (data == null || data.length == 0||data.length>avatarSize) {
        log.error("UploadAvatarByForm: Head size is not allowed to exceed {} bytes", avatarSize);
        return builder.status(Response.Status.BAD_REQUEST).entity("Head size must be within the specified range"+ avatarSize +" bytes");
      }
      return fileResponseFascade.avatarFileUpload(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), data, extension, business, rpcAble);
    } catch (Exception e) {
      log.warn( "UploadAvatarByForm:file field deal error", e);
      return builder.status(Response.Status.INTERNAL_SERVER_ERROR).entity("file field deal error");
    }
  }

  private Response.ResponseBuilder processAvatarActionUploadByBase64(Response.ResponseBuilder builder,
                                                                     ContainerRequestContext context,
                                                                     AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST")) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    byte[] data;
    Map<String, String> requestMap;
    String jsonContent = null;
    String extension;
    String business;
    String personAvatar;
    try {
      byte[] requestData = IOUtils.toByteArray(context.getEntityStream());
      if (requestData==null||requestData.length==0||requestData.length>avatarSize){
        log.error( "processAvatarActionUploadByBase64:Head size is not allowed to exceed "+ avatarSize +" bytes");
        return builder.status(Response.Status.BAD_REQUEST).entity("Head size must be within the specified range"+ avatarSize +" bytes");
      }
      jsonContent = new String(requestData, StandardCharsets.UTF_8);
      requestMap = JsonUtils.fromJson(jsonContent, HashedMap.class);
      String base64Data = Strings.isNullOrEmpty(requestMap.get("Data")) ?
        requestMap.get("data") :
        requestMap.get("Data");
      data = ImageUtils.fromBase64ToBytes(base64Data);
      extension = Strings.isNullOrEmpty(requestMap.get("Extension")) ?
        requestMap.get("extension") :
        requestMap.get("Extension");
      business = Strings.isNullOrEmpty(requestMap.get("Business")) ?
        requestMap.get("business") :
        requestMap.get("Business");
      personAvatar = Strings.isNullOrEmpty(requestMap.get("PersonAvatar")) ?
        requestMap.get("personAvatar") :
        requestMap.get("PersonAvatar");
    } catch (Exception e) {
      log.error( "processAvatarActionUploadByBase64:data:{}", jsonContent, e);
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
    }
    if (data == null || Strings.isNullOrEmpty(business) || Strings.isNullOrEmpty(extension)) {
      log.error( "processAvatarActionUploadByBase64:file field not found or extension not found  or business not found");
      return builder.status(Response.Status.BAD_REQUEST)
                    .entity("Content field not exist or extension not exist or business not exist");
    }
    boolean rpcAble;
    if ((FSCVersion.V2 == FSCContext.getLocal().getFscVersion() ||
      (!Strings.isNullOrEmpty(personAvatar) && personAvatar.equals("0")))) {
      rpcAble = false;
    } else {
      rpcAble = true;
    }
    return fileResponseFascade.avatarFileUpload(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), data, extension, business, rpcAble);
  }


  public static void main(String[] args) {
    Map<String, String> requestMap;
    String extension;
    String business;
    String personAvatar;
    String jsonContent = "{\"business\":\"yingxiaotong\",\"data\":\"data:image/jpeg;base64,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\",\"extension\":\"jpg\",\"personAvatar\":\"0\"}";
    requestMap = JsonUtils.fromJson(jsonContent, HashedMap.class);
    String base64Data = Strings.isNullOrEmpty(requestMap.get("Data")) ? requestMap.get("data") : requestMap.get("Data");
    byte[] data = ImageUtils.fromBase64ToBytes(base64Data);
    extension = Strings.isNullOrEmpty(requestMap.get("Extension")) ? requestMap.get("extension") : requestMap.get("Extension");
    business = Strings.isNullOrEmpty(requestMap.get("Business")) ? requestMap.get("business") : requestMap.get("Business");
    personAvatar = Strings.isNullOrEmpty(requestMap.get("PersonAvatar")) ? requestMap.get("personAvatar") : requestMap.get("PersonAvatar");
  }
}
