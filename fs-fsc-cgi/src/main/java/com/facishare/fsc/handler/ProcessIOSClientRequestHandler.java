package com.facishare.fsc.handler;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.common.utils.IOSLuaVersionUtils;
import com.facishare.fsc.common.utils.JsPatchVersionUtils;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.FSCContext;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.api.IConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Aaron on 16/5/16.
 */
@Slf4j
@Component
public class ProcessIOSClientRequestHandler extends AbstractProcessHandler implements NoAuthProcessHandler {
    @Value("${iOSVersionPath}")
    public String iOSVersionPath;

    private Map<String, String> globalJsPatchUrls = new HashMap<>();
    private Map<String, String> grayJsPatchUrls = new HashMap<>();
    private Map<String, List<Integer>> globalAppJspatchVersions = new HashMap<>();
    private Map<String, List<Integer>> grayAppJspatchVersions = new HashMap<>();
    private FsGrayReleaseBiz gray = FsGrayRelease.getInstance("jspatch");

    @PostConstruct
    private void init() {
        IConfigFactory factory = ConfigFactory.getInstance();
        factory.getConfig("fs-jspatch-global", (IConfig config) -> {
            globalJsPatchUrls = config.getAll();
            globalJsPatchUrls.forEach((k, v) -> {
                List<String> array = Splitter.on('_').trimResults().omitEmptyStrings().splitToList(k);
                if (array.size() == 3) {
                    String appVersion = array.get(0);
                    int jspatchVersion = NumberUtils.toInt(array.get(1));
                    globalAppJspatchVersions = JsPatchVersionUtils.createAppJsPathVersions(globalAppJspatchVersions, appVersion, jspatchVersion);
                }
            });
        });

        factory.getConfig("fs-gray-jspatch", (IConfig config) -> {
            grayJsPatchUrls = config.getAll();
            grayJsPatchUrls.forEach((k, v) -> {
                if (k.contains("url")) {
                    List<String> array = Splitter.on('_').trimResults().omitEmptyStrings().splitToList(k);
                    if (array.size() == 3) {
                        {
                            String appVersion = array.get(0);
                            int jspatchVersion = NumberUtils.toInt(array.get(1));
                            grayAppJspatchVersions = JsPatchVersionUtils.createAppJsPathVersions(grayAppJspatchVersions, appVersion, jspatchVersion);
                        }
                    }
                }
            });
        });
    }


    @Override
    public Response process(String area, String servlet, String action) {
        Response.ResponseBuilder builder = Response.status(200);
        ContainerRequestContext context = FSCContext.getCurrentRequestContext();
        switch (action) {
            case "CheckUpdate":
                builder = processiOSClientActionCheckUpdate(builder, context);
                break;
            case "CheckUpdateV2":
                builder = processiOSClientActionCheckUpdateV2(builder, context);
                break;
            default:
                builder.status(Response.Status.BAD_REQUEST);
        }
        return builder.build();
    }

    private Response.ResponseBuilder processiOSClientActionCheckUpdate(Response.ResponseBuilder builder, ContainerRequestContext context) {
        String appVersion = getQueryParam("_vn");
        String luaVersion = getQueryParam("luaversion");
        if (Strings.isNullOrEmpty(appVersion)) {
            log.warn("request url:{}, params isn't valid", context.getUriInfo().getRequestUri());
            return builder.status(Response.Status.BAD_REQUEST);
        } else {
            builder.status(200);
            builder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
            builder.entity(IOSLuaVersionUtils.needLuaFileUpdate(iOSVersionPath, appVersion, luaVersion));
        }
        return builder;
    }

    private Response.ResponseBuilder processiOSClientActionCheckUpdateV2(Response.ResponseBuilder builder, ContainerRequestContext context) {
        String appVersion = getQueryParam("appVersion");
          String jspatchVersionStr = getQueryParam("jspatchVersion");
        if (Strings.isNullOrEmpty(appVersion) || Strings.isNullOrEmpty(jspatchVersionStr) || !NumberUtils.isDigits(jspatchVersionStr)) {
            log.warn("request url:{}, params isn't valid", context.getUriInfo().getRequestUri());
            return builder.status(Response.Status.BAD_REQUEST);
        } else {
            String result;
            int latestJsPatchVersion;
            int jspatchVersion = NumberUtils.toInt(jspatchVersionStr);
            AuthInfo authInfo = FSCContext.getLocal().getAuthInfo();
            if (authInfo == null) {
                latestJsPatchVersion = JsPatchVersionUtils.getLatestJsPatchVersion(globalAppJspatchVersions, appVersion, jspatchVersion);
                result = JsPatchVersionUtils.getResponseJson(globalJsPatchUrls, appVersion, jspatchVersion, latestJsPatchVersion);
            } else {
                String euid = authInfo.getEnterpriseAccount() + "." + authInfo.getEmployeeId();
                String rule = appVersion + "_" + jspatchVersion + "_rule";
                boolean hitGray = gray.isAllow(rule, euid);
                log.info("jspatch rule is:{},hitGray is:{}",rule,hitGray);
                if (hitGray) {
                    latestJsPatchVersion = JsPatchVersionUtils.getLatestJsPatchVersion(grayAppJspatchVersions, appVersion, jspatchVersion);
                    if (latestJsPatchVersion > 0) {
                        result = JsPatchVersionUtils.getResponseJson(grayJsPatchUrls, appVersion, jspatchVersion, latestJsPatchVersion);
                    } else {
                        latestJsPatchVersion = JsPatchVersionUtils.getLatestJsPatchVersion(globalAppJspatchVersions, appVersion, jspatchVersion);
                        result = JsPatchVersionUtils.getResponseJson(globalJsPatchUrls, appVersion, jspatchVersion, latestJsPatchVersion);
                    }
                } else {
                    latestJsPatchVersion = JsPatchVersionUtils.getLatestJsPatchVersion(globalAppJspatchVersions, appVersion, jspatchVersion);
                    result = JsPatchVersionUtils.getResponseJson(globalJsPatchUrls, appVersion, jspatchVersion, latestJsPatchVersion);
                }
            }
            builder.status(200);
            builder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
            builder.entity(result);
        }
        return builder;
    }

}
