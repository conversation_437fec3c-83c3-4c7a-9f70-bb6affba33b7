package com.facishare.fsc.handler;

import com.facishare.fsc.api.model.CreateCaptcha;
import com.facishare.fsc.api.service.CaptchaService;
import com.facishare.fsc.common.utils.Guid;
import com.facishare.fsc.core.fascade.impl.GlobalResponseFascadeImpl;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import java.util.List;
import java.util.Objects;
import javax.annotation.PostConstruct;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.NewCookie;
import javax.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON> on 16/5/16.
 */
@Component
@Slf4j
public class ProcessCaptchaHandler extends AbstractProcessHandler implements NoAuthProcessHandler {
  @Autowired
  private GlobalResponseFascadeImpl globalResponseFascade;
  @Autowired
  private CaptchaService captchaService;

  @Override
  public Response process(String area, String servlet, String action) {
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);

    switch (action) {
      case "Get":
        builder = processCatchaActionGet(builder);
        break;
      default:
        builder.status(Response.Status.BAD_REQUEST);
    }

    return builder.build();
  }

  private String cookieDomain;
  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("variables_endpoint", config -> {
      cookieDomain=config.get("cookie_domain");
    });
  }

  private Response.ResponseBuilder processCatchaActionGet(Response.ResponseBuilder builder) {
    String cid = getQueryParam("cid");
    String lan = getQueryParam("lan");
    if (Strings.isNullOrEmpty(lan)) {
      lan = TraceContext.get().getLocale();
    }
    if (Strings.isNullOrEmpty(cid)) {
      builder = builder.status(Response.Status.BAD_REQUEST);
    } else {
      String epxIdCookieKey = "EPXId";
      String epxId = Guid.getGuid();
      CreateCaptcha.Arg arg = new CreateCaptcha.Arg();
      arg.cId = cid;
      arg.epxId = epxId;
      arg.lan = lan;
      NewCookie epxIdCookie = new NewCookie(epxIdCookieKey, epxId, "/", getDomain(), null, -1, true);
      String sameSiteCookie = epxIdCookie+"; SameSite=None";
      builder = Response.status(Response.Status.OK)
          .header(epxIdCookieKey, epxId)
          .header("Set-Cookie", sameSiteCookie)
          .header(HttpHeaders.CONTENT_TYPE, "image/jpeg")
          .entity(captchaService.create(arg).data);
    }
    return builder;
  }

  private String getDomain(){
    if (!Strings.isNullOrEmpty(cookieDomain)){
      return cookieDomain;
    }
    String host = Objects.toString(getRequestHeader("X-Forwarded-Host"), getRequestHeader("host"));
    List<String> domainSplit = Splitter.on(".").splitToList(host);
    if (domainSplit.size()==2){
      return host;
    }
    return Joiner.on(".").join(domainSplit.subList(1,domainSplit.size()));
  }
}
