package com.facishare.fsc.handler;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.fascade.FileResponseFascade;
import com.facishare.fsc.core.model.SecurityGroups;
import com.facishare.fsc.core.model.vo.FileDownloadQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
public class ProcessBaichuanCrossNoticeFileHandler extends AbstractProcessHandler implements
    ProcessHandler {

  @Autowired
  private FileResponseFascade fileResponseFascade;

  @Override
  public Response process(String area, String servlet, String action, AuthInfo authInfo) {
    ContainerRequestContext context = FSCContext.getCurrentRequestContext();
    switch (action) {
      case "ViewImage":
        return processBaichuanCrossNoticeFileActionViewImage(context, authInfo).build();
      case "DownloadAttachment":
        return processBaichuanCrossNoticeFileActionDownloadAttachment(context, authInfo).build();
      case "GetMp3":
        return processBaichuanCrossNoticeFileActionGetMp3(context, authInfo).build();
      case "DocPreview":
        return processBaichuanCrossNoticeFileActionDocPreview(context, authInfo).build();
      case "DocPage":
        return processBaichuanCrossNoticeFileActionDocPage(context, authInfo).build();
      case "Get":
        return processBaichuanCrossNoticeFileActionGet(context, authInfo).build();
      default:
        return Response.status(Response.Status.BAD_REQUEST).entity("ServletHandler: Invalid Action of " + action + ", Servlet: " + servlet).build();
    }

  }

  private Response.ResponseBuilder processBaichuanCrossNoticeFileActionGet(
      ContainerRequestContext context, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    return fileResponseFascade.getByPath(
        FileDownloadQueryVo.builder().ea(authInfo.getEnterpriseAccount())
            .sourceUser(authInfo.getTicketUserId()).path(path)
            .securityGroup(SecurityGroups.BaichuanCrossNotice).build());
  }

  private Response.ResponseBuilder processBaichuanCrossNoticeFileActionDocPage(
      ContainerRequestContext context, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    int index = 1, width = 0, length = 0;
    try {
      index = Integer.parseInt(getQueryParam("PageIndex"));
      width = Integer.parseInt(getQueryParam("Width"));
      length = Integer.parseInt(getQueryParam("MaxContentLength"));
    } catch (NumberFormatException e) {
      return Response.status(Response.Status.BAD_REQUEST).entity("param error");
    }
    return fileResponseFascade
        .docPageByPath(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), path,
            SecurityGroups.BaichuanCrossNotice, index, width, length);
  }

  private Response.ResponseBuilder processBaichuanCrossNoticeFileActionDocPreview(
      ContainerRequestContext context, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    return fileResponseFascade.docPreviewByPath(
        FileDownloadQueryVo.builder().ea(authInfo.getEnterpriseAccount())
            .sourceUser(authInfo.getTicketUserId()).path(path)
            .securityGroup(SecurityGroups.BaichuanCrossNotice).build());
  }

  private Response.ResponseBuilder processBaichuanCrossNoticeFileActionGetMp3(
      ContainerRequestContext context, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    return fileResponseFascade.getMp3ByPath(
        FileDownloadQueryVo.builder().ea(authInfo.getEnterpriseAccount())
            .sourceUser(authInfo.getTicketUserId()).path(path)
            .securityGroup(SecurityGroups.BaichuanCrossNotice).build());
  }

  private Response.ResponseBuilder processBaichuanCrossNoticeFileActionDownloadAttachment(
      ContainerRequestContext context, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    return fileResponseFascade.downloadByPath(
        FileDownloadQueryVo.builder().ea(authInfo.getEnterpriseAccount())
            .sourceUser(authInfo.getTicketUserId()).path(path)
            .securityGroup(SecurityGroups.BaichuanCrossNotice).build(), getQueryParam("Name"));
  }

  private Response.ResponseBuilder processBaichuanCrossNoticeFileActionViewImage(
      ContainerRequestContext context, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    return fileResponseFascade.getByPath(
        FileDownloadQueryVo.builder().ea(authInfo.getEnterpriseAccount())
            .sourceUser(authInfo.getTicketUserId()).path(path)
            .securityGroup(SecurityGroups.BaichuanCrossNotice).build());
  }
}
