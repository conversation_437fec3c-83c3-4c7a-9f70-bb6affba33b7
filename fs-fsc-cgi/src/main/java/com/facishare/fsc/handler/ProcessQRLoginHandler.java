package com.facishare.fsc.handler;

import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.fascade.FileResponseFascade;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/16.
 */
@Component
public class ProcessQRLoginHandler extends AbstractProcessHandler implements NoAuthProcessHandler {
  @Autowired
  private FileResponseFascade fileResponseFascade;

  @Override
  public Response process(String area, String servlet, String action) {
    Response.ResponseBuilder builder = Response.status(200);
    ContainerRequestContext context = FSCContext.getCurrentRequestContext();
    switch (action) {
      case "GetQRImage":
        builder = processQRLoginActionGetQRImage(context);
        break;
      default:
        builder.status(Response.Status.NOT_FOUND);
    }
    return builder.build();
  }

  private Response.ResponseBuilder processQRLoginActionGetQRImage(ContainerRequestContext context) {
    String qrCode = getQueryParam("QRCode");
    if (Strings.isNullOrEmpty(qrCode)) {
      return Response.status(Response.Status.BAD_REQUEST);
    }
    int size = 200;
    try {
      Integer.parseInt(getQueryParam("Size"));
    } catch (Exception e) {
      size = 200;
    }
    return fileResponseFascade.findQRImage(qrCode, size);
  }
}
