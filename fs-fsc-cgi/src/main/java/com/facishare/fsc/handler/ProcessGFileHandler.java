package com.facishare.fsc.handler;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.fascade.impl.FileResponseFascadeImpl;
import com.fxiaoke.metrics.CounterService;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
@Slf4j
public class ProcessGFileHandler extends AbstractProcessHandler implements ProcessHandler {
  @Autowired
  private FileResponseFascadeImpl fileResponseFascade;
  @Autowired
  private CounterService counterService;

  @Override
  public Response process(String area, String servlet, String action, AuthInfo authInfo) {
    ContainerRequestContext context = FSCContext.getCurrentRequestContext();
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    switch (action) {
      case "UploadByStream":
        builder = uploadByStream(builder, context, authInfo);
        break;
      default:
        builder.status(Response.Status.NOT_FOUND);
    }
    return builder.build();
  }

  private Response.ResponseBuilder uploadByStream(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String extension = getRequestHeader("extension");
    byte[] data;
    try {
      data = IOUtils.toByteArray(context.getEntityStream());
    } catch (Exception e) {
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
    }
    if (data == null || data.length == 0) {
      counterService.inc("upload.file.empty");
    }
    return fileResponseFascade.gTempFileUpload(authInfo.getSourceUser(),
      data,
      Strings.isNullOrEmpty(extension) ? "" : extension);
  }

}
