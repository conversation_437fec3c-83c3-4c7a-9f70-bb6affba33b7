package com.facishare.fsc.handler.async;

import com.facishare.fsc.RouteAreas;
import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.common.document.BatchDownloadDocumentUtils;
import com.facishare.fsc.common.document.BatchFileDocument;
import com.facishare.fsc.common.document.BatchFileEntity;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.FSSwitch;
import com.facishare.fsc.core.WarehouseType;
import com.facishare.fsc.core.fascade.FileResponseFascade;
import com.facishare.fsc.core.model.SecurityGroups;
import com.facishare.fsc.handler.AbstractProcessHandler;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.ws.rs.container.AsyncResponse;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

/**
 * Created by Aaron on 16/5/25.
 */
@Component
@Slf4j
public class AsyncUserServletHandler extends AbstractProcessHandler implements AsyncProcessHandler {
    @Resource
    private FileResponseFascade fileResponse;

  @Override
  public void process(AsyncResponse asyncResponse, String area, String servlet, String action, AuthInfo authInfo) {
    if (authInfo == null) {
      asyncResponse.resume(Response.status(Response.Status.UNAUTHORIZED).entity("No access"));
      return;
    }
    if (area.equals(RouteAreas.XiaoKeUser) || area.equals(RouteAreas.BaiChuanUser) || area.equals(RouteAreas.XiaoKeOrBaiChuanUser)) {
      processAsyncRequest(asyncResponse, servlet, action, getSecurityGroup(area), authInfo);
    } else {
      asyncResponse.resume(Response.status(Response.Status.NOT_FOUND).build());
    }
  }

  private String getSecurityGroup(String area) {
    if (area.equals(RouteAreas.BaiChuanUser)){
      return SecurityGroups.BaichuanCrossNetDisk;
    }
    return null;
  }

  private void processAsyncRequest(AsyncResponse asyncResponse,
                                   String servlet,
                                   String action,
                                   String securityGroup,
                                   AuthInfo authInfo) {
    if (servlet.equals("File")) {
      processFile(asyncResponse, securityGroup, action, authInfo);
    } else {
      asyncResponse.resume(Response.status(Status.NOT_FOUND).build());
    }
  }

  private void processFile(AsyncResponse asyncResponse, String securityGroup, String action, AuthInfo authInfo) {
    switch (action) {
      case "batchDownload":
        batchDownload(asyncResponse, securityGroup, authInfo);
        break;
      case "BatchDownloadByToken":
        batchDownloadByToken(asyncResponse, authInfo);
        break;
      default:
        asyncResponse.resume(Response.status(Response.Status.NOT_FOUND).build());
    }
  }

  private void batchDownloadByToken(AsyncResponse response, AuthInfo authInfo) {
    response.setTimeout(FSSwitch.COMET_FILE_DOWNLOAD_TIMEOUT, TimeUnit.SECONDS);
    String fileToken = getQueryParam("FileToken");
    Map<String, Object> map = Maps.newHashMap();
    if (Strings.isNullOrEmpty(fileToken)) {
      map.put("status", 3);
      map.put("message", "query param not correct!");
      response.resume(Response.ok()
                              .entity(JsonUtils.toJson(map))
                              .encoding("UTF-8")
                              .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                              .build());
    }
    fileResponse.asyncBatchDownloadByToken(response, authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), fileToken);
  }


  public void batchDownload(AsyncResponse response, String securityGroup, AuthInfo authInfo) {

    if (!FSCContext.getCurrentRequestContext().getRequest().getMethod().equalsIgnoreCase("POST")) {
      response.resume(Response.Status.METHOD_NOT_ALLOWED);
      return;
    }
    // 设置超时时间
    response.setTimeout(FSSwitch.COMET_FILE_DOWNLOAD_TIMEOUT, TimeUnit.SECONDS);
    String document = null;
    try {
      String bodyString = getBodyToString();
      BatchFileDocument docList = JsonUtils.fromJson(bodyString, BatchFileDocument.class);
      //给共享文件的path加上扩展名
      List<BatchFileEntity> fileEntities= docList.getFiles();
      for(BatchFileEntity batchFileEntity:fileEntities) {
        String name = batchFileEntity.Name;
        String path = batchFileEntity.Path;
        String ext = FilenameUtils.getExtension(name);
        path = path + "." + ext;
        batchFileEntity.Path = path;
      }
      if (Strings.isNullOrEmpty(docList.getWt())) {
        docList.setWt(WarehouseType.N);
      }
      document = BatchDownloadDocumentUtils.getFileDocumentStr(docList);
      fileResponse.asyncBatchDownloadByDocument(response,"none", authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), securityGroup, document, docList
        .getWt());
    } catch (Exception e) {
      log.error( "批量下载文档失败,securityGroup:{},authInfo:{},downloadXML:{}", securityGroup, authInfo, document, e);
      Map<String, Object> map = Maps.newHashMap();
      map.put("status", 3);
      map.put("message", "Failed to parse batch download document");
      response.resume(Response.ok()
                              .entity(JsonUtils.toJson(map))
                              .encoding("UTF-8")
                              .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                              .build());
    }
  }
}
