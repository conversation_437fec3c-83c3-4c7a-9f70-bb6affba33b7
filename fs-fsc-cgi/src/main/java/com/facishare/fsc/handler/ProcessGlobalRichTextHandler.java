package com.facishare.fsc.handler;

import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.fascade.impl.FileResponseFascadeImpl;
import com.facishare.fsc.core.model.vo.FileDownloadQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/17.
 */
@Component
public class ProcessGlobalRichTextHandler extends AbstractProcessHandler implements
    NoAuthProcessHandler {

  @Autowired
  private FileResponseFascadeImpl fileResponseFascade;

  @Override
  public Response process(String area, String servlet, String action) {
    Response.ResponseBuilder builder = Response.status(200);
    ContainerRequestContext context = FSCContext.getCurrentRequestContext();
    switch (action) {
      case "ViewPic":
        builder = processGlobalRichTextActionViewPic(builder, context);
        break;
      default:
        builder.status(Response.Status.NOT_FOUND);
    }
    return builder.build();
  }

  private Response.ResponseBuilder processGlobalRichTextActionViewPic(
      Response.ResponseBuilder builder, ContainerRequestContext context) {
    String gPath = getQueryParam("GPath");
    return fileResponseFascade.getByPath(FileDownloadQueryVo.builder().path(gPath).build());
  }
}
