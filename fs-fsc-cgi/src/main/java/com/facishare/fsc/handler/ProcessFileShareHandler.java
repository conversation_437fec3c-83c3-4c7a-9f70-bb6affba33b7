package com.facishare.fsc.handler;

import com.facishare.fsc.common.utils.BrowserUtils;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.fascade.FileResponseFascade;
import com.facishare.fsc.core.remote.DownloadSpeedLimiter;
import com.facishare.fsc.core.repository.FileRepository;
import com.facishare.fsc.domain.Constant;
import com.facishare.fsc.domain.ShareToken;
import com.facishare.fsc.core.model.vo.FileDownloadQueryVo;
import com.facishare.fsc.stone.StoneApiService;
import com.facishare.stone.sdk.response.StoneFileGetFileMetaResponse;
import com.fxiaoke.common.Guard;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.io.InputStream;
import java.util.Optional;
import javax.annotation.Resource;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * Created by Aaron on 16/5/16.
 */
@Slf4j
@Component
public class ProcessFileShareHandler extends AbstractProcessHandler implements NoAuthProcessHandler {

  @Autowired
  private FileResponseFascade fileResponseFascade;

  @Resource
  private StoneApiService stoneApiService;

  @Resource
  private DownloadSpeedLimiter speedLimiter;
  @Resource
  private FileRepository fileRepository;

  private String fileShareSkey;

  private long sharedTokenExpMills;

  @PostConstruct
  private void init() {
    ConfigFactory.getInstance().getConfig("fs-fsc-cgi-config", config -> {
      sharedTokenExpMills = config.getLong("shared_token_exp_mills", 3600000L);
    });
    ConfigFactory.getInstance().getConfig("fs-fsc-fileshare", config -> {
      fileShareSkey = config.get("skey");
    });
  }

  @Override
  public Response process(String area, String servlet, String action) {
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    ContainerRequestContext context = FSCContext.getCurrentRequestContext();
    switch (action) {
      case "Download":
        /*
        有过期时间
         */
        builder = processFileShareActionDownload(context);
        break;
      case "DownloadByPath":
        /*
        只针对G文件
         */
        builder = processFileShareActionDownloadByPath(context);
        break;
      case "ViewImage":
        /*
        有过期时间
         */
        builder = processFileShareActionViewImage(context);
        break;
      case "ViewPic":
        /*
        无过期时间，支持FPath
         */
        builder = processFileShareActionViewPic(context);
        break;
      case "ViewContent":
        builder = processRichTextActionViewContentForShare(context);
        break;
      case "GetAvatarByToken":
        /*
        无过期时间
         */
        builder = processGetAvatarByToken(builder);
        break;
      case "GetFileBySharedToken":
        /*
         * 无过期时间
         */
        builder = processGetFileBySharedToken(builder);
        break;
      case "DownloadFileBySharedToken":
        /*
          无过期时间
         */
        builder = processDownloadFileBySharedToken(builder);
        break;
      case "GetMp3FileByShareToken":
        builder = processGetMp3FileByShareToken(builder);
        break;
      case "ShowImage":
        builder = processShowImage(builder);
        break;
      case "QrShow":
        builder = processQrShow(builder);
        break;
      case "DownloadFileBySharedTokenV2":
        builder = processDownloadFileBySharedTokenV2(builder);
        break;
      default:
        builder.status(Response.Status.NOT_FOUND);
    }
    return builder.build();
  }

  private Response.ResponseBuilder processGetMp3FileByShareToken(Response.ResponseBuilder builder) {
    String token = getQueryParam("sharedToken");
    String fileName = getQueryParam("name");
    Optional<ShareToken> shareTokenOptional = ShareToken.parseAndInit(token,fileName);
    // 无效的 shareToken
    if (shareTokenOptional.isEmpty()||shareTokenOptional.get().isInvalidPATH()) {
      log.warn("Invalid shareToken:{}", token);
      return Response.status(Status.BAD_REQUEST).entity("Invalid shareToken");
    }
    ShareToken shareToken = shareTokenOptional.get();
    // shareToken 过期
    if (shareToken.isExpired(sharedTokenExpMills)) {
      return Response.status(Status.BAD_REQUEST).entity("Expired shareToken");
    }
    return getMp3File(shareToken, builder);
  }

  private Response.ResponseBuilder getMp3File(ShareToken shareToken, Response.ResponseBuilder responseBuilder) {
    String ea = shareToken.getEa();
    String user = shareToken.getUser();
    String path = shareToken.getPath();
    String securityGroup = shareToken.getSecurityGroup();
    byte[] data = fileRepository.getMp3ByPath(ea, user, path, securityGroup);
    if (data == null) {
      responseBuilder.status(Response.Status.NOT_FOUND);
    } else {
      responseBuilder
          .status(Status.OK)
          .header(HttpHeaders.CONTENT_TYPE, "audio/mpeg")
          .header(HttpHeaders.CACHE_CONTROL, "private, max-age=120")
          .header(HttpHeaders.CONTENT_LENGTH, data.length);
      InputStream stream = speedLimiter.wrapWithSpeedLimit(data, ea, user, path);
      responseBuilder.entity(stream);
    }
    return responseBuilder;
  }

  private Response.ResponseBuilder processRichTextActionViewContentForShare(ContainerRequestContext context) {
    String sourceId = getQueryParam("SourceId");
    return fileResponseFascade.findEnterpriseRichText(null, sourceId);
  }

  private Response.ResponseBuilder processGetAvatarByToken(Response.ResponseBuilder builder) {
    String avatarToken = getQueryParam("AvatarToken");
    String index = getQueryParam("Index");
    if (Strings.isNullOrEmpty(avatarToken)) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    return fileResponseFascade.getAvatarByToken(avatarToken, index, Maps.newHashMap());
  }

  private Response.ResponseBuilder processGetFileBySharedToken(Response.ResponseBuilder builder) {
    String token = getQueryParam("sharedToken");
    if (Strings.isNullOrEmpty(token)) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    return fileResponseFascade.getFileBySharedToken(token, Maps.newHashMap());
  }

  private Response.ResponseBuilder processDownloadFileBySharedToken(Response.ResponseBuilder builder) {
    String token = getQueryParam("sharedToken");
    String fileName = getQueryParam("name");
    Optional<ShareToken> shareTokenOptional = ShareToken.parseAndInit(token,fileName);
    // 无效的 shareToken
    if (shareTokenOptional.isEmpty()||shareTokenOptional.get().isInvalidPATH()) {
      log.warn("Invalid shareToken:{}", token);
      return Response.status(Status.BAD_REQUEST).entity("Invalid shareToken");
    }
    ShareToken shareToken = shareTokenOptional.get();
    // shareToken 过期
    if (shareToken.isExpired(sharedTokenExpMills)) {
      return Response.status(Status.BAD_REQUEST).entity("Expired shareToken");
    }
    return downloadFile(shareToken);
  }

  private Response.ResponseBuilder downloadFile(ShareToken shareToken){
    if (shareToken.isCrossFile()){
      return downloadAFile(shareToken);
    } else {
      return downloadNCFile(shareToken);
    }
  }

  private Response.ResponseBuilder downloadAFile(ShareToken shareToken) {
    byte[] aFileByte = stoneApiService.getAFileByte(shareToken.getEa(), shareToken.getEmployeeId(), Constant.DEFAULT_BUSINESS, shareToken.getPath(), shareToken.getFileName());
    InputStream speedLimitStream = speedLimiter.wrapWithSpeedLimit(aFileByte, shareToken.getEa(),
        shareToken.getUser(), shareToken.getPath());
    return Response.status(Response.Status.OK)
        .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM)
        .header(HttpHeaders.CONTENT_LENGTH, aFileByte.length)
        .header("Entity-Length", aFileByte.length)
        .header(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), shareToken.getFileName()))
        .entity(speedLimitStream);
  }

  private Response.ResponseBuilder downloadNCFile(ShareToken shareToken) {
    Optional<StoneFileGetFileMetaResponse> fileMetaOption = stoneApiService.getFileMeta(shareToken.getEa(),shareToken.getEmployeeId(), Constant.DEFAULT_BUSINESS, shareToken.getPath(), shareToken.getFileName());
    if (fileMetaOption.isEmpty() || fileMetaOption.get().getSize() == 0) {
      return Response.status(Response.Status.NOT_FOUND).entity("file not found");
    }
    StoneFileGetFileMetaResponse fileMeta = fileMetaOption.get();
    if (Strings.isNullOrEmpty(shareToken.getFileName())) {
      shareToken.setFileName(fileMeta.getOriginName());
    }
    return Response.status(Response.Status.OK)
        .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM)
        .header(HttpHeaders.CONTENT_LENGTH, fileMeta.getSize())
        .header("Entity-Length", fileMeta.getSize())
        .header(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), shareToken.getFileName()))
        .entity(stoneApiService.downloadByRangeStream(shareToken.getEa(), shareToken.getEmployeeId(), Constant.DEFAULT_BUSINESS, shareToken.getPath(), shareToken.getSecurityGroup(), fileMeta.getSize()));
  }

  private Response.ResponseBuilder processDownloadFileBySharedTokenV2(Response.ResponseBuilder builder) {
    String token = getQueryParam("sharedToken");
    String name = getQueryParam("name");
    if (Strings.isNullOrEmpty(token)) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    Guard guard = new Guard(fileShareSkey);
    List<String> detail = Lists.newArrayList();
    try {
      detail = Splitter.on("$").trimResults().splitToList(guard.decode(token));
    } catch (Exception e) {
      log.error("decode file token error :{}", token);
    }
    String ea = detail.get(0);
    String employeeId = detail.get(1);
    String path = detail.get(2);
    long expireTime;
    if (detail.size() == 5) {
      long timeStamp = Long.valueOf(detail.get(3));
      int invalidDay = Integer.valueOf(detail.get(4));
      expireTime = timeStamp + (invalidDay * 86400000L);
    } else if (detail.size() == 6) {
      expireTime = Long.parseLong(detail.get(4));
    } else {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    if (System.currentTimeMillis() > expireTime) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    FileDownloadQueryVo fileDownloadQueryVo = FileDownloadQueryVo.builder()
      .path(path)
      .ea(ea)
      .sourceUser("E." + employeeId)
      .securityGroup("XiaoKeNetDisk")
      .build();
    //不支持预览
    if (Strings.isNullOrEmpty(name)) {
      name = path;
    }
    return fileResponseFascade.downloadByPath(fileDownloadQueryVo, name);
  }

  private Response.ResponseBuilder processShowImage(Response.ResponseBuilder builder) {
    String fileId = getQueryParam("fileId");
    if (Strings.isNullOrEmpty(fileId)) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    Guard guard = new Guard(fileShareSkey);
    try {
      String originalStr = guard.decode(fileId);
      log.info("ShowImage fileId:{}", originalStr);
      List<String> list = Splitter.on('$').trimResults().omitEmptyStrings().splitToList(originalStr);
      if (list.size() == 3) {
        String ea = list.get(0);
        String employeeId = list.get(1);
        String npath = list.get(2);
        builder = fileResponseFascade.getByPath(FileDownloadQueryVo.builder().ea(ea).path(npath).sourceUser(ea + "." + employeeId).build());
        return builder;
      } else if (list.size() == 4) {
        String ea = list.get(0);
        String employeeId = list.get(1);
        String npath = list.get(2);
        String sg = list.get(3);
        builder = fileResponseFascade.getByPath(FileDownloadQueryVo.builder().ea(ea).path(npath).sourceUser(ea + "." + employeeId).securityGroup(sg).build());
        return builder;
      } else {
        return builder.status(Response.Status.BAD_REQUEST);
      }

    } catch (Exception e) {
      log.warn("fileId is invalid!");
      return builder.status(Response.Status.BAD_REQUEST);
    }
  }

  private Response.ResponseBuilder processQrShow(Response.ResponseBuilder builder) {
    String encryptUrl = getQueryParam("url");
    String sizeStr = getQueryParam("size");
    int defaultSize = 200;
    int size = NumberUtils.toInt(sizeStr, defaultSize);
    return fileResponseFascade.qrShow(encryptUrl, size);
  }


  private Response.ResponseBuilder processFileShareActionViewPic(ContainerRequestContext context) {
    String path = getQueryParam("NPath", "Path");
    String ea = null;
    if (path.startsWith("F_")) {
      try {
        ea = path.split("_")[1];
      } catch (Throwable e) {
        return Response.status(Response.Status.BAD_REQUEST);
      }
    }
    return fileResponseFascade.getByPath(FileDownloadQueryVo.builder().ea(ea).path(path).build());
  }

  private Response.ResponseBuilder processFileShareActionViewImage(ContainerRequestContext context) {
    String fileToken = getQueryParam("FileToken");
    String filetype = getQueryParam("filetype");
    String ua = context.getHeaderString("User-Agent");
    Response.ResponseBuilder builder = fileResponseFascade.shareFileDownloadByToken(fileToken, filetype, ua);
    builder.header(HttpHeaders.CONTENT_TYPE, "image/jpeg");
    return builder;
  }

  private Response.ResponseBuilder processFileShareActionDownload(ContainerRequestContext context) {
    String fileToken = getQueryParam("FileToken");
    String filetype = getQueryParam("filetype");
    String ua = context.getHeaderString("User-Agent");
    return fileResponseFascade.shareFileDownloadByToken(fileToken, filetype, ua);
  }

  private Response.ResponseBuilder processFileShareActionDownloadByPath(ContainerRequestContext context) {
    String path = getQueryParam("NPath", "Path");
    String name = getQueryParam("Name");
    if (Strings.isNullOrEmpty(path) || !path.startsWith("G_") || Strings.isNullOrEmpty(name)) {
      return Response.status(Response.Status.BAD_REQUEST);
    }
    return fileResponseFascade.downloadByPath(FileDownloadQueryVo.builder().path(path).build(), name);
  }

}
