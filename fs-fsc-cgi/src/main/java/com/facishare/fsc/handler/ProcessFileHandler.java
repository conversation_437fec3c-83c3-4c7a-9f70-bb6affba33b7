package com.facishare.fsc.handler;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.common.document.BatchDownloadDocumentUtils;
import com.facishare.fsc.common.document.BatchFileEntity;
import com.facishare.fsc.common.utils.BrowserUtils;
import com.facishare.fsc.common.utils.FileHelper;
import com.facishare.fsc.common.utils.Guid;
import com.facishare.fsc.common.utils.ImageUtils;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.common.utils.ParameterUtils;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.FilePathFilter;
import com.facishare.fsc.core.exceptions.ExceptionConstant;
import com.facishare.fsc.core.exceptions.FSCException;
import com.facishare.fsc.core.exceptions.FSCRemoteException;
import com.facishare.fsc.core.fascade.impl.FileResponseFascadeImpl;
import com.facishare.fsc.core.model.ChunkFileUploadDataResult;
import com.facishare.fsc.core.model.RangeDownloadResult;
import com.facishare.fsc.core.model.vo.FileDownloadQueryVo;
import com.facishare.fsc.core.remote.DownloadSpeedLimiter;
import com.facishare.fsc.core.repository.FileRepository;
import com.facishare.fsc.core.storage.impl.DownloadFileTokenDaoImpl;
import com.facishare.fsc.core.storage.model.DownloadFileToken;
import com.facishare.fsc.domain.BaseException;
import com.facishare.fsc.domain.FileSystemResourceEnum;
import com.facishare.fsc.domain.Result;
import com.facishare.fsc.model.RangeUnit;
import com.facishare.fsc.stone.StoneApiService;
import com.facishare.fsc.utils.FileTypeCheckHelp;
import com.facishare.fsc.utils.FilenameUtil;
import com.facishare.fsi.proxy.FsiWarehouseProxyFactory;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileGetFileMetaResponse;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fs.fmcg.sdk.ai.DetectClient;
import com.fs.fmcg.sdk.ai.contract.Detect;
import com.fs.fmcg.sdk.ai.contract.SdkContext;
import com.fxiaoke.common.SizeFormatter;
import com.fxiaoke.k8s.support.util.SystemUtils;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.helper.ConfigEiHelper;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

/**
 * Created by Aaron on 16/5/14.
 */
@Component
@Slf4j(topic = "ProcessFileHandler")
public class ProcessFileHandler extends AbstractProcessHandler implements ProcessHandler {

  @Resource
  private FileResponseFascadeImpl fileResponseFascade;
  @Resource
  private DownloadFileTokenDaoImpl downloadFileTokenDao;
  @Resource
  private FileRepository fileRepository;
  @Resource
  private FilePathFilter filePathFilter;
  @Resource
  private DownloadSpeedLimiter speedLimiter;
  @Resource
  private DetectClient detectClient;
  @Resource
  private FsiWarehouseProxyFactory fsiWarehouseProxyFactory;
  @Resource
  StoneApiService stoneApiService;
  private List<String> feedSupportExt = new ArrayList<>();
  private boolean filterUserEnv;
  private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("fsc");
  private HashSet<String> ignoreRetryPathSet;
  private static final String EXTENSION = "extension";
  private static final String BUSINESS = "business";

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-fsc-cgi-config", config -> {
      filterUserEnv = config.getBool("filter.user.env", false);
      log.info("是否开启用户环境过滤:{}", filterUserEnv);
      feedSupportExt = Splitter.on(",").splitToList(config.get("feed.support.ext"));
      log.info("Feed 业务允许缩略图片类型:{}", feedSupportExt);
      String ignoreRetryPath = config.get("ignore.retry.path");
      if (!Strings.isNullOrEmpty(ignoreRetryPath)) {
        List<String> ignorePathList = Splitter.on("|").trimResults().omitEmptyStrings()
            .splitToList(ignoreRetryPath);
        ignoreRetryPathSet= new HashSet<>(ignorePathList);
      }
    });
  }


  @Override
  public Response process(String area, String servlet, String action, AuthInfo authInfo) {
    ContainerRequestContext context = FSCContext.getCurrentRequestContext();
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    switch (action){
      case "ChunkFileUploadStart":
        return chunkFileUploadStart(builder, context, authInfo).build();
      case "ChunkFileUploadDataByStream":
        return chunkFileUploadDataByStream(builder, context, authInfo).build();
      case "ChunkFileUploadDataByForm":
        return chunkFileUploadDataByForm(builder, context, authInfo).build();
      case "ChunkFileUploadDataByBase64":
        return chunkFileUploadDataByBase64(builder, context, authInfo).build();
      case "ChunkFileUploadComplete":
        return chunkFileUploadComplete(builder, context, authInfo).build();
      case "UploadByStream":
        return uploadByStream(builder, context, authInfo).build();
      case "UploadByForm":
      case "UploadForOpen":
        return uploadByForm(builder, context, authInfo).build();
      case "UploadByBase64":
        return uploadByBase64(builder, context, authInfo).build();
      case "UploadForKuaiXiao":
        return uploadForKuaiXiao(builder, context, authInfo).build();
      case "ViewTempImg":
        return ViewTempImg(builder, context, authInfo).build();
      case "DownloadTempFile":
        return DownloadTempFile(builder, context, authInfo).build();
      case "BatchDownloadByStream":
        return BatchDownloadByStream(builder, context, authInfo).build();
      case "DownloadByToken":
        return DownloadByToken(builder, context, authInfo).build();
      case "DocPreviewByToken":
        return DocPreviewByToken(builder, context, authInfo).build();
      case "DocPageByToken":
        return DocPageByToken(builder, context, authInfo).build();
      case "DocPageByPath":
        return DocPageByPath(builder, context, authInfo).build();
      case "DocPreviewByPath":
        return DocPreviewByPath(builder, context, authInfo).build();
      case "GetByToken":
        return GetByToken(builder, context, authInfo).build();
      case "DownloadGlobal":
      case "DownloadByPath":
      case "DownloadByPathForOpen":
        return DownloadByPath(builder, context, authInfo).build();
      case "RangeDownloadByStream":
        return RangeDownloadByStream(builder, context, authInfo).build();
      case "DownloadByRangeStream":
        return DownloadByRangeStream(builder, context, authInfo).build();
      case "GetByPath":
      case "GetByPathForOpen":
        return GetByPath(builder, context, authInfo).build();
      case "GetMp3ByPath":
        return GetMp3ByPath(builder, context, authInfo).build();
      case "DownloadFileByBatch":
        return ProcessDownloadFileByBatch(builder, context, authInfo).build();
      case "CheckFileExist":
        return ProcessCheckFileExist(builder, context, authInfo).build();
      default:
        return builder.status(Response.Status.NOT_FOUND).build();
    }
  }

  private Response.ResponseBuilder DownloadByRangeStream(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    String fileName = getQueryParam("name");
    String path = getQueryParam("path");
    String token = getQueryParam("FileToken");

    if (Strings.isNullOrEmpty(path) && Strings.isNullOrEmpty(token)) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String business = "FSC"; // Default business
    String securityGroup = "";

    if (!Strings.isNullOrEmpty(token)) {
      DownloadFileToken fileToken = downloadFileTokenDao.find(authInfo.getEnterpriseAccount(), token,authInfo.getTicketUserId());
      path = fileToken.getFilePath();
      securityGroup = fileToken.getDownloadSecurityGroup();
      fileName = fileToken.getFileName();
      business = "NetDisk";
    } else if (!Strings.isNullOrEmpty(fileName)) {
      fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8);
    }

    if (!filePathFilter.isNCFile(path)) {
      log.warn("DownloadByRangeStream:file path illegal,path={},auth:{},ua:{}", path,authInfo,context.getHeaderString("User-Agent"));
      return builder.status(Response.Status.BAD_REQUEST);
    }

    try {
      Optional<StoneFileGetFileMetaResponse> fileMetaOptions = stoneApiService.getFileMeta(
          authInfo.getEnterpriseAccount(), authInfo.getEmployeeId(), business, path, securityGroup);
      if (fileMetaOptions.isEmpty() || fileMetaOptions.get().getSize() <= 0) {
        return builder.status(Response.Status.BAD_REQUEST);
      }
      StoneFileGetFileMetaResponse fileMeta= fileMetaOptions.get();
      if (Strings.isNullOrEmpty(fileName)) {
        fileName = fileMeta.getOriginName();
      }
      if (Strings.isNullOrEmpty(fileName)) {
        fileName = FilenameUtils.getName(path);
      }
      return Response.status(Response.Status.OK)
          .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM)
          .header(HttpHeaders.CONTENT_LENGTH, fileMeta.getSize())
          .header("Entity-Length",fileMeta.getSize())
          .header(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), fileName))
          .entity(stoneApiService.downloadByRangeStream(authInfo.getEnterpriseAccount(), authInfo.getEmployeeId(), business, path, securityGroup, fileMeta.getSize()));
    } catch (FSCException e) {
      if (e.code==400){
        return builder.status(Response.Status.BAD_REQUEST);
      }
      return builder.status(Response.Status.INTERNAL_SERVER_ERROR);
    }
  }

  private Response.ResponseBuilder RangeDownloadByStream(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    String path = getQueryParam("NPath", "Path");
    String securityGroup = "";
    String ea = authInfo.getEnterpriseAccount();
    String sourceUser = authInfo.getTicketUserId();
    if (Strings.isNullOrEmpty(path)) {
      String token = getQueryParam("FileToken");
      if (Strings.isNullOrEmpty(token)) {
        return builder.status(Response.Status.BAD_REQUEST);
      }
      DownloadFileToken fileToken = downloadFileTokenDao.find(ea, token, sourceUser);
      if (fileToken == null) {
        return builder.status(Response.Status.BAD_REQUEST);
      }

      path = fileToken.getFilePath();
      securityGroup = fileToken.getDownloadSecurityGroup();
    }

    if (!filePathFilter.isValid(path)) {
      return builder.status(Response.Status.BAD_REQUEST);
    }

    String range = getRequestHeader("Range");
    boolean noRange = Strings.isNullOrEmpty(range);
    range = Objects.toString(range, "bytes=0-");
    int unitIndex = range.indexOf('=');
    if (unitIndex == -1) {
      log.warn("downloadByRange Param:{},{},{},range fmt error", ea, path, range);
      return builder.status(Response.Status.REQUESTED_RANGE_NOT_SATISFIABLE);
    }
    String unit = range.substring(0, unitIndex);
    if (!EnumUtils.isValidEnum(RangeUnit.class, unit.toUpperCase())) {
      log.warn("downloadByRange Param:{},{},{},unit unValid", ea, path, range);
      return builder.status(Response.Status.REQUESTED_RANGE_NOT_SATISFIABLE);
    }
    String rangeList = range.substring(unitIndex + 1);
    if (rangeList.indexOf(',') != -1) {
      log.warn("downloadByRange Param:{},{},{},not support multipart range", ea, path, range);
      return builder.status(Response.Status.REQUESTED_RANGE_NOT_SATISFIABLE);
    }
    try {
      int splitIndex = rangeList.indexOf('-');
      int start = Integer.parseInt(rangeList.substring(0, splitIndex));
      int end = splitIndex == rangeList.length() - 1 ? 0 : Integer.parseInt(rangeList.substring(splitIndex + 1)) + 1;
      RangeDownloadResult result = fileResponseFascade.downloadByRange(FileDownloadQueryVo.builder()
        .ea(ea)
        .sourceUser(sourceUser)
        .path(path)
        .ua(context.getHeaderString("User-Agent"))
        .byteStartIndex(start)
        .byteEndIndex(end)
        .securityGroup(securityGroup)
        .build());

      byte[] data = result.getRangeData();
      InputStream stream = speedLimiter.wrapWithSpeedLimit(data, ea, sourceUser, path);
      return Response.status(noRange ? Status.OK : Status.PARTIAL_CONTENT)
        .header("Content-Range", "bytes " + result.getStart() + '-' + (result.getEnd() - 1) + '/' + result.getTotalSize())
        .header("Accept-Ranges", "bytes")
        .header(HttpHeaders.CONTENT_LENGTH, data.length)
        .header(HttpHeaders.CONTENT_TYPE, FileHelper.getMime(FilenameUtils.getExtension(path)))
        .header(HttpHeaders.CACHE_CONTROL, "private, max-age=120")
        .entity(stream);
    } catch (Exception e) {
      log.error("downloadByRange Exception:{},{},{}", ea, path, range, e);
      return Response.status(Status.NOT_FOUND);
    }

  }

  private Response.ResponseBuilder BatchDownloadByStream(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    String key = getQueryParam("token");
    String name = getQueryParam("name");
    String url = new String(Base64.getUrlDecoder().decode(key));
    if (Strings.isNullOrEmpty(key)) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    if (!Strings.isNullOrEmpty(name)) {
      name = URLDecoder.decode(name, StandardCharsets.UTF_8);
    } else {
      name = FilenameUtil.generateFileName("BatchDownload","zip");
    }
    return fileResponseFascade.batchDownloadByDocumentByStream(builder, url, name, authInfo.getEnterpriseAccount());
  }

  @Resource
  FileTypeCheckHelp fileTypeCheckHelp;

  private boolean userNotCurrentCloud(AuthInfo authInfo){
    int enterpriseId = authInfo.getEnterpriseId();
    if (enterpriseId!=0&&!ConfigEiHelper.getInstance().isCurrentCloud(String.valueOf(enterpriseId))){
      log.warn("cross-cloud uploads ,ea={},ei={}",authInfo.getEnterpriseAccount(),enterpriseId);
      return true;
    }
    return false;
  }

  private Response.ResponseBuilder uploadByStream(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    // 控制是否开启用户环境过滤 过滤跨云上传
    if (filterUserEnv && userNotCurrentCloud(authInfo)) {
      return builder.status(Response.Status.FORBIDDEN)
          .entity("Cross-cloud uploads by users not from this environment are prohibited");
    }
    // 文件大小获取 todo:将来可考虑限制大小
    int contentLength = getContentLengthAndStatistics(context.getLength(), authInfo).orElse(-1);
    if (contentLength == -1) {
      return builder.status(Response.Status.BAD_REQUEST).entity("ContentLength param not exist");
    }

    FileSystemResourceEnum resourceType = getResourceType();
    StoneFileUploadRequest stoneFileUploadRequest = initStoneFileUploadRequest(authInfo, resourceType,contentLength);
    // 包装文件流使其在一定缓冲区内可重复读取,用于检测文件类型
    try(BufferedInputStream  stream = new BufferedInputStream(context.getEntityStream())){

      // 控制是否开启文件类型检查
      if (gray.isAllow("fileTypeCheckV2", authInfo.getEnterpriseAccount())){
        fileTypeCheckHelp.check(stream, authInfo.getEnterpriseAccount(),
            contentLength, stoneFileUploadRequest.getExtensionName());
      }

      log.info("uploadByStream:file upload start,arg={},authInfo={}", stoneFileUploadRequest, authInfo);
      StoneFileUploadResponse result = stoneApiService.uploadByStream(resourceType,stoneFileUploadRequest, stream);
      log.info("uploadByStream:file upload success,arg={},authInfo={}", stoneFileUploadRequest.getExtensionName(), authInfo);
      return generateUploadResults(result);
    }catch (BaseException e){
      log.warn("uploadByStream:file upload check fail,extension={},authInfo={}", stoneFileUploadRequest.getExtensionName(), authInfo);
      return builder.status(Response.Status.BAD_REQUEST).type(MediaType.APPLICATION_JSON).entity(Result.error(e.getCode(),e.getMessage(),e.getCode()));
    } catch (IOException e) {
      log.warn("uploadByStream:file field empty or extension not found");
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
    } catch (FRestClientException e) {
      if (Objects.toString(e.getMessage(), "").contains("s312070021")) {
        log.warn("UploadByStream upload fail,extension={},authInfo={}", stoneFileUploadRequest.getExtensionName(), authInfo, e);
        return builder.status(Response.Status.BAD_REQUEST).entity("Invalid extension");
      }
      log.error("UploadByStream upload fail,args={},authInfo={}", stoneFileUploadRequest, authInfo, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  private FileSystemResourceEnum getResourceType() {
    Optional<String> resourceTypeOption = getFirstExistParam("Resource-Type");
    if (resourceTypeOption.isEmpty()) {
      String needCdn = getFirstExistQueryParam("needCdn").orElse("false");
      if (Boolean.TRUE.toString().equalsIgnoreCase(needCdn)) {
        return FileSystemResourceEnum.TC;
      }
      return FileSystemResourceEnum.TN;
    }
    return FileSystemResourceEnum.of(resourceTypeOption.get());
  }

  private Optional<Integer> getContentLengthAndStatistics(int contentLength,AuthInfo authInfo) {
    if (contentLength == -1) {
      contentLength =ParameterUtils.getTotalLength(getRequestHeader("totallength")) ;
      log.info("uploadByStream, totalLength: {}", contentLength);
    }
    if (contentLength == -1) {
      log.warn("uploadByStream, contentLength not exist");
      return Optional.empty();
    }
    if (contentLength > 1024 * 1024 * 10) {
      log.warn("TooLargeFile, uploadByStream,AuthInfo={},contentLength={}",authInfo,SizeFormatter.format(contentLength));
    }
    return Optional.of(contentLength);
  }

  private StoneFileUploadRequest initStoneFileUploadRequest(AuthInfo authInfo,
      FileSystemResourceEnum resourceEnum, int contentLength) {
    String extension = getExtension();
    String originFileName = getQueryParam("originFileName");
    if (!Strings.isNullOrEmpty(originFileName)) {
      originFileName = URLDecoder.decode(originFileName, StandardCharsets.UTF_8);
    }
    return initStoneFileUploadRequest(authInfo, resourceEnum, contentLength, extension,originFileName);
  }

  private StoneFileUploadRequest initStoneFileUploadRequest(AuthInfo authInfo,
      FileSystemResourceEnum resourceEnum, int contentLength, String extension,String filename) {
    String business = getQueryParam(BUSINESS);
    String needThumbnailStr = getQueryParam("needThumbnail");
    String words = getQueryParam("words");
    boolean needThumbnail = !Strings.isNullOrEmpty(needThumbnailStr) && ("1".equalsIgnoreCase(needThumbnailStr) || Boolean.parseBoolean(needThumbnailStr));
    String ea = authInfo.getEnterpriseAccount();
    int employeeId = ParameterUtils.getEmployeeId(authInfo.getEmployeeId());
    StoneFileUploadRequest stoneFileUploadRequest = new StoneFileUploadRequest();
    stoneFileUploadRequest.setEa(ea);
    stoneFileUploadRequest.setEmployeeId(employeeId);
    stoneFileUploadRequest.setOriginName(filename);
    stoneFileUploadRequest.setNeedThumbnail(needThumbnail);

    stoneFileUploadRequest.setBusiness(business);
    stoneFileUploadRequest.setExtensionName(extension);
    // 判断是否需要CDN加速,上传为C类型文件
    if (resourceEnum==FileSystemResourceEnum.TC||resourceEnum==FileSystemResourceEnum.C) {
      stoneFileUploadRequest.setNeedCdn(true);
    }
    // 判断是否是临时文件,需要设置过期时间
    if (resourceEnum==FileSystemResourceEnum.TN||resourceEnum==FileSystemResourceEnum.TC) {
      int expireDay = ParameterUtils.getExpireDay(getFirstExistQueryParam("expireDay").orElse("3"));
      stoneFileUploadRequest.setExpireDay(expireDay);
    }
    stoneFileUploadRequest.setFileSize(contentLength);
    if (!Strings.isNullOrEmpty(words)) {
       stoneFileUploadRequest.setWords(StringUtils.split(words, ","));
    }
    return stoneFileUploadRequest;
  }

  private Response.ResponseBuilder generateUploadResults(StoneFileUploadResponse result) {
    Map<String, String> map = Maps.newHashMap();
    map.put("TempFileName", result.getPath());
    map.put("FileExtension", result.getExtensionName());
    return Response.status(Response.Status.OK).entity(JsonUtils.toJson(map)).header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
  }

  private Response.ResponseBuilder chunkFileUploadDataByForm(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    int chunkIndex = -1;
    byte[] data = null;
    String path = null;
    String needCheck = null;
    String md5 = null;
    DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
    ServletFileUpload upload = new ServletFileUpload(diskFileItemFactory);
    try {
      List<FileItem> items = upload.parseRequest((HttpServletRequest) FSCContext.getRequest());
      for (FileItem item : items) {
        if (item.isFormField()) {
          switch (item.getFieldName()) {
            case "ChunkIndex":
              chunkIndex = Integer.parseInt(item.getString());
              break;
            case "Path":
              path = item.getString();
              break;
            case "NeedCheck":
              needCheck = item.getString();
              break;
            case "Md5":
              md5 = item.getString();
              break;
          }
          continue;
        }

        data = item.get();
      }
      if (data == null || Strings.isNullOrEmpty(path) || chunkIndex == -1) {
        log.error( "chunkFileUploadDataByForm:file field not found please check Path");
        return builder.status(Response.Status.BAD_REQUEST).entity("file field not exist please check Path");
      }

      if (!Strings.isNullOrEmpty(needCheck) && "1".equals(needCheck) && !Strings.isNullOrEmpty(md5)) {
        String serverMd5 = FileHelper.getMD5(data);
        if (!serverMd5.equals(md5)) {
          log.error("md5 check fail:requestMd5:{},serverMd5:{}", md5, serverMd5);
          throw new RuntimeException("param illegal:md5 check fail");
        }
      }

    } catch (Exception e) {
      log.error( "chunkFileUploadDataByForm:file field deal error", e);
      return builder.status(Status.BAD_REQUEST).entity(e.getMessage());
    }

    return fileResponseFascade.chunkFileUploadData(authInfo.getEnterpriseAccount(), path, chunkIndex, data);
  }

  private Response.ResponseBuilder chunkFileUploadDataByStream(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST")) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String path = getRequestHeader("Path");
    int chunkIndex = Integer.parseInt(getRequestHeader("ChunkIndex"));
    if (Strings.isNullOrEmpty(path)||chunkIndex<=0) {
      builder.status(Response.Status.BAD_REQUEST).entity("param illegal,please check field");
    }
    String business =
        Strings.isNullOrEmpty(getRequestHeader(BUSINESS)) ? "FSC" : getRequestHeader(BUSINESS);
    int employeeId = ParameterUtils.getEmployeeId(authInfo.getEmployeeId());
    String md5Digest=Strings.isNullOrEmpty(getRequestHeader("ETag"))?"":getRequestHeader("ETag");
    boolean uploadResult = stoneApiService.chunkUploadDataByStream(authInfo.getEnterpriseAccount(), employeeId, business, path, chunkIndex, md5Digest, context.getEntityStream());
    ChunkFileUploadDataResult result = new ChunkFileUploadDataResult();
    result.setSuccess(uploadResult);
    return builder.status(Response.Status.OK).entity(JsonUtils.toJson(result)).header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
  }

  private Response.ResponseBuilder chunkFileUploadDataByBase64(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    byte[] data = null;
    Map<String, String> requestMap = null;
    String jsongContent = null;
    String path = null;
    int chunkIndex = -1;
    try {
      jsongContent = new String(IOUtils.toByteArray(context.getEntityStream()), StandardCharsets.UTF_8);
      requestMap = JsonUtils.fromJson(jsongContent, HashedMap.class);
      String content = requestMap.get("Content");
      data = ImageUtils.fromBase64ToBytes(content);
      path = requestMap.get("Path");
      chunkIndex = Integer.parseInt(requestMap.get("ChunkIndex"));

      String needCheck = requestMap.get("NeedCheck");
      String md5 = requestMap.get("Md5");
      if (!Strings.isNullOrEmpty(needCheck) && "1".equals(needCheck) && !Strings.isNullOrEmpty(md5)) {
        String serverMd5 = FileHelper.getMD5(data);
        if (!serverMd5.equals(md5)) {
          log.error("md5 check fail:requestMd5:{},serverMd5:{}", md5, serverMd5);
          throw new RuntimeException("param illegal:md5 check fail");
        }
      }
    } catch (Exception e) {
      log.error( "chunkFileUploadDataByBase64:Content:{},ChunkIndex:{},Path:{}", jsongContent, chunkIndex, path, e);
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
    }
    if (data == null || Strings.isNullOrEmpty(path)) {
      log.error( "chunkFileUploadDataByBase64:file field not found or Path not found");
      return builder.status(Response.Status.BAD_REQUEST).entity("file field not exist or Path not found");
    }
    return fileResponseFascade.chunkFileUploadData(authInfo.getEnterpriseAccount(), path, chunkIndex, data);
  }

  private Response.ResponseBuilder chunkFileUploadComplete(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String path;
    String business;
    try {
      Map<String, String> formParams = getFormParams();
      path = formParams.getOrDefault("Path".toLowerCase(), "");
      business = formParams.getOrDefault(BUSINESS.toLowerCase(), "");
      if (Strings.isNullOrEmpty(path)) {
        throw new RuntimeException("param illegal:(Path)");
      }
    } catch (Exception e) {
      log.error( "chunkFileUploadComplete", e);
      builder.status(Response.Status.BAD_REQUEST).entity("please check field is exist:(Path)");
      return builder;
    }
    return fileResponseFascade.chunkFileUploadComplete(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), path, business);
  }

  private Response.ResponseBuilder chunkFileUploadStart(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String extension;
    int chunkCount;
    int chunkSize;
    int lastChunkSize;
    String code;
    String business = getQueryParam(BUSINESS);
    String needThumbnailStr = getQueryParam("needThumbnail");
    String hashRule;
    boolean needThumbnail;
    try {
      Map<String, String> formParams = getFormParams();
      extension = formParams.getOrDefault(EXTENSION.toLowerCase(), "");
      if (extension == null) {
        extension = "";
      }
      hashRule = formParams.get("HashRule".toLowerCase());
      chunkCount = Integer.parseInt(formParams.getOrDefault("ChunkCount".toLowerCase(), ""));
      chunkSize = Integer.parseInt(formParams.getOrDefault("ChunkSize".toLowerCase(), ""));
      lastChunkSize = Integer.parseInt(formParams.getOrDefault("LastChunkSize".toLowerCase(), ""));
      business =
          Strings.isNullOrEmpty(business) ? formParams.getOrDefault(BUSINESS.toLowerCase(), "FSC")
              : business;
      code = formParams.getOrDefault("Code".toLowerCase(), "");
      needThumbnailStr = Strings.isNullOrEmpty(needThumbnailStr) ? formParams.getOrDefault("NeedThumbnail".toLowerCase(), "false") : needThumbnailStr;
      needThumbnail = Boolean.parseBoolean(needThumbnailStr);
      if (Strings.isNullOrEmpty(code) || chunkCount == 0 || chunkSize == 0) {
        log.warn( "chunkFileUploadStart:param illegal:(Code,ChunkCount,ChunkSize)");
        return builder.status(Response.Status.BAD_REQUEST).entity("param illegal:(Code,ChunkCount,ChunkSize)");
      }
    } catch (Exception e) {
      log.error( "chunkFileUploadStart", e);
      builder.status(Response.Status.BAD_REQUEST).entity("please check field is exist:(Extension,ChunkCount,ChunkSize,LastChunkSize,Code)");
      return builder;
    }

    return fileResponseFascade.chunkFileUploadStart(authInfo.getEnterpriseAccount(),
      authInfo.getTicketUserId(),
      code,
      extension,
      chunkCount,
      chunkSize,
      lastChunkSize,
      business,
      needThumbnail,
      hashRule);
  }

  private boolean isCheck(AuthInfo authInfo){
    return gray.isAllow("uploadByFormFileTypeCheckV2",authInfo.getEnterpriseAccount())&&gray.isAllow("fileTypeCheckV2",authInfo.getEnterpriseAccount());
  }

  private Response.ResponseBuilder uploadByForm(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }

    // 控制是否开启用户环境过滤 过滤跨云上传
    if (filterUserEnv && userNotCurrentCloud(authInfo)) {
      return builder.status(Response.Status.FORBIDDEN)
          .entity("Cross-cloud uploads by users not from this environment are prohibited");
    }

    String filename=null;
    String extension = getExtension();
    byte[] fileData = null;
    int contentLength = 0;

    // 获取当前请求
    HttpServletRequest request = (HttpServletRequest) FSCContext.getRequest();
    // 判断是否是文件上传请求
    if (!ServletFileUpload.isMultipartContent(request)) {
      return builder.status(Status.BAD_REQUEST)
          .entity("file field not exist or extension not found");
    }

    // 创建一个工厂用于存储文件
    DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
    ServletFileUpload upload = new ServletFileUpload(diskFileItemFactory);

    try {
      List<FileItem> items = upload.parseRequest(request);
      // 遍历表单中的所有字段获取扩展名与文件数据
      for (FileItem item : items) {
        // 如果未传扩展名,从表单中获取扩展名
        if (item.isFormField()) {
          if (Strings.isNullOrEmpty(extension) && EXTENSION.equals(item.getFieldName())) {
            extension = item.getString();
          }
        } else {
          if (Strings.isNullOrEmpty(extension)) {
            filename = item.getName();
            if (!Strings.isNullOrEmpty(filename)) {
              extension = FilenameUtils.getExtension(filename);
            }
          }
          // 获取文件数据(遵循原有设计不优化为流式读取)
          fileData = item.get();
          // 获取文件大小(存在类型强转,最大支持2G)
          contentLength = (int) item.getSize();
        }
      }

      // 检查文件大小是否为空
      if (contentLength == 0 || fileData == null || fileData.length == 0) {
        log.warn("UploadByForm:file field empty or extension not found");
        return builder.status(Status.BAD_REQUEST)
            .entity("file field not exist or extension not found");
      }

    } catch (FileUploadException e) {
      log.warn("UploadByForm: file data read fail,extension={},authInfo={}", extension,authInfo, e);
      return builder.status(Status.BAD_REQUEST).entity("file upload fail");
    }

    FileSystemResourceEnum resourceType = getResourceType();
    StoneFileUploadRequest stoneFileUploadRequest = initStoneFileUploadRequest(authInfo, resourceType, contentLength, extension,filename);
    try (ByteArrayInputStream stream = new ByteArrayInputStream(fileData)) {
      // 控制是否开启文件类型检查(为表单上传的文件类型上传额外添加灰度控制项)
      if (isCheck(authInfo)) {
        fileTypeCheckHelp.check(stream, authInfo.getEnterpriseAccount(), contentLength, stoneFileUploadRequest.getExtensionName());
      }
      log.info("uploadByForm:file upload start,arg={},authInfo={}", stoneFileUploadRequest, authInfo);
      StoneFileUploadResponse result = stoneApiService.uploadByStream(resourceType, stoneFileUploadRequest, stream);
      log.info("uploadByForm:file upload success,arg={},authInfo={}", stoneFileUploadRequest.getExtensionName(), authInfo);
      return generateUploadResults(result);
    } catch (BaseException e) {
      log.warn("uploadByForm:file upload check fail,arg={},authInfo={}", stoneFileUploadRequest, authInfo);
      return builder.status(Status.BAD_REQUEST).type(MediaType.APPLICATION_JSON).entity(Result.error(e.getCode(), e.getMessage(), e.getCode()));
    } catch (IOException e) {
      log.warn("uploadByForm:file data read/write fail,arg={},authInfo={}", stoneFileUploadRequest, authInfo, e);
      return builder.status(Status.BAD_REQUEST).entity(e.getMessage());
    } catch (FRestClientException e) {
      if (Objects.toString(e.getMessage(), "").contains("s312070021")) {
        log.warn("uploadByForm upload fail,arg={},authInfo={}", stoneFileUploadRequest, authInfo, e);
        return builder.status(Status.BAD_REQUEST).entity("Invalid extension");
      }
      log.error("uploadByForm upload fail,args={},authInfo={}", stoneFileUploadRequest, authInfo, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  private String getExtension() {
    String extension=getQueryParam(EXTENSION);
    if (Strings.isNullOrEmpty(extension)) {
      extension = getRequestHeader(EXTENSION);
    }
    return Strings.isNullOrEmpty(extension) ? "" : extension;
  }

  private static final String KUAIXIAO_BUSINESS = "KuaiXiao";
  private static final String KUAIXIAO_AI_REC_APP_NAME = "FSC";

  private Response.ResponseBuilder uploadForKuaiXiao(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || context.getEntityStream() == null) {
      return builder.status(Response.Status.BAD_REQUEST);
    }

    // 快消AI识图相关参数
    String modelId = getQueryParam("modelId");
    String extraData = Objects.toString(getQueryParam("extraData"), "{}");
    String scene = getQueryParam("scene");
    // 文件上传相关参数
    String path = getQueryParam("path");
    String extension = getQueryParam(EXTENSION);
    if (extension==null){
      extension = "";
    }
    log.info("uploadForKuaiXiao:modelId:{},extraData:{},scene:{},path:{},extension:{}", modelId, extraData, scene, path, extension);
    // 待识别的图片数据
    byte[] fileData;

    if (!Strings.isNullOrEmpty(path)) {
      // 忽略因为事故或者其他原因导致无法重试成功的文件path,避免频繁报错
      if (ignoreRetryPath(path)) {
        log.warn("uploadForKuaiXiao:ignoreRetryPathList contains path,modelId:{},extraData:{},scene:{},preGenPath:{},extension:{}", modelId, extraData, scene, path, extension);
        fileData=fileRepository.getByPath(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), path, null, extension, null);
        if (fileData==null||fileData.length==0) {
          fileData= getIgnoreVirtualImg();
          log.warn("uploadForKuaiXiao:ignoreRetryPathList contains path,modelId:{},extraData:{},scene:{},preGenPath:{},extension:{},use virtual img", modelId, extraData, scene, path, extension);
        }
      }else {
        fileData = fileRepository.getByPath(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), path, null, extension, null);
      }
      if (fileData==null||fileData.length==0){
        log.error("uploadForKuaiXiao:file path download fail,modelId:{},extraData:{},scene:{},preGenPath:{},extension:{}", modelId, extraData, scene, path, extension);
        return builder.status(Response.Status.BAD_REQUEST).entity("file download fail,please check path");
      }
      return imgRecognitionByBaidu(authInfo,modelId,extraData,scene,fileData,path,extension);
    }

    try {
      fileData = IOUtils.toByteArray(context.getEntityStream());
      String uploadResultPath = kuaiXiaoFileUpload(authInfo, fileData, extension);
      return imgRecognitionByBaidu(authInfo, modelId, extraData, scene, fileData, uploadResultPath, extension);
    } catch (IOException ioException) {
      log.warn("uploadForKuaiXiao:file field is empty,modelId:{},extraData:{},scene:{},preGenPath:{},extension:{}",
          modelId, extraData, scene, path, extension);
      return builder.status(Response.Status.BAD_REQUEST).entity("file data is empty");
    } catch (FRestClientException e) {
      log.error("uploadForKuaiXiao:file upload fail,modelId:{},extraData:{},scene:{},preGenPath:{},extension:{}",
          modelId, extraData, scene, path, extension, e);
      return builder.status(Response.Status.BAD_REQUEST).entity("file upload fail");
    }
  }

  private boolean ignoreRetryPath(String path){
    if (path!=null&&!path.isEmpty()&&ignoreRetryPathSet != null){
     return ignoreRetryPathSet.stream()
         .anyMatch(path::startsWith);
    }
    return false;
  }

  private static byte[] getIgnoreVirtualImg(){
    try(InputStream stream=new ClassPathResource("BeeIcon.png").getInputStream()) {
      return stream.readAllBytes();
    } catch (IOException e) {
      log.error("getIgnoreVirtualImg error",e);
      return new byte[0];
    }
  }

  private Response.ResponseBuilder imgRecognitionByBaidu(AuthInfo authInfo,String modelId,String extraData,String scene,byte[] fileData,String path,String extension){
    SdkContext sdkContext = SdkContext.createInstance(authInfo.getEnterpriseId(), authInfo.getEnterpriseAccount(), authInfo.getEmployeeId(), KUAIXIAO_AI_REC_APP_NAME);
    Detect.Arg arg = new Detect.Arg();
    arg.setModelId(modelId);
    arg.setPath(path);
    arg.setImage(fileData);
    arg.setScene(scene);
    arg.setExtraData(extraData);
    Detect.Result dectResult = detectClient.detect(sdkContext, arg);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    Map<String, Object> map = Maps.newHashMap();
    map.put("npath", path);
    map.put("detectResult", dectResult);
    map.put("ext", extension);
    log.info("uploadForKuaiXiao:imgRecognitionByBaidu,modelId:{},path:{}", modelId,path);
    return responseBuilder.status(Response.Status.OK).entity(JsonUtils.toJson(map)).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
  }

  private String kuaiXiaoFileUpload(AuthInfo authInfo,byte[] bytes,String extension) throws FRestClientException, IOException {
    String enterpriseAccount = authInfo.getEnterpriseAccount();
    int employeeId = authInfo.getEmployeeId();
    if(gray.isAllow("UploadForKuaiXiaoSyncUpload",enterpriseAccount+"."+employeeId)){
      return syncUpload(enterpriseAccount, employeeId,bytes,extension);
    }
    // 异步上传,遵循原有逻辑,如果path为空,则生成一个随机的path
    return asyncUpload(enterpriseAccount, authInfo.getTicketUserId(),bytes,extension);
  }

  private String asyncUpload(String ea,String employeeId,byte[] fileData,String extension){
    String preGenPath = getNFileBaseName();
    log.info("UploadForKuaiXiao asyncUpload start,ea:{},employeeId:{},preGenPath:{},fileSize:{},extension:{}",ea,employeeId,preGenPath,fileData.length,extension);
    CompletableFuture.runAsync(() -> fileRepository.nUploadDirect(ea,employeeId, preGenPath, fileData, KUAIXIAO_BUSINESS, extension, false));
    log.info("UploadForKuaiXiao asyncUpload task submit end,ea:{},employeeId:{},preGenPath:{},fileSize:{},extension:{}",ea,employeeId,preGenPath,fileData.length,extension);
    return preGenPath;
  }

  private String syncUpload(String ea,int employeeId,byte[] fileData,String extension)
      throws FRestClientException, IOException {
    log.info("UploadForKuaiXiao syncUpload start,ea:{},employeeId:{},fileSize:{},extension:{}",ea,employeeId,fileData.length,extension);
    StoneFileUploadRequest stoneFileUploadRequest = new StoneFileUploadRequest();
    stoneFileUploadRequest.setEa(ea);
    stoneFileUploadRequest.setEmployeeId(employeeId);
    stoneFileUploadRequest.setBusiness(KUAIXIAO_BUSINESS);
    stoneFileUploadRequest.setExtensionName(extension);
    stoneFileUploadRequest.setFileSize(fileData.length);
    stoneFileUploadRequest.setNeedCdn(false);
    stoneFileUploadRequest.setNeedThumbnail(false);
    StoneFileUploadResponse result = stoneApiService.uploadForKuaiXiaoBySync(stoneFileUploadRequest, fileData);
    log.info("UploadForKuaiXiao syncUpload end,ea:{},employeeId:{},fileSize:{},extension:{},result:{}",ea,employeeId,fileData.length,extension,result);
    return result.getPath();
  }


  private String getNFileBaseName() {
    Date date = new Date();
    return String.format("N_%s_%s_%s", DateUtils.formatDate(date, "yyyyMM"), DateUtils.formatDate(date, "dd"), Guid.getGuid());
  }

  private Response.ResponseBuilder uploadByBase64(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.getRequest().getMethod().equalsIgnoreCase("POST") || !context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    byte[] data;
    Map<String, String> requestMap;
    String jsonContent = null;
    try {
      jsonContent = new String(IOUtils.toByteArray(context.getEntityStream()), StandardCharsets.UTF_8);
      requestMap = JsonUtils.fromJson(jsonContent, HashedMap.class);
      String content = requestMap.get("content");
      data = ImageUtils.fromBase64ToBytes(content);
    } catch (Exception e) {
      if (e instanceof IOException) {
        log.warn( "uploadByBase64:content:{}", jsonContent, e);
      } else {
        log.error( "uploadByBase64:content:{}", jsonContent, e);
      }
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
    }
    String extension = requestMap.get(EXTENSION);
    if (Strings.isNullOrEmpty(extension)) {
      log.error( "UploadByForm:extension not found");
      return builder.status(Response.Status.BAD_REQUEST).entity("extension not found");
    }
    if (data == null || data.length == 0) {
      log.warn( "uploadByBase64:file field empty");
      return builder.status(Response.Status.BAD_REQUEST).entity("file field not exist");
    }
    log.info("uploadByBase64:file upload start,auth:{},ext:{},size:{}", authInfo.getSourceUser(), extension, data.length);
    String path = fileResponseFascade.nTempFileUpload(authInfo.getEnterpriseAccount(),
        authInfo.getTicketUserId(),
        data,
        extension,
        null,
        false,
        null,
        false);
    Map<String, String> map = Maps.newHashMap();
    map.put("TempFileName", path);
    map.put("FileExtension", extension);
    log.info("uploadByBase64:file upload success,auth:{},ext:{},size:{},path:{}", authInfo.getSourceUser(), extension, data.length, path);
    return Response.status(Response.Status.OK).entity(JsonUtils.toJson(map)).header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
  }

  private Response.ResponseBuilder ViewTempImg(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    String tempFileName = getQueryParam("TempFileName");
    String ext = getQueryParam("TempFileExt");
    if (Strings.isNullOrEmpty(ext)) {
      if(tempFileName.contains(".")){
        ext = FilenameUtils.getExtension(tempFileName);
      } else{
        ext = "jpg";
      }
    }
    if (!tempFileName.contains("\\.")) {
      tempFileName = tempFileName + "." + ext;
    }
    //todo 兼容通讯录那面的错误请求，已通知他们改掉这里，后续可以下掉
    if (Strings.isNullOrEmpty(fsiWarehouseProxyFactory.getCloudType(authInfo.getEnterpriseId())) &&
      SystemUtils.getRuntimeEnv() == SystemUtils.RuntimeEnv.DEDICATED_CLOUD) {
      try {
        return builder.status(302).location(new URI("https://www.fxiaoke.com/FSC/EM/File/ViewTempImg?TempFileName=" + tempFileName));
      } catch (URISyntaxException e) {
        log.error("兼容错误请求失败，ea:{},path:{}", authInfo.getEnterpriseAccount(), tempFileName);
      }
    }
    return fileResponseFascade.getByPath(FileDownloadQueryVo.builder()
      .ea(authInfo.getEnterpriseAccount())
      .sourceUser(authInfo.getTicketUserId())
      .path(tempFileName)
      .build());
  }

  private Response.ResponseBuilder DownloadTempFile(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    String tempFileName = getQueryParam("TempFileName");
    if (!Strings.isNullOrEmpty(tempFileName) && tempFileName.contains(":")) {
      tempFileName = tempFileName.substring(tempFileName.indexOf(":") + 1);
    }
    String name = getQueryParam("Name");
    return fileResponseFascade.downloadByPath(FileDownloadQueryVo.builder()
      .ea(authInfo.getEnterpriseAccount())
      .sourceUser(authInfo.getTicketUserId())
      .path(tempFileName)
      .build(), name);
  }

  private Response.ResponseBuilder DownloadByToken(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    return fileResponseFascade.downloadByToken(authInfo.getEnterpriseAccount(),
      authInfo.getTicketUserId(),
      getQueryParam("FileToken"),
      getQueryParam("filetype"),
      context.getHeaderString("User-Agent"));
  }

  private Response.ResponseBuilder DocPreviewByToken(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    String fileToken = getQueryParam("FileToken");
    return fileResponseFascade.docPreviewByToken(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), fileToken);
  }

  private Response.ResponseBuilder DocPageByToken(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    String fileToken;
    int index;
    int width;
    int length;
    try {
      fileToken = getQueryParam("FileToken");
      index = Integer.parseInt(getQueryParam("PageIndex"));
      width = Integer.parseInt(getQueryParam("Width"));
      length = Integer.parseInt(getQueryParam("MaxContentLength"));
    } catch (Exception e) {
      log.error("param validate fail", e);
      return Response.status(Response.Status.BAD_REQUEST);
    }
    return fileResponseFascade.docPageByToken(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), fileToken, index, width, length);
  }

  private Response.ResponseBuilder DocPageByPath(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    String path;
    int index;
    int width;
    int length;
    try {
      path = getQueryParam("NPath", "Path");
      index = Integer.parseInt(getQueryParam("PageIndex"));
      width = Integer.parseInt(getQueryParam("Width"));
      length = Integer.parseInt(getQueryParam("MaxContentLength"));
    } catch (Exception e) {
      log.error("param validate fail", e);
      return Response.status(Response.Status.BAD_REQUEST);
    }

    if (path.contains("_")) {
      return fileResponseFascade.docPageByPath(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), path, "", index, width, length);
    } else {
      return fileResponseFascade.docPageByToken(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), path, index, width, length);
    }
  }

  private Response.ResponseBuilder DocPreviewByPath(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    return fileResponseFascade.docPreviewByPath(FileDownloadQueryVo.builder()
      .ea(authInfo.getEnterpriseAccount())
      .sourceUser(authInfo.getTicketUserId())
      .path(getQueryParam("NPath", "Path"))
      .build());
  }

  private Response.ResponseBuilder GetByToken(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    return fileResponseFascade.getByToken(authInfo.getEnterpriseAccount(),
      authInfo.getTicketUserId(),
      getQueryParam("FileToken"),
      getQueryParam("filetype"),
      context.getHeaderString("User-Agent"));
  }

  public Response.ResponseBuilder DownloadByPath(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    return fileResponseFascade.downloadAvatarByPath(FileDownloadQueryVo.builder()
      .ea(authInfo.getEnterpriseAccount())
      .sourceUser(authInfo.getTicketUserId())
      .path(getQueryParam("NPath", "Path"))
      .filetype(getQueryParam("filetype"))
      .ua(context.getHeaderString("User-Agent"))
      .build(), getQueryParam("Name"), getQueryParam("OldUglyJpgIndex"), getQueryParam("autofit"));
  }

  public Response.ResponseBuilder GetByPath(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    return fileResponseFascade.getByPath(FileDownloadQueryVo.builder()
      .ea(authInfo.getEnterpriseAccount())
      .sourceUser(authInfo.getTicketUserId())
      .path(getQueryParam("NPath", "Path"))
      .filetype(getQueryParam("filetype"))
      .parse(Boolean.parseBoolean(getQueryParam("parse")))
      .ua(context.getHeaderString("User-Agent"))
      .build());
  }

  private Response.ResponseBuilder GetMp3ByPath(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    return fileResponseFascade.getMp3ByPath(FileDownloadQueryVo.builder()
      .ea(authInfo.getEnterpriseAccount())
      .sourceUser(authInfo.getTicketUserId())
      .path(getQueryParam("NPath", "Path"))
      .build());
  }

  private Response.ResponseBuilder ProcessDownloadFileByBatch(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    if (!context.hasEntity()) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String document = null;
    try {
      Type type = new TypeToken<ArrayList<BatchFileEntity>>() {
      }.getType();
      ArrayList<BatchFileEntity> docList = JsonUtils.fromJson(new String(IOUtils.toByteArray(context.getEntityStream()), StandardCharsets.UTF_8), type);
      document = BatchDownloadDocumentUtils.getSingleFileDocumentStr(docList);
      return fileResponseFascade.nBatchDownload(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(), document, null);
    } catch (Exception e) {
      log.error( "ProcessDownloadFileByBatch:{}", document, e);
      return builder.status(Response.Status.INTERNAL_SERVER_ERROR).entity("batch download error");
    }
  }

  private Response.ResponseBuilder ProcessCheckFileExist(Response.ResponseBuilder builder, ContainerRequestContext context, AuthInfo authInfo) {
    int size = 0;
    try {
      String str = getQueryParam("Size");
      if (!Strings.isNullOrEmpty(str)) {
        size = Integer.parseInt(getQueryParam("Size"));
      }
    } catch (Exception e) {
      log.error( "ProcessCheckFileExist,", e);
    }
    return fileResponseFascade.checkFileExist(authInfo.getEnterpriseAccount(),
      authInfo.getTicketUserId(),
      getQueryParam("HashValue"),
      size,
      getQueryParam(BUSINESS));
  }
}