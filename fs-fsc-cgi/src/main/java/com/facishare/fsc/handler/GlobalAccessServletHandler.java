package com.facishare.fsc.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
public class GlobalAccessServletHandler implements NoAuthProcessHandler {
    @Autowired
    private ProcessGlobalRichTextHandler processGlobalRichTextHandler;
    @Autowired
    private ProcessGFileHandler processGFileHandler;
    @Override
    public Response process(String area, String servlet, String action) {
        switch (servlet)
        {
            case "RichText":
            return processGlobalRichTextHandler.process(area,servlet,action);
            case "GFile":
                return processGFileHandler.process(area,servlet,action,null);
            default:
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("ServletHandler: Invalid Action of "+action+", Servlet: "+servlet).build();
        }
    }

}
