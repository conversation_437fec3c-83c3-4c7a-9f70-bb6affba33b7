package com.facishare.fsc.handler;

import com.facishare.fsc.RouteAreas;
import com.facishare.fsc.common.authenticate.AuthInfo;
import javax.annotation.Resource;
import javax.ws.rs.core.Response;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
public class NeedAuthServletHandler implements ProcessHandler {

  @Resource
  ProcessFileHandler processFileHandler;
  @Resource
  ProcessAFileHandler processAFileHandler;
  @Resource
  ProcessGFileHandler processGFileHandler;
  @Resource
  ProcessRichTextHandler processRichTextHandler;
  @Resource
  ProcessBaichuanCrossNoticeFileHandler processBaichuanCrossNoticeFile;
  @Resource
  ProcessAvatarHandler processAvatarHandler;
  @Resource
  ProcessCreateImageHandler processCreateImageHandler;
  @Resource
  private ProcessContractHandler processContractHandler;

  @Override
  public Response process(String area, String servlet, String action, AuthInfo authInfo) {
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    if (area.equals(RouteAreas.XiaoKeUser)||area.equals(RouteAreas.BaiChuanUser)||area.equals(RouteAreas.XiaoKeOrBaiChuanUser)){
      switch (servlet) {
        case "File":
          return processFileHandler.process(area, servlet, action, authInfo);
        case "GFile":
          return processGFileHandler.process(area, servlet, action, authInfo);
        case "AFile":
          return processAFileHandler.process(area, servlet, action, authInfo);
        case "RichText":
          return processRichTextHandler.process(area, servlet, action, authInfo);
        case "BaichuanCrossNoticeFile":
          return processBaichuanCrossNoticeFile.process(area, servlet, action, authInfo);
        case "Avatar":
          return processAvatarHandler.process(area, servlet, action, authInfo);
        case "CreateImage":
          return processCreateImageHandler.process(area, servlet, action, authInfo);
        case "Contract":
          return processContractHandler.process(area, servlet, action, authInfo);
        default:
          return builder.status(Response.Status.NOT_FOUND).build();
      }
    }
    return builder.status(Response.Status.BAD_REQUEST).build();
  }
}
