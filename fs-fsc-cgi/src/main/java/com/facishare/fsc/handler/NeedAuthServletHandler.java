package com.facishare.fsc.handler;

import com.facishare.fsc.RouteAreas;
import com.facishare.fsc.common.authenticate.AuthInfo;
import javax.annotation.Resource;
import javax.ws.rs.core.Response;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
public class NeedAuthServletHandler implements ProcessHandler {

  @Resource
  ProcessFileHandler processFileHandler;
  @Resource
  ProcessAFileHandler processAFileHandler;
  @Resource
  ProcessGFileHandler processGFileHandler;
  @Resource
  ProcessRichTextHandler processRichTextHandler;
  @Resource
  ProcessBaichuanCrossNoticeFileHandler processBaichuanCrossNoticeFile;
  @Resource
  ProcessAvatarHandler processAvatarHandler;
  @Resource
  ProcessCreateImageHandler processCreateImageHandler;
  @Resource
  private ProcessContractHandler processContractHandler;

  @Override
  public Response process(String area, String servlet, String action, AuthInfo authInfo) {
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    if (area.equals(RouteAreas.XiaoKeUser)||area.equals(RouteAreas.BaiChuanUser)||area.equals(RouteAreas.XiaoKeOrBaiChuanUser)){
      return switch (servlet) {
        case "File" -> processFileHandler.process(area, servlet, action, authInfo);
        case "GFile" -> processGFileHandler.process(area, servlet, action, authInfo);
        case "AFile" -> processAFileHandler.process(area, servlet, action, authInfo);
        case "RichText" -> processRichTextHandler.process(area, servlet, action, authInfo);
        case "BaichuanCrossNoticeFile" -> processBaichuanCrossNoticeFile.process(area, servlet, action, authInfo);
        case "Avatar" -> processAvatarHandler.process(area, servlet, action, authInfo);
        case "CreateImage" -> processCreateImageHandler.process(area, servlet, action, authInfo);
        case "Contract" -> processContractHandler.process(area, servlet, action, authInfo);
        default -> builder.status(Response.Status.NOT_FOUND).build();
      };
    }
    return builder.status(Response.Status.BAD_REQUEST).build();
  }
}
