package com.facishare.fsc.handler;

import com.facishare.fsc.core.FSCContext;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import java.util.Optional;
import org.apache.commons.io.IOUtils;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.MultivaluedMap;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.Map;
import java.util.Objects;

/**
 * Created by <PERSON> on 16/5/20.
 */
public class AbstractProcessHandler {
    public String getQueryParam(String key) {
        ContainerRequestContext context = Objects.requireNonNull(FSCContext.getCurrentRequestContext());
        MultivaluedMap<String, String> parameters = context.getUriInfo().getQueryParameters();
        String val = parameters.getFirst(key);
        if (Strings.isNullOrEmpty(val)) {
            return getParams().get(key.toLowerCase());
        }
        return val;
    }

    /**
     * 获取参数，如果没有则返回null
     * @param keys 优先级从高到低 不区分大小写
     * @return 参数值
     */
    public String getQueryParam(String... keys) {
        ContainerRequestContext context = Objects.requireNonNull(FSCContext.getCurrentRequestContext());
        MultivaluedMap<String, String> parameters = context.getUriInfo().getQueryParameters();
        for (String key : keys) {
            String val = parameters.getFirst(key);
            if (!Strings.isNullOrEmpty(val)) {
                return val;
            }
        }
        Map<String, String> params = getParams();
        for (String key : keys) {
            String val = params.get(key.toLowerCase());
            if (!Strings.isNullOrEmpty(val)) {
                return val;
            }
        }
        return null;
    }

    public String getRequestHeader(String key) {
        String value = FSCContext.getCurrentRequestContext().getHeaders().getFirst(key);
        return Strings.isNullOrEmpty(value) ? FSCContext.getCurrentRequestContext().getHeaders().getFirst(key.toLowerCase()) : value;
    }

    public static Optional<String> getFirstExistParam(String... parametersNames){
        Optional<String> headerParameters = getFirstExistHeader(parametersNames);
        if (headerParameters.isPresent()) {
            return headerParameters;
        } else {
            return getFirstExistQueryParam(parametersNames);
        }
    }

    public static Optional<String> getFirstExistHeader(String... headerNames){
        ContainerRequestContext currentRequestContext = FSCContext.getCurrentRequestContext();
        if (headerNames != null) {
            for (String headerName : headerNames) {
                String headerValue = currentRequestContext.getHeaderString(headerName);
                if (!Strings.isNullOrEmpty(headerValue)) {
                    // 如果存在逗号，返回逗号前的部分
                    int commaIndex = headerValue.indexOf(",");
                    return Optional.of((commaIndex != -1) ? headerValue.substring(0, commaIndex) : headerValue);
                }
            }
        }
        return Optional.empty();
    }

    public static Optional<String> getFirstExistQueryParam(String... paramNames){
        MultivaluedMap<String, String> queryParameters = FSCContext.getCurrentRequestContext().getUriInfo().getQueryParameters();
        if (paramNames != null) {
            for (String paramName : paramNames) {
                String paramValue = queryParameters.getFirst(paramName);
                // 防呆，如果没有找到，再尝试小写
                if (Strings.isNullOrEmpty(paramValue)){
                    paramValue = queryParameters.getFirst(paramName.toLowerCase());
                }
                if (!Strings.isNullOrEmpty(paramValue)) {
                    return Optional.of(paramValue);
                }
            }
        }
        return Optional.empty();
    }

    public String getBodyToString() throws IOException {
        return new String(IOUtils.toByteArray(FSCContext.getLocal().getCurrentRequestContext().getEntityStream()), StandardCharsets.UTF_8);
    }

    /**
     * 获取所有参数，请求的key转换为小写
     */
    public Map<String, String> getParams() {
        ContainerRequestContext context = Objects.requireNonNull(FSCContext.getCurrentRequestContext());
        MultivaluedMap<String, String> parameters = context.getUriInfo().getQueryParameters();
        Map<String, String> map = Maps.newHashMap();
        for (String key : parameters.keySet()) {
            map.put(key.toLowerCase(), parameters.getFirst(key));
        }
        return map;
    }

    public Map<String,String> getFormParams(){
        Enumeration<String> enums= FSCContext.getRequest().getParameterNames();
        Map<String, String> map = Maps.newHashMap();
        while (enums.hasMoreElements()) {
            String key=enums.nextElement();
            map.put(key.toLowerCase(), FSCContext.getRequest().getParameter(key));
        }
        return map;
    }
}
