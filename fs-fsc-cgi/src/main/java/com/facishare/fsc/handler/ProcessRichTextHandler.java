package com.facishare.fsc.handler;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.fascade.impl.FileResponseFascadeImpl;
import com.facishare.fsc.core.model.RichTextPostData;
import com.facishare.fsc.core.model.vo.FileDownloadQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/14.
 */
@Component
@Slf4j
public class ProcessRichTextHandler extends AbstractProcessHandler implements ProcessHandler {

  @Autowired
  private FileResponseFascadeImpl fileResponseFascade;

  @Override
  public Response process(String area, String servlet, String action, AuthInfo authInfo) {
    ContainerRequestContext context = FSCContext.getCurrentRequestContext();
    ;
    Response.ResponseBuilder builder = Response.status(200);
    switch (action) {
      case "ViewPic":
        builder = processRichTextActionViewPic(builder, context, authInfo);
        break;
      case "Save": // 提交已编辑的富文本，需要将文本中的所有临时图片保存为正式文件
        builder = processRichTextActionSave(builder, context, authInfo);
        break;
      default:
        builder.status(Response.Status.NOT_FOUND)
            .entity("ServletHandler: Invalid Action of " + action + ", Servlet: " + servlet);
    }
    return builder.build();
  }

  private Response.ResponseBuilder processRichTextActionSave(Response.ResponseBuilder builder,
      ContainerRequestContext context, AuthInfo authInfo) {
    // FSAuthP 不再使用因此不再校验（即均为XiaoKeUserAuthTicket 而不会存在 BaiChuanAuthTicket）
    // if (!(authInfo.getFsAuthTicket() instanceof XiaoKeUserAuthTicket)) {
    //   return builder.status(Response.Status.FORBIDDEN);
    // }
    String postData = null;
    try {
      postData = getBodyToString();
      RichTextPostData richTextPostData = JsonUtils.fromJson(postData, RichTextPostData.class);
      return fileResponseFascade
          .saveEnterpriseRichText(authInfo.getEnterpriseAccount(), authInfo.getTicketUserId(),
              richTextPostData);
    } catch (Exception e) {
      log.error("processRichTextActionSave:{}", postData, e);
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage())
          .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
    }
  }

  private Response.ResponseBuilder processRichTextActionViewPic(Response.ResponseBuilder builder,
      ContainerRequestContext context, AuthInfo authInfo) {
    return fileResponseFascade.getByPath(FileDownloadQueryVo.builder().ea(authInfo.getEnterpriseAccount()).path(getQueryParam("NPath", "Path")).build());
  }
}
