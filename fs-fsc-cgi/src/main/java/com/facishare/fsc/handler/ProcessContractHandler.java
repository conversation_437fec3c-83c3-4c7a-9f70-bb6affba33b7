package com.facishare.fsc.handler;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.fascade.ContractFacade;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 16/5/24.
 */
@Component
@Slf4j
public class ProcessContractHandler extends AbstractProcessHandler implements ProcessHandler {
    @Autowired
    private ContractFacade contractFacade;

    @Override
    public Response process(String area, String servlet, String action, AuthInfo authInfo) {
        Response.ResponseBuilder builder = Response.status(Response.Status.OK);
        switch (action) {
            case "Upload":
                builder = processUpload(builder,authInfo);
                break;
            default:
                builder.status(Response.Status.NOT_FOUND);
        }
        return builder.build();
    }

    private Response.ResponseBuilder processUpload(Response.ResponseBuilder builder, AuthInfo authInfo) {
        byte[] data = null;
        Map<String,Object> result= Maps.newHashMap();
        DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
        ServletFileUpload upload = new ServletFileUpload(diskFileItemFactory);
        try {
            List<FileItem> items = upload.parseRequest((HttpServletRequest) FSCContext.getRequest());
            Iterator iter = items.iterator();
            while (iter.hasNext()) {
                FileItem item = (FileItem) iter.next();
                if (item.isFormField()) {
                } else {
                    data = item.get();
                }
            }
            if (data == null) {
                log.error("processUpload:data field not found");
                result.put("M1",1);
                result.put("M2","processUpload:data field not found");
                return builder.status(Response.Status.OK).entity(JsonUtils.toJson(result));
            }
            return contractFacade.uploadFile(authInfo.getMobile(),
                    authInfo.getDeviceId(),authInfo.getEmployeeId(),
                    authInfo.getEnterpriseId(),authInfo.getEmployeeAccount()
                    ,authInfo.getEnterpriseAccount(),data);
        } catch (Exception e) {
            log.error("processUpload:data field not found");
            result.put("M1",1);
            result.put("M2","processUpload:data field not found");
            return builder.status(Response.Status.OK).entity("file field deal error");
        }
    }
}
