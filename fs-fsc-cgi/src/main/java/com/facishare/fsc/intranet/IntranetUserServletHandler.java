package com.facishare.fsc.intranet;

import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.core.fascade.FileResponseFascade;
import com.facishare.fsc.core.model.vo.FileDownloadQueryVo;
import com.facishare.fsc.handler.AbstractProcessHandler;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/25.
 */
@Component
@Slf4j
public class IntranetUserServletHandler extends AbstractProcessHandler implements
    IntranetProcessHandler {

  @Autowired
  private FileResponseFascade fileResponseFascade;

  @Override
  public Response process(String servlet, String action, AuthInfo authInfo) {
    switch (servlet) {
      case "File":
        return processFile(action);
      case "FileShare":
        return  processFileShare(action);
      default:
        return Response.status(Response.Status.NOT_FOUND).build();
    }
  }


  private Response processFile(String action) {
    switch (action) {
      case "DownloadByPath":
        return downloadByPath();
      case "GetByPath":
        return getByPath();
      default:
        return Response.status(Response.Status.NOT_FOUND).build();
    }
  }

  private Response processFileShare(String action)
  {
    switch (action) {
      case "QrShow":
        return qrShow();
      default:
        return Response.status(Response.Status.NOT_FOUND).build();
    }
  }


  public Response getByPath() {
    String ea = getQueryParam("ea");
    String path = getQueryParam("path");
    String securityGroup = getQueryParam("security_group");
    String employID = getQueryParam("employee_id");
    if (Strings.isNullOrEmpty(path)) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }
    return fileResponseFascade.getByPath(
        FileDownloadQueryVo.builder().ea(ea).sourceUser(ea + "." + employID).path(path)
            .securityGroup(securityGroup).build()).build();
  }

  public Response getByPath(String ea, String path, String employee_id) {
    if (Strings.isNullOrEmpty(path)) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }
    return fileResponseFascade.getByPath(
        FileDownloadQueryVo.builder().ea(ea).sourceUser(ea + "." + employee_id).path(path).build())
        .build();
  }

  public Response downloadByPath(String ea, String path, String employee_id, String sg,
      String name) {
    if (Strings.isNullOrEmpty(path)) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }
    return fileResponseFascade.downloadByPath(
        FileDownloadQueryVo.builder().ea(ea).sourceUser(ea + "." + employee_id).path(path)
            .securityGroup(sg).build(), name).build();
  }

  public Response downloadByPath() {
    String ea = getQueryParam("ea");
    String path = getQueryParam("path");
    String name = getQueryParam("name");
    String securityGroup = getQueryParam("security_group");
    String employID = getQueryParam("employee_id");
    if (Strings.isNullOrEmpty(path)) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }
    return fileResponseFascade.downloadByPath(
        FileDownloadQueryVo.builder().ea(ea).sourceUser(ea + "." + employID).path(path)
            .securityGroup(securityGroup).build(),name).build();
  }

  public  Response qrShow() {
    String encryptUrl = getQueryParam("url");
    String sizeStr = getQueryParam("size");
    int defaultSize = 200;
    int size = NumberUtils.toInt(sizeStr, defaultSize);
    return fileResponseFascade.qrShow(encryptUrl, size).build();
  }
}
