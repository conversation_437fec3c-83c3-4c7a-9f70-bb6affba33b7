package com.facishare.fsc.provider;

import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.exceptions.ExceptionConstant;
import com.facishare.fsc.core.exceptions.FSCRemoteException;
import com.fxiaoke.common.Guard;
import com.fxiaoke.enterpriserelation2.arg.AuthWithoutEaArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.AuthUserResult;
import com.fxiaoke.enterpriserelation2.service.AuthService;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import java.util.HashMap;
import java.util.function.Function;
import java.util.stream.Stream;
import javax.annotation.Resource;
import javax.ws.rs.core.MultivaluedMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.jboss.resteasy.spi.ResteasyProviderFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.container.ContainerResponseContext;
import javax.ws.rs.container.ContainerResponseFilter;
import javax.ws.rs.core.Cookie;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by Aaron on 16/4/20.
 */
@Provider
@Component
@Slf4j
public class FSCRestRequestFilter
    implements ContainerRequestFilter, ContainerResponseFilter, ExceptionMapper<Throwable> {
  private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("fsc");
  private Guard guard;
  @Autowired
  AuthService authService;
  @Autowired
  EIEAConverter eieaConverter;
  @Resource
  ActiveSessionAuthorizeService activeSessionAuthorizeService;

  private static final int DEFAULT_EMPLOYEE_ID=10000;

  @PostConstruct
  public void init() {
    ConfigFactory.getInstance().getConfig("fs-bi-file-config", config -> {
      String key = config.get("biFileEncryptKey");
      if (!Strings.isNullOrEmpty(key)) {
        guard = new Guard(key);
      }
    });
  }

  @Override
  public void filter(ContainerRequestContext requestContext) {
    FSCContext fscContext = FSCContext.getLocal();
    fscContext.setRequestContext(requestContext);
    // UploadForOpen这种路由的cookie是从外部请求来的，解析cookie要单独做
    String requestUri = requestContext.getUriInfo().getRequestUri().toString().toLowerCase();
    String appId = getAppidByRequest(requestContext);
    AuthInfo authInfo = null;
    if (requestUri.contains("/em/file/uploadforopen") || requestUri.contains("/em/file/getbypathforopen") ||
        requestUri.contains("/em/file/downloadbypathforopen") || !Strings.isNullOrEmpty(appId)) {
      Map<String, Cookie> cookieMap = requestContext.getCookies();
      String erInfo = cookieMap.get("ERInfo") == null ? "" : cookieMap.get("ERInfo").getValue();
      String crInfo = cookieMap.get("CRInfo") == null ? "" : cookieMap.get("CRInfo").getValue();
      if (!Strings.isNullOrEmpty(erInfo) || !Strings.isNullOrEmpty(crInfo)) {
        String cookie = erInfo;
        if (Strings.isNullOrEmpty(erInfo)) {
          cookie = crInfo;
        }
        log.debug("cookie:{},appId:{}", cookie, appId);
        AuthWithoutEaArg arg = new AuthWithoutEaArg();
        arg.setErInfo(cookie);
        arg.setLinkAppId(appId);
        RestResult<AuthUserResult> result = authService.authWithoutEa(HeaderObj.newInstance(appId, null, null, null),
            arg);
        if (!result.isSuccess()) {
          log.warn("get the auth message error,cookie:{},appId:{}",cookie,appId);
          return;
        }
        String ea = result.getData().getUpstreamEa();
        authInfo = new AuthInfo();
        authInfo.setEnterpriseAccount(ea);
        int employeeId = getEmployeeIdByDownstreamOuterUid(result.getData().getDownstreamOuterUid());
        authInfo.setEmployeeId(employeeId);
        authInfo.setTicketUserId("E." + employeeId);
        int ei = eieaConverter.enterpriseAccountToId(ea);
        authInfo.setEnterpriseId(ei);
        fscContext.setAuthInfo(authInfo);
        ResteasyProviderFactory.pushContext(AuthInfo.class, authInfo);
      } else {
        return;
      }
    } else if (requestUri.contains("/apl/async")) {
      String ea = getQueryParam("ea");
      String ei = getQueryParam("ei");
      String employeeId = getQueryParam("employeeId");
      if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(ei) || Strings.isNullOrEmpty(employeeId)) {
        log.warn("get the auth message error,ea:{},ei:{},employeeId:{}", ea, ei, employeeId);
        return;
      }
      authInfo = new AuthInfo();
      authInfo.setEnterpriseAccount(ea);
      authInfo.setEnterpriseId(NumberUtils.toInt(ei));
      authInfo.setEmployeeId(NumberUtils.toInt(employeeId));
      authInfo.setTicketUserId("E." +employeeId);
      fscContext.setAuthInfo(authInfo);
      ResteasyProviderFactory.pushContext(AuthInfo.class, authInfo);
    } else {
      String path = requestContext.getUriInfo().getQueryParameters().getFirst("path");
      if (!Strings.isNullOrEmpty(path) && path.contains(":") && path.contains("N_")) {
        try {
          String ei = guard.decode(path.substring(0, path.indexOf(":")));
          String ea = eieaConverter.enterpriseIdToAccount(NumberUtils.toInt(ei));
          authInfo = new AuthInfo();
          authInfo.setEnterpriseAccount(ea);
          authInfo.setEnterpriseId(NumberUtils.toInt(ei));
          authInfo.setEmployeeId(DEFAULT_EMPLOYEE_ID);
          fscContext.setAuthInfo(authInfo);
          ResteasyProviderFactory.pushContext(AuthInfo.class, authInfo);
          return;
        } catch (Exception e) {
          log.error("获取加密企业失败,path:{}", path);
        }
      } else {
        // 获取cookie
        Optional<String> cookie = getExistFirstCookieStr(requestContext, "FSAuthXC", "FSAuthX");
        if (cookie.isEmpty()){
          return;
        }
        // 根据cookie从ASM获取用户信息
        Optional<AuthInfo> authInfoOptional = getAuthInfo(cookie.get());
        if (authInfoOptional.isEmpty()) {
          return;
        }
        // 将用户信息放入上下文
        authInfo = authInfoOptional.get();
        fscContext.setAuthInfo(authInfo);
        ResteasyProviderFactory.pushContext(AuthInfo.class, authInfo);
      }
    }
    if (authInfo!=null){
      TraceContext traceContext = TraceContext.get();
      String userId = authInfo.getEnterpriseAccount() + '.' + authInfo.getEmployeeId();
      if (gray.isAllow("color", userId)) {
        traceContext.setColor(true);
      }
      traceContext.setUid(userId);
      traceContext.setEa(authInfo.getEnterpriseAccount());
      traceContext.setEmployeeId(String.valueOf(authInfo.getEmployeeId()));
      traceContext.setEi(String.valueOf(authInfo.getEnterpriseId()));
      MDC.put("userId", userId);
    }
  }

  @Override
  public Response toResponse(Throwable exception) {
    log.error("final Exception:{}", "toResponse", exception);

    if (exception instanceof javax.ws.rs.NotFoundException) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    if (exception instanceof FSCRemoteException
        && Objects.equals(((FSCRemoteException) exception).code, ExceptionConstant.INVALID_EXTENSION)) {
      return Response.status(Response.Status.BAD_REQUEST).entity("The extension is invalid").encoding("UTF-8").build();
    }
    return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("Server exception(FSC)").encoding("UTF-8").build();
  }

  @Override
  public void filter(ContainerRequestContext requestContext, ContainerResponseContext responseContext) {
    FSCContext.removeLocal();
    MDC.clear();
  }

  public String getQueryParam(String key) {
    ContainerRequestContext context = Objects.requireNonNull(FSCContext.getCurrentRequestContext());
    MultivaluedMap<String, String> parameters = context.getUriInfo().getQueryParameters();

    Map<String, String> paramMap = new HashMap<>();
    for (String paramKey : parameters.keySet()) {
      paramMap.put(paramKey.toLowerCase(), parameters.getFirst(paramKey));
    }

    String val = paramMap.get(key);
    if (Strings.isNullOrEmpty(val)) {
      return paramMap.get(key.toLowerCase());
    }

    return val;
  }

  private int getEmployeeIdByDownstreamOuterUid(Long downstreamOuterUid){
    // 2147483647
    if (downstreamOuterUid == null || downstreamOuterUid < 1
        || downstreamOuterUid > Integer.MAX_VALUE) {
      log.warn("downstreamOuterUid:{} is Invalid,Reset to:{}", downstreamOuterUid,DEFAULT_EMPLOYEE_ID);
      return DEFAULT_EMPLOYEE_ID;
    }
    return downstreamOuterUid.intValue();
  }

  private String getAppidByRequest(ContainerRequestContext requestContext) {
    // 从Query中获取
    String appId = getAppidByQuery(requestContext);
    if (appId==null) {
      // 从Head中获取
      appId = getAppidByHeader(requestContext);
    }
    return appId;
  }

  private String getAppidByQuery(ContainerRequestContext requestContext) {
    // 获取查询参数
    MultivaluedMap<String, String> queryParameters = requestContext.getUriInfo().getQueryParameters();
    // 获取第一个非空的查询参数值
    Function<String, String> getFirstNonEmptyParam = key -> {
      String value = queryParameters.getFirst(key);
      return value != null && !value.isBlank() ? value : null;
    };
    return Stream.of("appid", "appId")
        .map(getFirstNonEmptyParam)
        .filter(Objects::nonNull)
        .findFirst()
        .orElse(null);
  }
  // 从Head中获取
  private String getAppidByHeader(ContainerRequestContext requestContext) {
   return Stream.of("fs-out-appId", "fs-out-appid")
        .map(requestContext::getHeaderString)
        .filter(value -> value != null && !value.isBlank())
        .findFirst()
        .orElse(null);
  }

  private Optional<String> getExistFirstCookieStr(ContainerRequestContext requestContext, String... cookieNames) {
    Map<String, Cookie> cookies = requestContext.getCookies();
    if (cookieNames!=null){
      for (String cookieName : cookieNames) {
        Cookie cookie = cookies.get(cookieName);
        if (cookie != null && !Strings.isNullOrEmpty(cookie.getValue())) {
          return Optional.of(cookie.getValue());
        }
      }
    }
    return Optional.empty();
  }

  private Optional<AuthInfo> getAuthInfo(String cookie) {
    if (!Strings.isNullOrEmpty(cookie)){
      CookieToAuth.Argument arg = new CookieToAuth.Argument();
      arg.setCookie(cookie);
      CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(arg);
      if (result != null && result.getBody() != null) {
        AuthXC authXC = result.getBody();
        AuthInfo authInfo = new AuthInfo();
        // 设置企业账号
        authInfo.setEnterpriseAccount(authXC.getEnterpriseAccount());
        // 设置企业ID
        authInfo.setEnterpriseId(authXC.getEnterpriseId());
        // 设置员工账号
        authInfo.setEmployeeAccount(authXC.getAccount());
        // 设置员工ID
        authInfo.setEmployeeId(authXC.getEmployeeId());
        // 设置员工名称
        authInfo.setEmployeeName(authXC.getName());
        // 设置员工全名称
        authInfo.setEmployeeFullName(authXC.getFullName());
        // 设置手机号
        authInfo.setMobile(authXC.getMobile());
        // 设置客户端来源
        authInfo.setClientSource(authXC.getClientSource());
        // 设置设备ID
        authInfo.setDeviceId(authXC.getDeviceId());
        // 设置会话ID(不再设置SessionToken,不再使用,因此不再设置)
        authInfo.setSessionId(authXC.getSessionId());
        // 设置员工ID 为 "E."+员工ID(老文件系统的逻辑)
        authInfo.setTicketUserId("E."+authXC.getEmployeeId());
        // authInfo.setFsAuthTicket(); 在代码中不再使用，因此不再设置
        log.debug("get the auth message success,authInfo:{}", authInfo);
        return Optional.of(authInfo);
      }
    }
    log.warn("get the authInfo fail,cookie:{}", cookie);
    return Optional.empty();
  }

}
