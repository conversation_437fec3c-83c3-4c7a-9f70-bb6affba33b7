package com.facishare.fsc.utils;

import cn.hutool.core.io.FileTypeUtil;
import com.facishare.fsc.domain.BaseException;
import com.facishare.fsc.domain.FileCheckConfig;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.moandjiezana.toml.Toml;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "FileTypeCheckHelp")
public class FileTypeCheckHelp {

  private Set<String> ableFileCheckEaList;
  private Map<String, FileCheckConfig> fileCheckEaConfigCache;

  private static final String CONFIG_NAME = "file-upload-type-config-toml";
  private static final String DEFAULT_COMMON_KEY = "common";
  private static final int MIN_CHECK_FILE_SIZE = 205;

  private static final Set<String> NEED_FOR_BACK_FILE_LIST =new HashSet<>();
  private static final Set<String> ONE_TO_MANY_LIST=new HashSet<>();

  static {
    ONE_TO_MANY_LIST.add("jpg,jpeg,jpe,jfif");
    ONE_TO_MANY_LIST.add("tif,tiff");

    NEED_FOR_BACK_FILE_LIST.add("zip");
    NEED_FOR_BACK_FILE_LIST.add("docx");
    NEED_FOR_BACK_FILE_LIST.add("xlsx");
    NEED_FOR_BACK_FILE_LIST.add("pptx");
    NEED_FOR_BACK_FILE_LIST.add("apk");
    NEED_FOR_BACK_FILE_LIST.add("jar");
    NEED_FOR_BACK_FILE_LIST.add("war");
    NEED_FOR_BACK_FILE_LIST.add("ofd");
  }

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig(CONFIG_NAME, config -> {
      String configStr = config.getString();
      if (configStr != null && !configStr.isEmpty()) {
        // 加载配置
        log.info("configName:[ {} ] load start", CONFIG_NAME);
        loadConfig(new Toml().read(configStr));
        log.info("configName:[ {} ] load end", CONFIG_NAME);
      } else {
        log.warn("configName:[ {} ] does not exist or or empty,clear config", CONFIG_NAME);
        resetConfig();
      }
    });
  }

  public void check(InputStream stream, String ea, long fileSize, String extension) throws IOException {

    // 校验是否支持mark/reset(不支持直接跳过避免阻塞业务)
    if (!stream.markSupported()){
      return;
    }

    // 校验是否开启了文件类型检查
    if (!ableFileCheck(ea)) {
      return;
    }

    // 校验文件扩展名类型与文件大小
    if (Strings.isNullOrEmpty(extension) || fileSize <= 0) {
      throw new BaseException(400,"file ext or fileSize is empty");
    }

    // 校验文件扩展名类型是否在支持名单中(通过名单检测的不会存在配置为空的情况）
    FileCheckConfig fileCheckConfig = getFileCheckConfig(ea);
    // 标准化处理文件扩展名（去除.并转为小写）
    String ext = getExtension(extension);
    if (notInSupportList(ext, fileCheckConfig)) {
      throw new BaseException(400,"file ext is not in support list");
    }

    // 判断是否开启了文件类型真实检查(不开启则直接返回)
    if (!fileCheckConfig.ableExactCheck()) {
      return;
    }

    // 文件小于最低检测标准(不检测直接返回)
    if (fileSize < MIN_CHECK_FILE_SIZE) {
      return;
    }

    // 在此列表中的文件类型服务缺少对应的检测方法,为避免误判直接返回
    if (ignoreExactCheck(ext, fileCheckConfig)) {
      return;
    }

    // 获取文件真实类型,如果无法获取类型则直接返回避免阻塞业务
    Optional<String> fileTypeOptional = getBinaryFileType(stream, ext);
    if (fileTypeOptional.isEmpty()) {
      if (fileCheckConfig.ableStrictMode()){
        throw new BaseException(400,"files that cannot be verified are forbidden to be uploaded");
      }
      log.warn("Could not get the actual file type,skip,ea:{},ext:{}", ea,ext);
      return;
    }

    // 校验文件真实类型是否在支持名单中
    if (notInSupportList(fileTypeOptional.get(), fileCheckConfig)) {
      throw new BaseException(400,"file actual type "+fileTypeOptional.get()+ " is not in support list");
    }

    // 检查文件扩展名与文件真实类型是否匹配
    if (fileCheckConfig.ableTypeMatching()&&!matchActualType(ext,fileTypeOptional.get())) {
      throw new BaseException(400,"file ext "+extension+" is not matching actual type "+fileTypeOptional.get());
    }
  }

  private void loadConfig(Toml toml) {
    Set<String> eaSet = ConcurrentHashMap.newKeySet();
    Map<String, FileCheckConfig> fileCheckConfigMap = new ConcurrentHashMap<>();
    for (Map.Entry<String, Object> entry : toml.entrySet()) {
      String ea = entry.getKey();
      Toml entryValue = (Toml) entry.getValue();
      FileCheckConfig fileCheckConfig = createFileCheckConfig(ea, entryValue);
      if (!fileCheckConfig.check()) {
        // 配置错误时不加载配置使用上一次的配置
        if (fileCheckEaConfigCache != null && fileCheckEaConfigCache.containsKey(ea)) {
          fileCheckConfig = fileCheckEaConfigCache.get(ea);
          log.warn("ea:[ {} ] use last config", ea);
        } else {
          log.error("ea:[ {} ] config error,please check", ea);
          continue;
        }
      }
      fileCheckConfigMap.put(ea, fileCheckConfig);
      eaSet.add(ea);
      log.info("add ea:[ {} ] config success", ea);
    }
    updateConfig(eaSet, fileCheckConfigMap);
  }

  private FileCheckConfig createFileCheckConfig(String ea, Toml entryValue) {
    String whiteList = entryValue.getString("whiteList", "");
    String blackList = entryValue.getString("blackList", "");
    Boolean whiteModel = entryValue.getBoolean("whiteModel", true);
    Boolean ableExactCheck = entryValue.getBoolean("ableExactCheck", false);
    Boolean ableStrictMode = entryValue.getBoolean("ableStrictMode", false);
    String ignoreExactCheckList = entryValue.getString("ignoreExactCheckList", "");
    Boolean ableTypeMatching = entryValue.getBoolean("ableTypeMatching",false);
    // 初始化配置时检查配置是否正确
    return new FileCheckConfig(ea, whiteList, blackList,
        whiteModel, ableExactCheck,ableStrictMode,
        ignoreExactCheckList, ableTypeMatching);
  }

  // 更新配置（保持更新顺序）
  private void updateConfig(Set<String> eaSet, Map<String, FileCheckConfig> fileCheckConfigMap) {
    this.fileCheckEaConfigCache = fileCheckConfigMap;
    this.ableFileCheckEaList = eaSet;
  }

  // 重置配置(文件删除或者配置为空时)
  private void resetConfig() {
    Optional.ofNullable(ableFileCheckEaList).ifPresent(Set::clear);
    Optional.ofNullable(fileCheckEaConfigCache).ifPresent(Map::clear);
  }

  private boolean ableFileCheck(String ea) {
    if (ableFileCheckEaList.isEmpty() || fileCheckEaConfigCache.isEmpty()) {
      return false;
    }
    return ableFileCheckEaList.contains(ea) || ableFileCheckEaList.contains(
        DEFAULT_COMMON_KEY);
  }

  private FileCheckConfig getFileCheckConfig(String ea) {
    FileCheckConfig fileCheckConfig = fileCheckEaConfigCache.get(ea);
    if (fileCheckConfig == null) {
      fileCheckConfig = fileCheckEaConfigCache.get(DEFAULT_COMMON_KEY);
    }
    return fileCheckConfig;
  }

  private boolean matchActualType(String extension, String actualType) {
    String[] actualTypes = actualType.contains(",") ? actualType.split(",") : new String[]{actualType};
    for (String type : actualTypes) {
      if (extension.equalsIgnoreCase(type)) {
        return true;
      }
    }
    return false;
  }

  private boolean ignoreExactCheck(String extension, FileCheckConfig fileCheckConfig) {
    String[] extensions = extension.contains(",") ? extension.split(",") : new String[]{extension};
    for (String ext : extensions) {
      if (fileCheckConfig.isIgnoreExactCheckList(ext)) {
        return true;
      }
    }
    return false;
  }

  private boolean notInSupportList(String extension, FileCheckConfig fileCheckConfig) {
    String[] extensions = extension.contains(",") ? extension.split(",") : new String[]{extension};
    if (fileCheckConfig.isWhiteModel()) {
      for (String ext : extensions) {
        if (fileCheckConfig.isInWhiteList(ext)) {
          return false;
        }
      }
      return true;
    } else {
      for (String ext : extensions) {
        if (fileCheckConfig.isInBlackList(ext)) {
          return true;
        }
      }
      return false;
    }
  }


  private String getExtension(String extension) {
    if (Strings.isNullOrEmpty(extension)) {
      return "";
    }
    if (extension.contains(".")) {
      return FilenameUtils.getExtension(extension);
    }
    return extension.toLowerCase();
  }

  /**
   * 文件魔数相同,需要扩展名辅助识别
   * @param extension 文件扩展名
   * @return 是否需要辅助识别
   */
  private boolean isNeedForBack(String extension){
    return NEED_FOR_BACK_FILE_LIST.contains(extension);
  }

  /**
   * 相同文件类型有多种扩展名,需将其多种扩展名均返回以避免误判
   * @param extension 文件扩展名
   * @return 适配处理后的文件扩展名
   */
  private String oneToMayFormat(String extension){
    for (String ext : ONE_TO_MANY_LIST) {
      if (ext.contains(extension)) {
        return ext;
      }
    }
    return extension;
  }

  private Optional<String> getBinaryFileType(InputStream stream, String extension) throws IOException {

    try {
      String type;
      stream.mark(65);
      if (isNeedForBack(extension)) {
        type = FileTypeUtil.getType(stream, "default." + extension);
      } else {
        type = FileTypeUtil.getType(stream);
      }
      if (type != null) {
        return Optional.of(oneToMayFormat(type));
      }
    } finally {
      stream.reset();
    }

    try {
      stream.mark(65);
      return MagicNumberFileUtil.getExtByStream(stream, extension);
    } finally {
      stream.reset();
    }


  }


}
