package com.facishare.fsc.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class FilenameUtil {

  private FilenameUtil() {
  }

  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


  public static String generateFileName(String prefix, String suffix) {
    LocalDateTime now = LocalDateTime.now();
    return prefix + "-" + now.format(formatter) + "." + suffix;
  }

  // 生成 YYYY-MM-dd格式的文件名
  public static String generateFileName(String suffix) {
    LocalDateTime now = LocalDateTime.now();
    return now.format(formatter) + "." + suffix;
  }
}
