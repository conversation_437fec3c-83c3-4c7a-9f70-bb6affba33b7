package com.facishare.fsc.utils;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.HexUtil;
import com.facishare.fsc.domain.BaseException;
import com.facishare.fsc.domain.ErrorInfoEnum;
import com.facishare.fsc.domain.FileUploadTypeConfig;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.moandjiezana.toml.Toml;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentSkipListMap;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "FileTypeHelper")
public class FileTypeHelper {

  private static final int MIN_CHECK_FILE_SIZE =205;
  private boolean ableFileInspection;
  private static final List<String> supportCustomCheckFileType=new ArrayList<>();
  private static final Map<String, String> MagicNumberFileTypeComparisonTable = new ConcurrentSkipListMap<>();
  static {
    supportCustomCheckFileType.add("msg");
    supportCustomCheckFileType.add("htm");
    supportCustomCheckFileType.add("html");
    supportCustomCheckFileType.add("docm");
    supportCustomCheckFileType.add("xml");
    supportCustomCheckFileType.add("vsd");
    supportCustomCheckFileType.add("wps");
    supportCustomCheckFileType.add("et");
    supportCustomCheckFileType.add("dps");
    supportCustomCheckFileType.add("heic");

    MagicNumberFileTypeComparisonTable.put("msg","d0cf11e0a1b11ae1");
    MagicNumberFileTypeComparisonTable.put("htm","3c21646f");
    MagicNumberFileTypeComparisonTable.put("html","3c21444f");
    MagicNumberFileTypeComparisonTable.put("docm","504b0304");
    MagicNumberFileTypeComparisonTable.put("xml","3c3f786d6c2076657273");
    MagicNumberFileTypeComparisonTable.put("vsd","d0cf11e0a1b11ae10000");
    MagicNumberFileTypeComparisonTable.put("wps","d0cf11e0a1b11ae10000");
    MagicNumberFileTypeComparisonTable.put("et","d0cf11e0a1b11ae10000");
    MagicNumberFileTypeComparisonTable.put("dps","d0cf11e0a1b11ae10000");
    MagicNumberFileTypeComparisonTable.put("heic","667479706865696667479706d");
  }
  Cache<String, FileUploadTypeConfig> fileUploadTypeConfigCache=CacheBuilder.newBuilder().build();

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-fsc-cgi-config", config -> {
      ableFileInspection=config.getBool("able.file.inspection",false);
    });
    if (ableFileInspection){
      ConfigFactory.getConfig("fs-file-upload-type-config-toml", config -> {
        Toml toml = new Toml().read(config.getString());
        loadConfig(toml);
        log.info("FileTypeHelper init success");
      });
    }else {
      log.info("FileTypeHelper init success,ableFileInspection is false");
    }

  }

  private void loadConfig(Toml toml){
    for (Entry<String, Object> entry : toml.entrySet()){
      String enterpriseAccount = entry.getKey();
      Toml enterpriseFileUploadTypeConfig = (Toml) entry.getValue();
      FileUploadTypeConfig fileUploadTypeConfig = new FileUploadTypeConfig();
      fileUploadTypeConfig.setEnterpriseAccount(enterpriseAccount);
      fileUploadTypeConfig.setSupportTypeList(Splitter.on(",").omitEmptyStrings().trimResults().splitToList(enterpriseFileUploadTypeConfig.getString("supportUploadFileType")));
      fileUploadTypeConfig.setNotCheckTypeList(Splitter.on(",").omitEmptyStrings().trimResults().splitToList(enterpriseFileUploadTypeConfig.getString("notCheckUploadFileType")));
      Boolean exactCheck = enterpriseFileUploadTypeConfig.getBoolean("supportUploadFileTypeExactCheck", false);
      fileUploadTypeConfig.setExactCheck(exactCheck);
      Boolean supportUploadFileTypeMatching=enterpriseFileUploadTypeConfig.getBoolean("supportUploadFileTypeMatching",false);
      fileUploadTypeConfig.setTypeMatching(supportUploadFileTypeMatching);
      fileUploadTypeConfigCache.put(enterpriseAccount,fileUploadTypeConfig);
    }
  }

  public void checkFileType(String ea, BufferedInputStream stream, long fileSize, String extension) throws BaseException {
    if (Strings.isNullOrEmpty(extension) || fileSize <= 0) {
      throw new BaseException(ErrorInfoEnum.INVALID_PARAMETER);
    }
    // 对扩展名进行处理,兼容如添加了.号或者.ext.ext等各种情况
    extension = getExtension(extension);

    // 获取配置 1. 优先获取企业配置 2. 其次获取通用配置 3.如果都没有配置 则不校验直接通过
    FileUploadTypeConfig config = fileUploadTypeConfigCache.getIfPresent(ea);
    if (config == null) {
      config = fileUploadTypeConfigCache.getIfPresent("common");
    }
    if (config != null) {
      List<String> supportTypeList = config.getSupportTypeList();
      // 如果未配置任何文件类型 则视为不限制文件类型
      if (supportTypeList != null && !supportTypeList.isEmpty()) {
        // 判断是否在允许上传的文件类型列表中
        if (!supportTypeList.contains(extension)) {
          throw new BaseException(ErrorInfoEnum.INVALID_EXTENSION);
        }
        // 判断文件大小是否大于最小检查文件大小 如果大于则进行文件类型检查
        // 判断文件类型是否在不需要检查的文件类型列表中 如果在则直接通过
        // 并且开启了精确检查
        if (fileSize >= MIN_CHECK_FILE_SIZE && !config.getNotCheckTypeList().contains(extension) && config.isExactCheck()) {
          try {
            // 判断文件类型是否匹配
            if (!isExtensionMatchingFile(stream, extension,config.isTypeMatching(),supportTypeList)) {
              throw new BaseException(ErrorInfoEnum.FILE_TYPE_MISMATCH);
            }
          } catch (IOException e) {
            throw new BaseException(ErrorInfoEnum.FILE_UPLOAD_IO_ERROR);
          }
        }
      }
    }
  }

  private String getExtension(String extension) {
    if (Strings.isNullOrEmpty(extension)) {
      return "";
    }
    if (extension.contains(".")) {
      return FilenameUtils.getExtension(extension);
    }
    return extension;
  }

  private boolean isExtensionMatchingFile(BufferedInputStream stream ,String extension,boolean typeMatching,List<String> supportTypeList) throws IOException {
    stream.mark(65);
    boolean matchResult;
    if (supportCustomCheckFileType.contains(extension)) {
      matchResult=isExtensionMatchingFileTByMagic(stream,extension);
    } else {
      String actualType = FileTypeUtil.getType(stream, "default." + extension);
      // 开启类型匹配则要求文件扩展名必须与实际类型一致
      if (typeMatching) {
        matchResult = extension.equalsIgnoreCase(actualType);
      } else {
        // 不开启类型匹配则要求实际类型在允许上传的文件类型列表中
        matchResult = supportTypeList.stream().anyMatch(supportType -> supportType.equalsIgnoreCase(actualType));
      }
    }
    stream.reset();
    return matchResult;
  }
  private boolean isExtensionMatchingFileTByMagic(BufferedInputStream stream,String extension) throws IOException {
    String fileStreamHexHead;
    if (extension.equalsIgnoreCase("heic")) {
      stream.skip(4);
      byte[] bytes = new byte[14];
      stream.read(bytes);
      fileStreamHexHead = HexUtil.encodeHexStr(bytes);
    }else {
      fileStreamHexHead = IoUtil.readHex64Upper(stream);
    }
    String fileMagic = MagicNumberFileTypeComparisonTable.get(extension);
    return fileStreamHexHead.toLowerCase().startsWith(fileMagic);
  }

}
