package com.facishare.fsc.utils;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.HexUtil;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Slf4j(topic = "MagicNumberFileUtil")
public class MagicNumberFileUtil {

  private MagicNumberFileUtil() {
  }

  private static final Set<String> fileTypeSet=new HashSet<>();
  private static final Map<String, String> MagicNumberFileTypeComparisonTable=new LinkedHashMap<>();

  static {
    init();
  }

  public static void init(){
    fileTypeSet.add("vsd");
    fileTypeSet.add("wps");
    fileTypeSet.add("et");
    fileTypeSet.add("dps");
    fileTypeSet.add("msg");
    fileTypeSet.add("msi");
    fileTypeSet.add("htm");
    fileTypeSet.add("html");
    fileTypeSet.add("xml");
    fileTypeSet.add("heic");
    fileTypeSet.add("api");
    fileTypeSet.add("com");
    fileTypeSet.add("dll");
    fileTypeSet.add("ocx");
    fileTypeSet.add("olb");
    fileTypeSet.add("imm");
    fileTypeSet.add("ime");
    fileTypeSet.add("dpl");
    fileTypeSet.add("drv");
    fileTypeSet.add("vxd");
    fileTypeSet.add("sys");
    fileTypeSet.add("vbx");
    fileTypeSet.add("ax");
    fileTypeSet.add("acm");
    fileTypeSet.add("flt");
    fileTypeSet.add("fon");
    fileTypeSet.add("scr");
    fileTypeSet.add("lrc");
    fileTypeSet.add("cpl");
    fileTypeSet.add("x32");
    fileTypeSet.add("pif");
    fileTypeSet.add("qts");
    fileTypeSet.add("qtx");
    fileTypeSet.add("386");
    fileTypeSet.add("bin");

    // 有序的map,靠前的优先级高
    MagicNumberFileTypeComparisonTable.put("2320","msi");
    MagicNumberFileTypeComparisonTable.put("d0cf11e0a1b11ae1","msg,vsd,wps,et,dps,msi,doc,dot,xls,xlt,xla,ppt,apr,ppa,pps,pot,sdw,db");
    MagicNumberFileTypeComparisonTable.put("d0cf11e0","dot,ppt,xla,ppa,pps,pot,msi,sdw,db");
    MagicNumberFileTypeComparisonTable.put("3c21444f4354","html");
    MagicNumberFileTypeComparisonTable.put("3c2144","htm");
    MagicNumberFileTypeComparisonTable.put("3c48544d4c3e","html");
    MagicNumberFileTypeComparisonTable.put("3c68746d6c3e","html");
    MagicNumberFileTypeComparisonTable.put("68746d6c3e","html");
    MagicNumberFileTypeComparisonTable.put("3c3f786d6c","xml");
    MagicNumberFileTypeComparisonTable.put("3c3f78","xml");
    MagicNumberFileTypeComparisonTable.put("fffe3c0052004f004f0054005300540055004200","xml");
    MagicNumberFileTypeComparisonTable.put("6674797068656963","heic");
    MagicNumberFileTypeComparisonTable.put("e93b03","com");
    MagicNumberFileTypeComparisonTable.put("4d5a900003000000","api,ax,flt");
    MagicNumberFileTypeComparisonTable.put("4d5aee","com");
    MagicNumberFileTypeComparisonTable.put("4d5a90","exe,dll,ocx,olb,imm,ime");
    MagicNumberFileTypeComparisonTable.put("4d5a50","dpl");
    MagicNumberFileTypeComparisonTable.put("4d5a16","drv");
    MagicNumberFileTypeComparisonTable.put("4d5a","dll,exe,drv,vxd,sys,ocx,vbx,com,ax,acm,flt,fon,scr,lrc,cpl,x32,pif,qts,qtx,cpl,386");
    MagicNumberFileTypeComparisonTable.put("edabeedb","rpm");
    MagicNumberFileTypeComparisonTable.put("424c4932323351","bin");
  }

  public static boolean isSupportByExt(String extension){
    return fileTypeSet.contains(extension);
  }

  /**
   * 根据文件十六进制头获取实际的文件类型
   * @param hexHead 文件十六进制头
   * @return 文件类型
   */
  private static Optional<String> getExtByMagic(String hexHead){
    for (Entry<String, String> entry : MagicNumberFileTypeComparisonTable.entrySet()) {
      String key = entry.getKey();
      String value = entry.getValue();
      if (hexHead.toLowerCase().startsWith(key)) {
        return Optional.of(value);
      }
    }
    return Optional.empty();
  }

  /**
   * 根据文件头和扩展名获取文件扩展名。
   *
   * @param hexHead 文件头的十六进制字符串
   * @param extension 文件扩展名
   * @return 包含文件扩展名的 Optional 对象，如果无法确定扩展名则返回 Optional.empty()
   */
  public static Optional<String> getExtByMagicAndExt(String hexHead, String extension) {
    log.info("hexHead:{},extension:{}",hexHead,extension);
    Optional<String> extOptional = getExtByMagic(hexHead);
    if (extOptional.isPresent()) {
      String extStr = extOptional.get();
      if (extStr.contains(",") && extStr.contains(extension)) {
        log.info("hexHead multiple hits,extStr:{},extension:{}",extStr,extension);
        return Optional.of(extension);
      }
      log.info("get ext by magic:{},extStr:{}",hexHead,extStr);
      return Optional.of(extStr);
    }
    log.warn("can not get ext by magic,hexHead:{},extension:{}",hexHead,extension);
    return Optional.empty();
  }

  /**
   * 从流中获取文件头的十六进制字符串。
   *
   * @param stream 输入流
   * @param extension 文件扩展名
   * @return 文件头的十六进制字符串
   * @throws IOException 读取流失败
   */
  private static String getHexHead(InputStream stream,String extension) throws IOException {
    if (extension.equalsIgnoreCase("heic")) {
      stream.skip(4);
      byte[] bytes = new byte[14];
      stream.read(bytes);
      return HexUtil.encodeHexStr(bytes);
    }else {
      return IoUtil.readHex64Upper(stream);
    }
  }

  /**
   * 从流中获取文件类型。
   *
   * @param stream 输入流
   * @param extension 文件扩展名
   * @return 包含文件类型的 Optional 对象，如果无法确定文件类型则返回 Optional.empty()
   */
  public static Optional<String> getExtByStream(InputStream stream,String extension){
    try {
      String hexHead = getHexHead(stream, extension);
      return getExtByMagicAndExt(hexHead, extension);
    } catch (IOException e) {
      return Optional.empty();
    }
  }

}