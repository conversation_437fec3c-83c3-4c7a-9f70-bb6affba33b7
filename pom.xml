<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fxiaoke.common</groupId>
    <artifactId>fxiaoke-parent-pom</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <packaging>pom</packaging>

  <groupId>com.facishare</groupId>
  <artifactId>fs-fsc</artifactId>
  <version>2.2.0-SNAPSHOT</version>

  <modules>
    <module>fs-fsc-api</module>
    <module>fs-fsc-cgi</module>
    <module>fs-fsc-common</module>
    <module>fs-fsc-core</module>
    <module>fs-fsc-provider</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    <encoding>UTF-8</encoding>
    <fs-customer-contract-core.version>1.0-SNAPSHOT</fs-customer-contract-core.version>
    <google-zxing.version>3.3.0</google-zxing.version>
    <kaptcha.version>0.0.9</kaptcha.version>
    <fs-common-web.version>2.0.0-SNAPSHOT</fs-common-web.version>
    <warehouse-batch-api.version>2.0.0-SNAPSHOT</warehouse-batch-api.version>
    <commons-fileupload.version>1.5</commons-fileupload.version>
    <curator.version>4.2.0</curator.version>

    <fsi.proxy.version>4.1.0-SNAPSHOT</fsi.proxy.version>
  </properties>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
              <goal>prepare-agent-integration</goal>
              <goal>report</goal>
              <goal>report-integration</goal>
            </goals>
            <configuration>
              <propertyName>jacocoArgLine</propertyName>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.sonarsource.scanner.maven</groupId>
        <artifactId>sonar-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!--suppress UnresolvedMavenProperty -->
          <argLine>
            ${jacocoArgLine}
            --add-opens java.base/java.lang.reflect=ALL-UNNAMED
            --add-opens java.base/java.lang=ALL-UNNAMED
            --add-opens java.base/java.math=ALL-UNNAMED
          </argLine>
          <systemPropertyVariables>
            <process.profile>fstest</process.profile>
          </systemPropertyVariables>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
