<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.facishare</groupId>
    <artifactId>fs-fsc</artifactId>
    <version>2.2.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>fs-fsc-core</artifactId>
  <dependencies>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsc-common</artifactId>
      <version>${project.parent.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>activation</artifactId>
          <groupId>javax.activation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-customer-contract-core</artifactId>
      <version>${fs-customer-contract-core.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>fs-common-web</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>warehouse-batch-api</artifactId>
      <version>${warehouse-batch-api.version}</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsi-proxy</artifactId>
      <version>${fsi.proxy.version}</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-spring</artifactId>
      <version>${resteasy.version}</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jaxrs</artifactId>
      <version>${resteasy.version}</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jackson2-provider</artifactId>
      <version>${resteasy.version}</version>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>fs-common-gray-release</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>jedis-spring-support</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fcp-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fcp-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>fs-jmimemagic-ext</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.16.1</version>
    </dependency>
    <dependency>
      <groupId>org.simplejavamail</groupId>
      <artifactId>outlook-message-parser</artifactId>
      <version>1.7.7</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>2.28.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>
  </dependencies>
</project>
