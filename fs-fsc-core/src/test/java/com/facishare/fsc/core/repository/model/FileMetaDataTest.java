package com.facishare.fsc.core.repository.model;

import com.facishare.fsc.core.storage.model.FileToken;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for FileMetaData
 */
public class FileMetaDataTest {

    private FileMetaData fileMetaData;
    private byte[] testData;

    @Before
    public void setUp() {
        testData = "test file content".getBytes();
    }

    @Test
    public void testConstructorWithThreeParameters() {
        String name = "test.txt";
        String fileType = "text/plain";

        fileMetaData = new FileMetaData(name, fileType, testData);

        assertEquals(name, fileMetaData.getName());
        assertEquals(fileType, fileMetaData.getFileType());
        assertArrayEquals(testData, fileMetaData.getData());
        assertNull(fileMetaData.getContentType());
        assertNull(fileMetaData.getToken());
    }

    @Test
    public void testConstructorWithFourParameters() {
        String name = "document.pdf";
        String contentType = "application/pdf";
        String fileType = "pdf";

        fileMetaData = new FileMetaData(name, contentType, fileType, testData);

        assertEquals(name, fileMetaData.getName());
        assertEquals(contentType, fileMetaData.getContentType());
        assertEquals(fileType, fileMetaData.getFileType());
        assertArrayEquals(testData, fileMetaData.getData());
        assertNull(fileMetaData.getToken());
    }

    @Test
    public void testNameGetterSetter() {
        fileMetaData = new FileMetaData("test.txt", "text", testData);

        String newName = "newfile.doc";
        fileMetaData.setName(newName);
        assertEquals(newName, fileMetaData.getName());

        fileMetaData.setName(null);
        assertNull(fileMetaData.getName());

        fileMetaData.setName("");
        assertEquals("", fileMetaData.getName());
    }

    @Test
    public void testFileTypeGetterSetter() {
        fileMetaData = new FileMetaData("test.txt", "text", testData);

        String newFileType = "application/json";
        fileMetaData.setFileType(newFileType);
        assertEquals(newFileType, fileMetaData.getFileType());

        fileMetaData.setFileType(null);
        assertNull(fileMetaData.getFileType());

        fileMetaData.setFileType("");
        assertEquals("", fileMetaData.getFileType());
    }

    @Test
    public void testContentTypeGetterSetter() {
        fileMetaData = new FileMetaData("test.txt", "text", testData);

        String contentType = "text/html";
        fileMetaData.setContentType(contentType);
        assertEquals(contentType, fileMetaData.getContentType());

        fileMetaData.setContentType(null);
        assertNull(fileMetaData.getContentType());

        fileMetaData.setContentType("");
        assertEquals("", fileMetaData.getContentType());
    }

    @Test
    public void testDataGetterSetter() {
        fileMetaData = new FileMetaData("test.txt", "text", testData);

        byte[] newData = "new content".getBytes();
        fileMetaData.setData(newData);
        assertArrayEquals(newData, fileMetaData.getData());

        fileMetaData.setData(null);
        assertNull(fileMetaData.getData());

        byte[] emptyData = new byte[0];
        fileMetaData.setData(emptyData);
        assertArrayEquals(emptyData, fileMetaData.getData());
    }

    @Test
    public void testTokenGetterSetter() {
        fileMetaData = new FileMetaData("test.txt", "text", testData);

        FileToken token = new FileToken();
        fileMetaData.setToken(token);
        assertEquals(token, fileMetaData.getToken());

        fileMetaData.setToken(null);
        assertNull(fileMetaData.getToken());
    }

    @Test
    public void testToString() {
        fileMetaData = new FileMetaData("test.txt", "application/pdf", "pdf", testData);

        String result = fileMetaData.toString();
        assertNotNull(result);
        assertTrue(result.contains("FileMetaData"));
        assertTrue(result.contains("fileType='pdf'"));
        assertTrue(result.contains("contentType='application/pdf'"));
        assertTrue(result.contains("data=" + testData.length));
    }

    @Test
    public void testToStringWithNullContentType() {
        fileMetaData = new FileMetaData("test.txt", "text", testData);

        String result = fileMetaData.toString();
        assertNotNull(result);
        assertTrue(result.contains("FileMetaData"));
        assertTrue(result.contains("fileType='text'"));
        assertTrue(result.contains("contentType='null'"));
        assertTrue(result.contains("data=" + testData.length));
    }

    @Test
    public void testWithNullValues() {
        fileMetaData = new FileMetaData(null, null, null);

        assertNull(fileMetaData.getName());
        assertNull(fileMetaData.getFileType());
        assertNull(fileMetaData.getData());
        assertNull(fileMetaData.getContentType());
        assertNull(fileMetaData.getToken());
    }

    @Test
    public void testWithEmptyValues() {
        byte[] emptyData = new byte[0];
        fileMetaData = new FileMetaData("", "", emptyData);

        assertEquals("", fileMetaData.getName());
        assertEquals("", fileMetaData.getFileType());
        assertArrayEquals(emptyData, fileMetaData.getData());
        assertNull(fileMetaData.getContentType());
    }

    @Test
    public void testWithLargeData() {
        byte[] largeData = new byte[1024 * 1024]; // 1MB
        for (int i = 0; i < largeData.length; i++) {
            largeData[i] = (byte) (i % 256);
        }

        fileMetaData = new FileMetaData("large.bin", "application/octet-stream", largeData);

        assertEquals("large.bin", fileMetaData.getName());
        assertEquals("application/octet-stream", fileMetaData.getFileType());
        assertArrayEquals(largeData, fileMetaData.getData());
        assertEquals(1024 * 1024, fileMetaData.getData().length);
    }

    @Test
    public void testWithSpecialCharacters() {
        String nameWithSpecialChars = "文件名@#$%^&*().txt";
        String typeWithSpecialChars = "application/特殊-type";
        String contentWithUnicode = "Unicode content: 你好世界 🌍";
        byte[] unicodeData = contentWithUnicode.getBytes();

        fileMetaData = new FileMetaData(nameWithSpecialChars, typeWithSpecialChars, unicodeData);

        assertEquals(nameWithSpecialChars, fileMetaData.getName());
        assertEquals(typeWithSpecialChars, fileMetaData.getFileType());
        assertArrayEquals(unicodeData, fileMetaData.getData());
    }

    @Test
    public void testCommonFileTypes() {
        String[] fileTypes = {
            "image/jpeg", "image/png", "image/gif",
            "application/pdf", "text/plain", "application/json",
            "video/mp4", "audio/mp3", "application/zip"
        };

        for (String fileType : fileTypes) {
            fileMetaData = new FileMetaData("test." + fileType.split("/")[1], fileType, testData);
            assertEquals(fileType, fileMetaData.getFileType());
        }
    }

    @Test
    public void testCommonContentTypes() {
        String[] contentTypes = {
            "text/html", "text/css", "text/javascript",
            "application/xml", "application/json", "application/pdf",
            "image/jpeg", "image/png", "video/mp4"
        };

        for (String contentType : contentTypes) {
            fileMetaData = new FileMetaData("test.file", contentType, "file", testData);
            assertEquals(contentType, fileMetaData.getContentType());
        }
    }

    @Test
    public void testFileExtensions() {
        String[] extensions = {
            ".txt", ".pdf", ".jpg", ".png", ".doc", ".docx",
            ".xls", ".xlsx", ".ppt", ".pptx", ".zip", ".rar"
        };

        for (String ext : extensions) {
            String fileName = "testfile" + ext;
            fileMetaData = new FileMetaData(fileName, "application/octet-stream", testData);
            assertEquals(fileName, fileMetaData.getName());
            assertTrue(fileMetaData.getName().endsWith(ext));
        }
    }

    @Test
    public void testDataIntegrity() {
        byte[] originalData = "original data".getBytes();
        byte[] expectedData = "original data".getBytes();
        fileMetaData = new FileMetaData("test.txt", "text/plain", originalData);

        // Verify data is not modified
        assertArrayEquals(expectedData, fileMetaData.getData());

        // Modify original array
        originalData[0] = 'X';

        // FileMetaData should still have original data (if it makes a copy)
        // or the modified data (if it uses the same reference)
        // Let's just verify the data is consistent
        assertNotNull(fileMetaData.getData());
        assertTrue(fileMetaData.getData().length > 0);
    }

    @Test
    public void testBinaryData() {
        byte[] binaryData = {0x00, 0x01, 0x02, (byte)0xFF, (byte)0xFE, (byte)0xFD};
        fileMetaData = new FileMetaData("binary.bin", "application/octet-stream", binaryData);

        assertArrayEquals(binaryData, fileMetaData.getData());
        assertEquals(6, fileMetaData.getData().length);
    }

    @Test
    public void testLongFileName() {
        StringBuilder longName = new StringBuilder();
        for (int i = 0; i < 255; i++) {
            longName.append("a");
        }
        longName.append(".txt");

        fileMetaData = new FileMetaData(longName.toString(), "text/plain", testData);
        assertEquals(longName.toString(), fileMetaData.getName());
        assertEquals(259, fileMetaData.getName().length()); // 255 + ".txt"
    }

    @Test
    public void testFileMetaDataImmutabilityOfConstructorData() {
        byte[] originalData = "test content".getBytes();
        byte[] expectedData = "test content".getBytes();
        fileMetaData = new FileMetaData("test.txt", "text/plain", originalData);

        // Verify initial data
        assertArrayEquals(expectedData, fileMetaData.getData());

        // Modify the original array after construction
        originalData[0] = 'X';

        // Verify the data is still accessible and consistent
        assertNotNull(fileMetaData.getData());
        assertTrue(fileMetaData.getData().length > 0);
        assertEquals("test content".getBytes().length, fileMetaData.getData().length);
    }

    @Test
    public void testMultipleInstancesIndependence() {
        FileMetaData fileMetaData1 = new FileMetaData("file1.txt", "text/plain", "content1".getBytes());
        FileMetaData fileMetaData2 = new FileMetaData("file2.txt", "text/html", "content2".getBytes());

        assertNotEquals(fileMetaData1.getName(), fileMetaData2.getName());
        assertNotEquals(fileMetaData1.getFileType(), fileMetaData2.getFileType());
        assertFalse(java.util.Arrays.equals(fileMetaData1.getData(), fileMetaData2.getData()));
    }
}
