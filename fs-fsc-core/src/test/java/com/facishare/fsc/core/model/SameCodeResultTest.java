package com.facishare.fsc.core.model;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for SameCodeResult model
 */
public class SameCodeResultTest {

    @Test
    public void testConstructor() {
        boolean isExist = true;
        long size = 1024L;
        String tempPath = "/temp/path";

        SameCodeResult result = new SameCodeResult(isExist, size, tempPath);

        assertEquals(isExist, result.IsExist);
        assertEquals(size, result.Size);
        assertEquals(tempPath, result.TempPath);
    }

    @Test
    public void testConstructorWithFalseIsExist() {
        boolean isExist = false;
        long size = 0L;
        String tempPath = null;

        SameCodeResult result = new SameCodeResult(isExist, size, tempPath);

        assertEquals(isExist, result.IsExist);
        assertEquals(size, result.Size);
        assertEquals(tempPath, result.TempPath);
    }

    @Test
    public void testConstructorWithLargeSize() {
        boolean isExist = true;
        long size = Long.MAX_VALUE;
        String tempPath = "/very/long/temp/path";

        SameCodeResult result = new SameCodeResult(isExist, size, tempPath);

        assertEquals(isExist, result.IsExist);
        assertEquals(size, result.Size);
        assertEquals(tempPath, result.TempPath);
    }

    @Test
    public void testConstructorWithZeroSize() {
        boolean isExist = false;
        long size = 0L;
        String tempPath = "";

        SameCodeResult result = new SameCodeResult(isExist, size, tempPath);

        assertFalse(result.IsExist);
        assertEquals(0L, result.Size);
        assertEquals("", result.TempPath);
    }

    @Test
    public void testConstructorWithNullTempPath() {
        boolean isExist = true;
        long size = 2048L;
        String tempPath = null;

        SameCodeResult result = new SameCodeResult(isExist, size, tempPath);

        assertTrue(result.IsExist);
        assertEquals(2048L, result.Size);
        assertNull(result.TempPath);
    }

    @Test
    public void testFieldsArePublic() {
        SameCodeResult result = new SameCodeResult(false, 100L, "/test");

        // Test that fields are directly accessible (public)
        result.IsExist = true;
        result.Size = 200L;
        result.TempPath = "/new/path";

        assertTrue(result.IsExist);
        assertEquals(200L, result.Size);
        assertEquals("/new/path", result.TempPath);
    }

    @Test
    public void testWithSpecialCharactersInPath() {
        boolean isExist = true;
        long size = 1024L;
        String tempPath = "/temp/path with spaces/and-special@chars#.tmp";

        SameCodeResult result = new SameCodeResult(isExist, size, tempPath);

        assertTrue(result.IsExist);
        assertEquals(1024L, result.Size);
        assertEquals("/temp/path with spaces/and-special@chars#.tmp", result.TempPath);
    }

    @Test
    public void testBooleanFieldValues() {
        // Test with true
        SameCodeResult result1 = new SameCodeResult(true, 100L, "/path1");
        assertTrue(result1.IsExist);

        // Test with false
        SameCodeResult result2 = new SameCodeResult(false, 200L, "/path2");
        assertFalse(result2.IsExist);
    }

    @Test
    public void testSizeFieldBoundaries() {
        // Test with minimum long value
        SameCodeResult result1 = new SameCodeResult(true, Long.MIN_VALUE, "/path1");
        assertEquals(Long.MIN_VALUE, result1.Size);

        // Test with maximum long value
        SameCodeResult result2 = new SameCodeResult(true, Long.MAX_VALUE, "/path2");
        assertEquals(Long.MAX_VALUE, result2.Size);

        // Test with zero
        SameCodeResult result3 = new SameCodeResult(true, 0L, "/path3");
        assertEquals(0L, result3.Size);
    }

    @Test
    public void testTypicalUseCases() {
        // File exists case
        SameCodeResult existingFile = new SameCodeResult(true, 1048576L, "/tmp/existing_file.dat");
        assertTrue(existingFile.IsExist);
        assertEquals(1048576L, existingFile.Size);
        assertEquals("/tmp/existing_file.dat", existingFile.TempPath);

        // File doesn't exist case
        SameCodeResult nonExistingFile = new SameCodeResult(false, 0L, null);
        assertFalse(nonExistingFile.IsExist);
        assertEquals(0L, nonExistingFile.Size);
        assertNull(nonExistingFile.TempPath);
    }
}
