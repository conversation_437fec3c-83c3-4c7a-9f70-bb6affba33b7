package com.facishare.fsc.core.processor.model;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for CompressFileDownloadEvent
 */
public class CompressFileDownloadEventTest {

    @Test
    public void testDefaultConstructor() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        assertNotNull(event);
        assertNull(event.getCode());
        assertNull(event.getMessage());
        assertNull(event.getKey());
        assertNull(event.getDownloadURL());
    }

    @Test
    public void testSettersAndGetters() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        
        Integer code = 0;
        String message = "Success";
        String key = "test_key_123";
        String downloadURL = "http://example.com/download/file.zip";

        event.setCode(code);
        event.setMessage(message);
        event.setKey(key);
        event.setDownloadURL(downloadURL);

        assertEquals(code, event.getCode());
        assertEquals(message, event.getMessage());
        assertEquals(key, event.getKey());
        assertEquals(downloadURL, event.getDownloadURL());
    }

    @Test
    public void testToJson() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        event.setCode(0);
        event.setMessage("Success");
        event.setKey("test_key");
        event.setDownloadURL("http://example.com/download");

        String json = event.toJson();
        assertNotNull(json);
        assertTrue(json.contains("\"code\":0"));
        assertTrue(json.contains("\"message\":\"Success\""));
        assertTrue(json.contains("\"key\":\"test_key\""));
        assertTrue(json.contains("\"downloadURL\":\"http://example.com/download\""));
    }

    @Test
    public void testFromJson() {
        String json = "{\"code\":1,\"message\":\"Error\",\"key\":\"error_key\",\"downloadURL\":\"http://error.com\"}";
        
        CompressFileDownloadEvent event = CompressFileDownloadEvent.fromJson(json);
        
        assertNotNull(event);
        assertEquals(Integer.valueOf(1), event.getCode());
        assertEquals("Error", event.getMessage());
        assertEquals("error_key", event.getKey());
        assertEquals("http://error.com", event.getDownloadURL());
    }

    @Test
    public void testFromJsonWithNullValues() {
        String json = "{\"code\":null,\"message\":null,\"key\":null,\"downloadURL\":null}";
        
        CompressFileDownloadEvent event = CompressFileDownloadEvent.fromJson(json);
        
        assertNotNull(event);
        assertNull(event.getCode());
        assertNull(event.getMessage());
        assertNull(event.getKey());
        assertNull(event.getDownloadURL());
    }

    @Test
    public void testFromJsonWithEmptyJson() {
        String json = "{}";
        
        CompressFileDownloadEvent event = CompressFileDownloadEvent.fromJson(json);
        
        assertNotNull(event);
        assertNull(event.getCode());
        assertNull(event.getMessage());
        assertNull(event.getKey());
        assertNull(event.getDownloadURL());
    }

    @Test
    public void testToJsonAndFromJsonRoundTrip() {
        CompressFileDownloadEvent originalEvent = new CompressFileDownloadEvent();
        originalEvent.setCode(200);
        originalEvent.setMessage("Download completed");
        originalEvent.setKey("download_key_456");
        originalEvent.setDownloadURL("https://cdn.example.com/files/archive.zip");

        String json = originalEvent.toJson();
        CompressFileDownloadEvent deserializedEvent = CompressFileDownloadEvent.fromJson(json);

        assertEquals(originalEvent.getCode(), deserializedEvent.getCode());
        assertEquals(originalEvent.getMessage(), deserializedEvent.getMessage());
        assertEquals(originalEvent.getKey(), deserializedEvent.getKey());
        assertEquals(originalEvent.getDownloadURL(), deserializedEvent.getDownloadURL());
    }

    @Test
    public void testToString() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        event.setCode(404);
        event.setMessage("File not found");
        event.setKey("not_found_key");
        event.setDownloadURL("http://notfound.com");

        String toString = event.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("CompressFileDownloadEvent{"));
        assertTrue(toString.contains("code=404"));
        assertTrue(toString.contains("message=File not found"));
        assertTrue(toString.contains("key=not_found_key"));
        assertTrue(toString.contains("downloadURL=http://notfound.com"));
    }

    @Test
    public void testToStringWithNullValues() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();

        String toString = event.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("CompressFileDownloadEvent{"));
        assertTrue(toString.contains("code=null"));
        assertTrue(toString.contains("message=null"));
        assertTrue(toString.contains("key=null"));
        assertTrue(toString.contains("downloadURL=null"));
    }

    @Test
    public void testWithDifferentCodes() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        
        // Test success code
        event.setCode(0);
        assertEquals(Integer.valueOf(0), event.getCode());
        
        // Test error codes
        event.setCode(1);
        assertEquals(Integer.valueOf(1), event.getCode());
        
        event.setCode(500);
        assertEquals(Integer.valueOf(500), event.getCode());
        
        // Test negative code
        event.setCode(-1);
        assertEquals(Integer.valueOf(-1), event.getCode());
    }

    @Test
    public void testWithLongMessage() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        
        StringBuilder longMessage = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longMessage.append("This is a very long error message. ");
        }
        
        event.setMessage(longMessage.toString());
        assertEquals(longMessage.toString(), event.getMessage());
    }

    @Test
    public void testWithSpecialCharactersInMessage() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        
        String specialMessage = "Error with special chars: !@#$%^&*()_+-={}[]|\\:;\"'<>?,./";
        event.setMessage(specialMessage);
        assertEquals(specialMessage, event.getMessage());
    }

    @Test
    public void testWithUnicodeInMessage() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        
        String unicodeMessage = "错误信息: 文件下载失败 🚫";
        event.setMessage(unicodeMessage);
        assertEquals(unicodeMessage, event.getMessage());
    }

    @Test
    public void testWithComplexDownloadURL() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        
        String complexURL = "https://cdn.example.com/files/download?token=abc123&expires=1234567890&signature=xyz789";
        event.setDownloadURL(complexURL);
        assertEquals(complexURL, event.getDownloadURL());
    }

    @Test
    public void testWithEmptyStrings() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        
        event.setMessage("");
        event.setKey("");
        event.setDownloadURL("");
        
        assertEquals("", event.getMessage());
        assertEquals("", event.getKey());
        assertEquals("", event.getDownloadURL());
    }

    @Test
    public void testJsonSerializationWithSpecialCharacters() {
        CompressFileDownloadEvent event = new CompressFileDownloadEvent();
        event.setCode(0);
        event.setMessage("Success with \"quotes\" and \\backslashes\\");
        event.setKey("key_with_spaces and-dashes");
        event.setDownloadURL("http://example.com/path with spaces/file.zip");

        String json = event.toJson();
        CompressFileDownloadEvent deserializedEvent = CompressFileDownloadEvent.fromJson(json);

        assertEquals(event.getCode(), deserializedEvent.getCode());
        assertEquals(event.getMessage(), deserializedEvent.getMessage());
        assertEquals(event.getKey(), deserializedEvent.getKey());
        assertEquals(event.getDownloadURL(), deserializedEvent.getDownloadURL());
    }

    @Test
    public void testTypicalUseCases() {
        // Success case
        CompressFileDownloadEvent successEvent = new CompressFileDownloadEvent();
        successEvent.setCode(0);
        successEvent.setMessage("File compressed successfully");
        successEvent.setKey("batch_download_123");
        successEvent.setDownloadURL("https://storage.example.com/compressed/batch_123.zip");
        
        assertEquals(Integer.valueOf(0), successEvent.getCode());
        assertNotNull(successEvent.getMessage());
        assertNotNull(successEvent.getKey());
        assertNotNull(successEvent.getDownloadURL());
        
        // Error case
        CompressFileDownloadEvent errorEvent = new CompressFileDownloadEvent();
        errorEvent.setCode(3);
        errorEvent.setMessage("Compression failed: insufficient storage space");
        errorEvent.setKey("batch_download_456");
        errorEvent.setDownloadURL(null);
        
        assertEquals(Integer.valueOf(3), errorEvent.getCode());
        assertNotNull(errorEvent.getMessage());
        assertNotNull(errorEvent.getKey());
        assertNull(errorEvent.getDownloadURL());
    }
}
