package com.facishare.fsc.core.storage.model;

import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Date;

/**
 * Test class for ShareFileToken
 */
public class ShareFileTokenTest {

    private ShareFileToken shareFileToken;

    @Before
    public void setUp() {
        shareFileToken = new ShareFileToken();
    }

    @Test
    public void testDefaultConstructor() {
        assertNotNull(shareFileToken);
        assertNull(shareFileToken.get_id());
        assertNull(shareFileToken.getEA());
        assertNull(shareFileToken.getEaYearMonth());
        assertNull(shareFileToken.getFileToken());
        assertNull(shareFileToken.getFileType());
        assertNull(shareFileToken.getFileName());
        assertNull(shareFileToken.getFilePath());
        assertNull(shareFileToken.getCreateTime());
        assertNull(shareFileToken.getWarehouseType());
    }

    @Test
    public void testInheritance() {
        assertTrue(shareFileToken instanceof FileToken);
    }

    @Test
    public void testIdGetterSetter() {
        ObjectId objectId = new ObjectId();
        shareFileToken.set_id(objectId);
        assertEquals(objectId, shareFileToken.get_id());
        
        shareFileToken.set_id(null);
        assertNull(shareFileToken.get_id());
    }

    @Test
    public void testEAGetterSetter() {
        String ea = "test_enterprise";
        shareFileToken.setEA(ea);
        assertEquals(ea, shareFileToken.getEA());
        
        shareFileToken.setEA(null);
        assertNull(shareFileToken.getEA());
        
        shareFileToken.setEA("");
        assertEquals("", shareFileToken.getEA());
    }

    @Test
    public void testEaYearMonthGetterSetter() {
        String eaYearMonth = "202312";
        shareFileToken.setEaYearMonth(eaYearMonth);
        assertEquals(eaYearMonth, shareFileToken.getEaYearMonth());
        
        shareFileToken.setEaYearMonth(null);
        assertNull(shareFileToken.getEaYearMonth());
        
        shareFileToken.setEaYearMonth("");
        assertEquals("", shareFileToken.getEaYearMonth());
    }

    @Test
    public void testFileTokenGetterSetter() {
        String token = "abc123def456";
        shareFileToken.setFileToken(token);
        assertEquals(token, shareFileToken.getFileToken());
        
        shareFileToken.setFileToken(null);
        assertNull(shareFileToken.getFileToken());
        
        shareFileToken.setFileToken("");
        assertEquals("", shareFileToken.getFileToken());
    }

    @Test
    public void testFileTypeGetterSetter() {
        String fileType = "image/jpeg";
        shareFileToken.setFileType(fileType);
        assertEquals(fileType, shareFileToken.getFileType());
        
        shareFileToken.setFileType(null);
        assertNull(shareFileToken.getFileType());
        
        shareFileToken.setFileType("");
        assertEquals("", shareFileToken.getFileType());
    }

    @Test
    public void testFileNameGetterSetter() {
        String fileName = "test_file.jpg";
        shareFileToken.setFileName(fileName);
        assertEquals(fileName, shareFileToken.getFileName());
        
        shareFileToken.setFileName(null);
        assertNull(shareFileToken.getFileName());
        
        shareFileToken.setFileName("");
        assertEquals("", shareFileToken.getFileName());
    }

    @Test
    public void testFilePathGetterSetter() {
        String filePath = "/path/to/file.jpg";
        shareFileToken.setFilePath(filePath);
        assertEquals(filePath, shareFileToken.getFilePath());
        
        shareFileToken.setFilePath(null);
        assertNull(shareFileToken.getFilePath());
        
        shareFileToken.setFilePath("");
        assertEquals("", shareFileToken.getFilePath());
    }

    @Test
    public void testCreateTimeGetterSetter() {
        Date createTime = new Date();
        shareFileToken.setCreateTime(createTime);
        assertEquals(createTime, shareFileToken.getCreateTime());
        
        shareFileToken.setCreateTime(null);
        assertNull(shareFileToken.getCreateTime());
    }

    @Test
    public void testWarehouseTypeGetterSetter() {
        String warehouseType = "A";
        shareFileToken.setWarehouseType(warehouseType);
        assertEquals(warehouseType, shareFileToken.getWarehouseType());
        
        shareFileToken.setWarehouseType(null);
        assertNull(shareFileToken.getWarehouseType());
        
        shareFileToken.setWarehouseType("");
        assertEquals("", shareFileToken.getWarehouseType());
    }

    @Test
    public void testAllFieldsSetAndGet() {
        ObjectId objectId = new ObjectId();
        String ea = "enterprise_account";
        String eaYearMonth = "202312";
        String token = "share_token_123";
        String fileType = "application/pdf";
        String fileName = "document.pdf";
        String filePath = "/documents/document.pdf";
        Date createTime = new Date();
        String warehouseType = "N";

        shareFileToken.set_id(objectId);
        shareFileToken.setEA(ea);
        shareFileToken.setEaYearMonth(eaYearMonth);
        shareFileToken.setFileToken(token);
        shareFileToken.setFileType(fileType);
        shareFileToken.setFileName(fileName);
        shareFileToken.setFilePath(filePath);
        shareFileToken.setCreateTime(createTime);
        shareFileToken.setWarehouseType(warehouseType);

        assertEquals(objectId, shareFileToken.get_id());
        assertEquals(ea, shareFileToken.getEA());
        assertEquals(eaYearMonth, shareFileToken.getEaYearMonth());
        assertEquals(token, shareFileToken.getFileToken());
        assertEquals(fileType, shareFileToken.getFileType());
        assertEquals(fileName, shareFileToken.getFileName());
        assertEquals(filePath, shareFileToken.getFilePath());
        assertEquals(createTime, shareFileToken.getCreateTime());
        assertEquals(warehouseType, shareFileToken.getWarehouseType());
    }

    @Test
    public void testWithSpecialCharacters() {
        String ea = "企业@账号#123";
        String fileName = "文件名 with spaces & special chars!.txt";
        String filePath = "/路径/with spaces/and-special@chars#/";

        shareFileToken.setEA(ea);
        shareFileToken.setFileName(fileName);
        shareFileToken.setFilePath(filePath);

        assertEquals(ea, shareFileToken.getEA());
        assertEquals(fileName, shareFileToken.getFileName());
        assertEquals(filePath, shareFileToken.getFilePath());
    }

    @Test
    public void testWithLongStrings() {
        StringBuilder longString = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longString.append("a");
        }
        String longValue = longString.toString();

        shareFileToken.setEA(longValue);
        shareFileToken.setFileName(longValue);
        shareFileToken.setFilePath(longValue);

        assertEquals(longValue, shareFileToken.getEA());
        assertEquals(longValue, shareFileToken.getFileName());
        assertEquals(longValue, shareFileToken.getFilePath());
    }

    @Test
    public void testDateHandling() {
        // Test with current date
        Date now = new Date();
        shareFileToken.setCreateTime(now);
        assertEquals(now, shareFileToken.getCreateTime());

        // Test with past date
        Date pastDate = new Date(System.currentTimeMillis() - 86400000); // 1 day ago
        shareFileToken.setCreateTime(pastDate);
        assertEquals(pastDate, shareFileToken.getCreateTime());

        // Test with future date
        Date futureDate = new Date(System.currentTimeMillis() + 86400000); // 1 day from now
        shareFileToken.setCreateTime(futureDate);
        assertEquals(futureDate, shareFileToken.getCreateTime());
    }

    @Test
    public void testObjectIdHandling() {
        // Test with new ObjectId
        ObjectId id1 = new ObjectId();
        shareFileToken.set_id(id1);
        assertEquals(id1, shareFileToken.get_id());

        // Test with another ObjectId
        ObjectId id2 = new ObjectId();
        shareFileToken.set_id(id2);
        assertEquals(id2, shareFileToken.get_id());
        assertNotEquals(id1, shareFileToken.get_id());
    }

    @Test
    public void testCommonFileTypes() {
        String[] fileTypes = {
            "image/jpeg", "image/png", "image/gif",
            "application/pdf", "text/plain", "application/json",
            "video/mp4", "audio/mp3"
        };

        for (String fileType : fileTypes) {
            shareFileToken.setFileType(fileType);
            assertEquals(fileType, shareFileToken.getFileType());
        }
    }

    @Test
    public void testCommonWarehouseTypes() {
        String[] warehouseTypes = {"A", "N"};

        for (String warehouseType : warehouseTypes) {
            shareFileToken.setWarehouseType(warehouseType);
            assertEquals(warehouseType, shareFileToken.getWarehouseType());
        }
    }

    @Test
    public void testEaYearMonthFormats() {
        String[] yearMonths = {
            "202301", "202312", "202401", "202412",
            "2023-01", "2023-12", "23-01", "23-12"
        };

        for (String yearMonth : yearMonths) {
            shareFileToken.setEaYearMonth(yearMonth);
            assertEquals(yearMonth, shareFileToken.getEaYearMonth());
        }
    }

    @Test
    public void testInheritedMethodsWork() {
        // Test that inherited methods work correctly
        String ea = "test_ea";
        String eaYearMonth = "202312";
        String fileType = "application/zip";
        String filePath = "/shares/archive.zip";
        String warehouseType = "A";

        shareFileToken.setEA(ea);
        shareFileToken.setEaYearMonth(eaYearMonth);
        shareFileToken.setFileType(fileType);
        shareFileToken.setFilePath(filePath);
        shareFileToken.setWarehouseType(warehouseType);

        assertEquals(ea, shareFileToken.getEA());
        assertEquals(eaYearMonth, shareFileToken.getEaYearMonth());
        assertEquals(fileType, shareFileToken.getFileType());
        assertEquals(filePath, shareFileToken.getFilePath());
        assertEquals(warehouseType, shareFileToken.getWarehouseType());
    }

    @Test
    public void testTypicalShareScenarios() {
        // Public share scenario
        ShareFileToken publicShare = new ShareFileToken();
        publicShare.setEA("public_ea");
        publicShare.setFileToken("public_share_token_123");
        publicShare.setFileName("public_document.pdf");
        publicShare.setFilePath("/public/shares/document.pdf");
        publicShare.setFileType("application/pdf");
        publicShare.setCreateTime(new Date());
        publicShare.setWarehouseType("A");
        
        assertEquals("public_ea", publicShare.getEA());
        assertEquals("public_share_token_123", publicShare.getFileToken());
        assertEquals("application/pdf", publicShare.getFileType());
        
        // Private share scenario
        ShareFileToken privateShare = new ShareFileToken();
        privateShare.setEA("private_ea");
        privateShare.setFileToken("private_share_token_456");
        privateShare.setFileName("confidential.docx");
        privateShare.setFilePath("/private/shares/confidential.docx");
        privateShare.setFileType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        privateShare.setCreateTime(new Date());
        privateShare.setWarehouseType("N");
        
        assertEquals("private_ea", privateShare.getEA());
        assertEquals("private_share_token_456", privateShare.getFileToken());
        assertEquals("N", privateShare.getWarehouseType());
    }
}
