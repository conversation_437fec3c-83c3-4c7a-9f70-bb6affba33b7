package com.facishare.fsc.core.repository.model;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for ViewRichTextResult
 */
public class ViewRichTextResultTest {

    private ViewRichTextResult viewRichTextResult;

    @Test
    public void testConstructorWithValidValues() {
        String title = "Test Title";
        String richText = "Test Rich Text Content";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
    }

    @Test
    public void testConstructorWithNullValues() {
        viewRichTextResult = new ViewRichTextResult(null, null);
        
        assertNull(viewRichTextResult.getTitle());
        assertNull(viewRichTextResult.getRichText());
    }

    @Test
    public void testConstructorWithEmptyValues() {
        viewRichTextResult = new ViewRichTextResult("", "");
        
        assertEquals("", viewRichTextResult.getTitle());
        assertEquals("", viewRichTextResult.getRichText());
    }

    @Test
    public void testTitleGetterSetter() {
        viewRichTextResult = new ViewRichTextResult("Initial Title", "Initial Content");
        
        String newTitle = "Updated Title";
        viewRichTextResult.setTitle(newTitle);
        assertEquals(newTitle, viewRichTextResult.getTitle());
        
        viewRichTextResult.setTitle(null);
        assertNull(viewRichTextResult.getTitle());
        
        viewRichTextResult.setTitle("");
        assertEquals("", viewRichTextResult.getTitle());
    }

    @Test
    public void testRichTextGetterSetter() {
        viewRichTextResult = new ViewRichTextResult("Test Title", "Initial Content");
        
        String newRichText = "Updated Rich Text Content";
        viewRichTextResult.setRichText(newRichText);
        assertEquals(newRichText, viewRichTextResult.getRichText());
        
        viewRichTextResult.setRichText(null);
        assertNull(viewRichTextResult.getRichText());
        
        viewRichTextResult.setRichText("");
        assertEquals("", viewRichTextResult.getRichText());
    }

    @Test
    public void testWithHtmlContent() {
        String title = "HTML Document";
        String htmlContent = "<html><body><h1>Hello World</h1><p>This is a paragraph.</p></body></html>";
        
        viewRichTextResult = new ViewRichTextResult(title, htmlContent);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(htmlContent, viewRichTextResult.getRichText());
    }

    @Test
    public void testWithSpecialCharacters() {
        String title = "特殊字符标题 @#$%^&*()";
        String richText = "Rich text with special chars: 你好世界 🌍 & symbols: <>&\"'";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
    }

    @Test
    public void testWithLongContent() {
        StringBuilder longTitle = new StringBuilder();
        StringBuilder longContent = new StringBuilder();
        
        for (int i = 0; i < 1000; i++) {
            longTitle.append("Title ");
            longContent.append("Content line ").append(i).append(". ");
        }
        
        String title = longTitle.toString();
        String content = longContent.toString();
        
        viewRichTextResult = new ViewRichTextResult(title, content);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(content, viewRichTextResult.getRichText());
        assertTrue(viewRichTextResult.getTitle().length() > 5000);
        assertTrue(viewRichTextResult.getRichText().length() > 10000);
    }

    @Test
    public void testWithWhitespaceContent() {
        String title = "   Title with spaces   ";
        String richText = "\n\t  Rich text with whitespace  \n\t";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
    }

    @Test
    public void testWithNewlineCharacters() {
        String title = "Multi\nLine\nTitle";
        String richText = "Line 1\nLine 2\nLine 3\n";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
        assertTrue(viewRichTextResult.getTitle().contains("\n"));
        assertTrue(viewRichTextResult.getRichText().contains("\n"));
    }

    @Test
    public void testWithTabCharacters() {
        String title = "Title\twith\ttabs";
        String richText = "Content\twith\ttab\tcharacters";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
        assertTrue(viewRichTextResult.getTitle().contains("\t"));
        assertTrue(viewRichTextResult.getRichText().contains("\t"));
    }

    @Test
    public void testWithJsonContent() {
        String title = "JSON Document";
        String jsonContent = "{\"name\":\"John\",\"age\":30,\"city\":\"New York\"}";
        
        viewRichTextResult = new ViewRichTextResult(title, jsonContent);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(jsonContent, viewRichTextResult.getRichText());
    }

    @Test
    public void testWithXmlContent() {
        String title = "XML Document";
        String xmlContent = "<?xml version=\"1.0\"?><root><item>value</item></root>";
        
        viewRichTextResult = new ViewRichTextResult(title, xmlContent);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(xmlContent, viewRichTextResult.getRichText());
    }

    @Test
    public void testWithMarkdownContent() {
        String title = "Markdown Document";
        String markdownContent = "# Header\n\n**Bold text** and *italic text*\n\n- List item 1\n- List item 2";
        
        viewRichTextResult = new ViewRichTextResult(title, markdownContent);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(markdownContent, viewRichTextResult.getRichText());
    }

    @Test
    public void testWithEscapedCharacters() {
        String title = "Title with \"quotes\" and 'apostrophes'";
        String richText = "Content with \\backslashes\\ and /forward/slashes/";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
    }

    @Test
    public void testMultipleInstancesIndependence() {
        ViewRichTextResult result1 = new ViewRichTextResult("Title 1", "Content 1");
        ViewRichTextResult result2 = new ViewRichTextResult("Title 2", "Content 2");
        
        assertNotEquals(result1.getTitle(), result2.getTitle());
        assertNotEquals(result1.getRichText(), result2.getRichText());
        
        // Modify one instance
        result1.setTitle("Modified Title 1");
        result1.setRichText("Modified Content 1");
        
        // Other instance should remain unchanged
        assertEquals("Title 2", result2.getTitle());
        assertEquals("Content 2", result2.getRichText());
    }

    @Test
    public void testSettersOverwritePreviousValues() {
        viewRichTextResult = new ViewRichTextResult("Original Title", "Original Content");
        
        assertEquals("Original Title", viewRichTextResult.getTitle());
        assertEquals("Original Content", viewRichTextResult.getRichText());
        
        viewRichTextResult.setTitle("New Title");
        viewRichTextResult.setRichText("New Content");
        
        assertEquals("New Title", viewRichTextResult.getTitle());
        assertEquals("New Content", viewRichTextResult.getRichText());
    }

    @Test
    public void testWithUnicodeContent() {
        String title = "Unicode Title: 中文标题 العربية русский";
        String richText = "Unicode content: 日本語 한국어 ελληνικά עברית";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
    }

    @Test
    public void testWithEmojis() {
        String title = "Title with emojis 😀 🎉 🚀";
        String richText = "Content with emojis: 👍 ❤️ 🌟 🎯";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
    }

    @Test
    public void testWithNumericContent() {
        String title = "123456789";
        String richText = "Numbers: 0.123 -456 1.23e-4 +789";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
    }

    @Test
    public void testWithMixedContent() {
        String title = "Mixed Content Title 123 @#$";
        String richText = "<p>HTML with <b>bold</b> and numbers 123 and symbols @#$%</p>";
        
        viewRichTextResult = new ViewRichTextResult(title, richText);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(richText, viewRichTextResult.getRichText());
    }

    @Test
    public void testParameterOrder() {
        // Test that constructor parameters are in correct order: title, richText
        String title = "This is the title";
        String content = "This is the content";
        
        viewRichTextResult = new ViewRichTextResult(title, content);
        
        assertEquals(title, viewRichTextResult.getTitle());
        assertEquals(content, viewRichTextResult.getRichText());
        
        // Verify they're not swapped
        assertNotEquals(content, viewRichTextResult.getTitle());
        assertNotEquals(title, viewRichTextResult.getRichText());
    }

    @Test
    public void testTypicalUseCases() {
        // Blog post scenario
        ViewRichTextResult blogPost = new ViewRichTextResult(
            "How to Write Unit Tests",
            "<h1>Introduction</h1><p>Unit testing is important...</p>"
        );
        assertEquals("How to Write Unit Tests", blogPost.getTitle());
        assertTrue(blogPost.getRichText().contains("<h1>"));
        
        // Email scenario
        ViewRichTextResult email = new ViewRichTextResult(
            "Meeting Reminder",
            "Dear team,\n\nPlease remember our meeting tomorrow at 2 PM.\n\nBest regards"
        );
        assertEquals("Meeting Reminder", email.getTitle());
        assertTrue(email.getRichText().contains("Dear team"));
        
        // Documentation scenario
        ViewRichTextResult documentation = new ViewRichTextResult(
            "API Documentation",
            "## Authentication\n\nUse Bearer token in Authorization header."
        );
        assertEquals("API Documentation", documentation.getTitle());
        assertTrue(documentation.getRichText().contains("Bearer token"));
    }
}
