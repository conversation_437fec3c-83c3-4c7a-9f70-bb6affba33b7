package com.facishare.fsc.core.model;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for Avatar model
 */
public class AvatarTest {

    @Test
    public void testConstructor() {
        String path = "/avatar/path";
        char index = 'A';
        boolean avatar = true;

        Avatar avatarObj = new Avatar(path, index, avatar);

        assertEquals(path, avatarObj.getPath());
        assertEquals(index, avatarObj.getIndex());
        assertEquals(avatar, avatarObj.isAvatar());
    }

    @Test
    public void testGetters() {
        String path = "/test/avatar.jpg";
        char index = 'B';
        boolean avatar = false;

        Avatar avatarObj = new Avatar(path, index, avatar);

        assertEquals(path, avatarObj.getPath());
        assertEquals(index, avatarObj.getIndex());
        assertEquals(avatar, avatarObj.isAvatar());
    }

    @Test
    public void testToString() {
        String path = "/avatar/test.png";
        char index = 'C';
        boolean avatar = true;

        Avatar avatarObj = new Avatar(path, index, avatar);
        String toString = avatarObj.toString();

        assertNotNull(toString);
        assertTrue(toString.contains("Avatar{"));
        assertTrue(toString.contains("path='/avatar/test.png'"));
        assertTrue(toString.contains("index=C"));
        assertTrue(toString.contains("avatar=true"));
    }

    @Test
    public void testWithNullPath() {
        char index = 'D';
        boolean avatar = false;

        Avatar avatarObj = new Avatar(null, index, avatar);

        assertNull(avatarObj.getPath());
        assertEquals(index, avatarObj.getIndex());
        assertEquals(avatar, avatarObj.isAvatar());
    }

    @Test
    public void testWithEmptyPath() {
        String path = "";
        char index = 'E';
        boolean avatar = true;

        Avatar avatarObj = new Avatar(path, index, avatar);

        assertEquals("", avatarObj.getPath());
        assertEquals(index, avatarObj.getIndex());
        assertTrue(avatarObj.isAvatar());
    }

    @Test
    public void testWithSpecialCharacters() {
        String path = "/path/with spaces/and-special@chars#.jpg";
        char index = '@';
        boolean avatar = false;

        Avatar avatarObj = new Avatar(path, index, avatar);

        assertEquals(path, avatarObj.getPath());
        assertEquals(index, avatarObj.getIndex());
        assertFalse(avatarObj.isAvatar());
    }

    @Test
    public void testWithDifferentCharacters() {
        // Test with lowercase letter
        Avatar avatar1 = new Avatar("/path1", 'a', true);
        assertEquals('a', avatar1.getIndex());

        // Test with uppercase letter
        Avatar avatar2 = new Avatar("/path2", 'Z', false);
        assertEquals('Z', avatar2.getIndex());

        // Test with digit
        Avatar avatar3 = new Avatar("/path3", '9', true);
        assertEquals('9', avatar3.getIndex());

        // Test with special character
        Avatar avatar4 = new Avatar("/path4", '$', false);
        assertEquals('$', avatar4.getIndex());
    }

    @Test
    public void testBooleanValues() {
        // Test with true
        Avatar avatar1 = new Avatar("/path1", 'A', true);
        assertTrue(avatar1.isAvatar());

        // Test with false
        Avatar avatar2 = new Avatar("/path2", 'B', false);
        assertFalse(avatar2.isAvatar());
    }

    @Test
    public void testImmutability() {
        String originalPath = "/original/path";
        char originalIndex = 'X';
        boolean originalAvatar = true;

        Avatar avatarObj = new Avatar(originalPath, originalIndex, originalAvatar);

        // Verify that the object returns the same values
        assertEquals(originalPath, avatarObj.getPath());
        assertEquals(originalIndex, avatarObj.getIndex());
        assertEquals(originalAvatar, avatarObj.isAvatar());

        // Since Avatar is immutable (final fields), we can't change the values
        // This test verifies that the getters consistently return the same values
        assertEquals(originalPath, avatarObj.getPath());
        assertEquals(originalIndex, avatarObj.getIndex());
        assertEquals(originalAvatar, avatarObj.isAvatar());
    }

    @Test
    public void testToStringWithNullPath() {
        Avatar avatarObj = new Avatar(null, 'N', false);
        String toString = avatarObj.toString();

        assertNotNull(toString);
        assertTrue(toString.contains("Avatar{"));
        assertTrue(toString.contains("path='null'"));
        assertTrue(toString.contains("index=N"));
        assertTrue(toString.contains("avatar=false"));
    }

    @Test
    public void testToStringWithEmptyPath() {
        Avatar avatarObj = new Avatar("", 'E', true);
        String toString = avatarObj.toString();

        assertNotNull(toString);
        assertTrue(toString.contains("Avatar{"));
        assertTrue(toString.contains("path=''"));
        assertTrue(toString.contains("index=E"));
        assertTrue(toString.contains("avatar=true"));
    }

    @Test
    public void testToStringFormat() {
        Avatar avatarObj = new Avatar("/test", 'T', false);
        String toString = avatarObj.toString();

        // Verify the exact format
        String expected = "Avatar{path='/test', index=T, avatar=false}";
        assertEquals(expected, toString);
    }

    @Test
    public void testWithLongPath() {
        StringBuilder longPath = new StringBuilder("/very/long/path");
        for (int i = 0; i < 100; i++) {
            longPath.append("/directory").append(i);
        }
        longPath.append("/file.jpg");

        Avatar avatarObj = new Avatar(longPath.toString(), 'L', true);
        assertEquals(longPath.toString(), avatarObj.getPath());
        assertEquals('L', avatarObj.getIndex());
        assertTrue(avatarObj.isAvatar());
    }

    @Test
    public void testWithUnicodeCharacters() {
        String path = "/path/with/unicode/文件.jpg";
        char index = '中';
        boolean avatar = true;

        Avatar avatarObj = new Avatar(path, index, avatar);

        assertEquals(path, avatarObj.getPath());
        assertEquals(index, avatarObj.getIndex());
        assertTrue(avatarObj.isAvatar());
    }
}
