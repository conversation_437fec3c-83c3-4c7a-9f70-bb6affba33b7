package com.facishare.fsc.core;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for FSCVersion enum
 */
public class FSCVersionTest {

    @Test
    public void testEnumValues() {
        FSCVersion[] values = FSCVersion.values();
        assertEquals(1, values.length);
        assertEquals(FSCVersion.V2, values[0]);
    }

    @Test
    public void testEnumValueOf() {
        assertEquals(FSCVersion.V2, FSCVersion.valueOf("V2"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testEnumValueOfWithInvalidValue() {
        FSCVersion.valueOf("V1");
    }

    @Test(expected = NullPointerException.class)
    public void testEnumValueOfWithNullValue() {
        FSCVersion.valueOf(null);
    }

    @Test
    public void testEnumName() {
        assertEquals("V2", FSCVersion.V2.name());
    }

    @Test
    public void testEnumToString() {
        assertEquals("V2", FSCVersion.V2.toString());
    }

    @Test
    public void testEnumOrdinal() {
        assertEquals(0, FSCVersion.V2.ordinal());
    }

    @Test
    public void testEnumEquals() {
        assertEquals(FSCVersion.V2, FSCVersion.V2);
        assertTrue(FSCVersion.V2.equals(FSCVersion.V2));
        assertFalse(FSCVersion.V2.equals(null));
        assertFalse(FSCVersion.V2.equals("V2"));
    }

    @Test
    public void testEnumHashCode() {
        assertEquals(FSCVersion.V2.hashCode(), FSCVersion.V2.hashCode());
    }

    @Test
    public void testEnumCompareTo() {
        assertEquals(0, FSCVersion.V2.compareTo(FSCVersion.V2));
    }

    @Test
    public void testEnumInSwitchStatement() {
        String result = getVersionDescription(FSCVersion.V2);
        assertEquals("Version 2", result);
    }

    private String getVersionDescription(FSCVersion version) {
        switch (version) {
            case V2:
                return "Version 2";
            default:
                return "Unknown";
        }
    }

    @Test
    public void testEnumInArray() {
        FSCVersion[] versions = {FSCVersion.V2};
        assertEquals(1, versions.length);
        assertEquals(FSCVersion.V2, versions[0]);
    }

    @Test
    public void testEnumConstantSpecificMethods() {
        // Test that enum constant has expected properties
        assertNotNull(FSCVersion.V2);
        assertTrue(FSCVersion.V2 instanceof FSCVersion);
        assertTrue(FSCVersion.V2 instanceof Enum);
    }

    @Test
    public void testEnumSerialization() {
        // Test enum name for serialization purposes
        assertEquals("V2", FSCVersion.V2.name());

        // Test that valueOf can deserialize
        FSCVersion deserialized = FSCVersion.valueOf(FSCVersion.V2.name());
        assertEquals(FSCVersion.V2, deserialized);
    }

    @Test
    public void testEnumClass() {
        assertEquals(FSCVersion.class, FSCVersion.V2.getClass());
        assertEquals(FSCVersion.class, FSCVersion.V2.getDeclaringClass());
    }

    @Test
    public void testEnumValuesImmutability() {
        FSCVersion[] values1 = FSCVersion.values();
        FSCVersion[] values2 = FSCVersion.values();

        // Should return different arrays (defensive copy)
        assertNotSame(values1, values2);

        // But with same content
        assertArrayEquals(values1, values2);

        // Modifying returned array should not affect enum
        values1[0] = null;
        FSCVersion[] values3 = FSCVersion.values();
        assertEquals(FSCVersion.V2, values3[0]);
    }

    @Test
    public void testEnumWithCollections() {
        java.util.Set<FSCVersion> versionSet = java.util.EnumSet.of(FSCVersion.V2);
        assertTrue(versionSet.contains(FSCVersion.V2));
        assertEquals(1, versionSet.size());

        java.util.Map<FSCVersion, String> versionMap = new java.util.EnumMap<>(FSCVersion.class);
        versionMap.put(FSCVersion.V2, "Version Two");
        assertEquals("Version Two", versionMap.get(FSCVersion.V2));
    }
}
