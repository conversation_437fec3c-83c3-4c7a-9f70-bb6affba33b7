package com.facishare.fsc.core.model.vo;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for MsgFileVo
 */
public class MsgFileVoTest {

    @Test
    public void testDefaultConstructor() {
        MsgFileVo msgFileVo = new MsgFileVo();
        assertNotNull(msgFileVo);
        assertNull(msgFileVo.getFileName());
        assertEquals(0L, msgFileVo.getFileLength());
        assertNull(msgFileVo.getFilePath());
        assertNull(msgFileVo.getDesc());
        assertNull(msgFileVo.getSuffix());
        assertNull(msgFileVo.getData());
    }

    @Test
    public void testSettersAndGetters() {
        MsgFileVo msgFileVo = new MsgFileVo();
        
        String fileName = "test.txt";
        long fileLength = 1024L;
        String filePath = "/path/to/file";
        String desc = "Test file description";
        String suffix = "txt";
        byte[] data = {1, 2, 3, 4, 5};

        msgFileVo.setFileName(fileName);
        msgFileVo.setFileLength(fileLength);
        msgFileVo.setFilePath(filePath);
        msgFileVo.setDesc(desc);
        msgFileVo.setSuffix(suffix);
        msgFileVo.setData(data);

        assertEquals(fileName, msgFileVo.getFileName());
        assertEquals(fileLength, msgFileVo.getFileLength());
        assertEquals(filePath, msgFileVo.getFilePath());
        assertEquals(desc, msgFileVo.getDesc());
        assertEquals(suffix, msgFileVo.getSuffix());
        assertArrayEquals(data, msgFileVo.getData());
    }

    @Test
    public void testToString() {
        MsgFileVo msgFileVo = new MsgFileVo();
        msgFileVo.setFileName("document.pdf");
        msgFileVo.setFileLength(2048L);
        msgFileVo.setFilePath("/documents/document.pdf");
        msgFileVo.setDesc("PDF document");
        msgFileVo.setSuffix("pdf");
        msgFileVo.setData(new byte[]{10, 20, 30});

        String toString = msgFileVo.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("document.pdf"));
        assertTrue(toString.contains("2048"));
        assertTrue(toString.contains("/documents/document.pdf"));
        assertTrue(toString.contains("PDF document"));
        assertTrue(toString.contains("pdf"));
    }

    @Test
    public void testEquals() {
        MsgFileVo msgFileVo1 = new MsgFileVo();
        msgFileVo1.setFileName("test.txt");
        msgFileVo1.setFileLength(1024L);
        msgFileVo1.setFilePath("/test.txt");
        msgFileVo1.setDesc("Test");
        msgFileVo1.setSuffix("txt");
        msgFileVo1.setData(new byte[]{1, 2, 3});

        MsgFileVo msgFileVo2 = new MsgFileVo();
        msgFileVo2.setFileName("test.txt");
        msgFileVo2.setFileLength(1024L);
        msgFileVo2.setFilePath("/test.txt");
        msgFileVo2.setDesc("Test");
        msgFileVo2.setSuffix("txt");
        msgFileVo2.setData(new byte[]{1, 2, 3});

        MsgFileVo msgFileVo3 = new MsgFileVo();
        msgFileVo3.setFileName("different.txt");
        msgFileVo3.setFileLength(2048L);

        assertEquals(msgFileVo1, msgFileVo2);
        assertNotEquals(msgFileVo1, msgFileVo3);
        assertNotEquals(msgFileVo1, null);
        assertNotEquals(msgFileVo1, "not a MsgFileVo");
    }

    @Test
    public void testHashCode() {
        MsgFileVo msgFileVo1 = new MsgFileVo();
        msgFileVo1.setFileName("test.txt");
        msgFileVo1.setFileLength(1024L);

        MsgFileVo msgFileVo2 = new MsgFileVo();
        msgFileVo2.setFileName("test.txt");
        msgFileVo2.setFileLength(1024L);

        assertEquals(msgFileVo1.hashCode(), msgFileVo2.hashCode());
    }

    @Test
    public void testWithNullValues() {
        MsgFileVo msgFileVo = new MsgFileVo();
        msgFileVo.setFileName(null);
        msgFileVo.setFilePath(null);
        msgFileVo.setDesc(null);
        msgFileVo.setSuffix(null);
        msgFileVo.setData(null);

        assertNull(msgFileVo.getFileName());
        assertNull(msgFileVo.getFilePath());
        assertNull(msgFileVo.getDesc());
        assertNull(msgFileVo.getSuffix());
        assertNull(msgFileVo.getData());
    }

    @Test
    public void testWithEmptyValues() {
        MsgFileVo msgFileVo = new MsgFileVo();
        msgFileVo.setFileName("");
        msgFileVo.setFilePath("");
        msgFileVo.setDesc("");
        msgFileVo.setSuffix("");
        msgFileVo.setData(new byte[0]);

        assertEquals("", msgFileVo.getFileName());
        assertEquals("", msgFileVo.getFilePath());
        assertEquals("", msgFileVo.getDesc());
        assertEquals("", msgFileVo.getSuffix());
        assertArrayEquals(new byte[0], msgFileVo.getData());
    }

    @Test
    public void testFileLengthBoundaries() {
        MsgFileVo msgFileVo = new MsgFileVo();
        
        msgFileVo.setFileLength(Long.MAX_VALUE);
        assertEquals(Long.MAX_VALUE, msgFileVo.getFileLength());
        
        msgFileVo.setFileLength(Long.MIN_VALUE);
        assertEquals(Long.MIN_VALUE, msgFileVo.getFileLength());
        
        msgFileVo.setFileLength(0L);
        assertEquals(0L, msgFileVo.getFileLength());
    }

    @Test
    public void testWithSpecialCharacters() {
        MsgFileVo msgFileVo = new MsgFileVo();
        msgFileVo.setFileName("file with spaces & special chars!@#.txt");
        msgFileVo.setFilePath("/path/with spaces/and-special@chars#/");
        msgFileVo.setDesc("Description with special chars: !@#$%^&*()");
        msgFileVo.setSuffix("txt");

        assertEquals("file with spaces & special chars!@#.txt", msgFileVo.getFileName());
        assertEquals("/path/with spaces/and-special@chars#/", msgFileVo.getFilePath());
        assertEquals("Description with special chars: !@#$%^&*()", msgFileVo.getDesc());
        assertEquals("txt", msgFileVo.getSuffix());
    }

    @Test
    public void testWithUnicodeCharacters() {
        MsgFileVo msgFileVo = new MsgFileVo();
        msgFileVo.setFileName("文件名.txt");
        msgFileVo.setFilePath("/路径/文件名.txt");
        msgFileVo.setDesc("文件描述");
        msgFileVo.setSuffix("txt");

        assertEquals("文件名.txt", msgFileVo.getFileName());
        assertEquals("/路径/文件名.txt", msgFileVo.getFilePath());
        assertEquals("文件描述", msgFileVo.getDesc());
        assertEquals("txt", msgFileVo.getSuffix());
    }

    @Test
    public void testWithLargeData() {
        MsgFileVo msgFileVo = new MsgFileVo();
        byte[] largeData = new byte[10000];
        for (int i = 0; i < largeData.length; i++) {
            largeData[i] = (byte) (i % 256);
        }
        
        msgFileVo.setData(largeData);
        msgFileVo.setFileLength(largeData.length);
        
        assertArrayEquals(largeData, msgFileVo.getData());
        assertEquals(largeData.length, msgFileVo.getFileLength());
    }

    @Test
    public void testCommonFileSuffixes() {
        MsgFileVo msgFileVo = new MsgFileVo();
        
        String[] suffixes = {"txt", "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "jpg", "png", "gif", "mp4", "avi"};
        
        for (String suffix : suffixes) {
            msgFileVo.setSuffix(suffix);
            assertEquals(suffix, msgFileVo.getSuffix());
        }
    }

    @Test
    public void testDataArrayModification() {
        MsgFileVo msgFileVo = new MsgFileVo();
        byte[] originalData = {1, 2, 3, 4, 5};
        msgFileVo.setData(originalData);
        
        // Modify the original array
        originalData[0] = 99;
        
        // The msgFileVo data should also be modified (same reference)
        assertEquals(99, msgFileVo.getData()[0]);
    }

    @Test
    public void testTypicalFileScenarios() {
        // Text file
        MsgFileVo textFile = new MsgFileVo();
        textFile.setFileName("readme.txt");
        textFile.setFileLength(512L);
        textFile.setFilePath("/docs/readme.txt");
        textFile.setDesc("Readme file");
        textFile.setSuffix("txt");
        textFile.setData("Hello World".getBytes());
        
        assertEquals("readme.txt", textFile.getFileName());
        assertEquals("txt", textFile.getSuffix());
        
        // Image file
        MsgFileVo imageFile = new MsgFileVo();
        imageFile.setFileName("photo.jpg");
        imageFile.setFileLength(1048576L); // 1MB
        imageFile.setFilePath("/images/photo.jpg");
        imageFile.setDesc("Photo image");
        imageFile.setSuffix("jpg");
        
        assertEquals("photo.jpg", imageFile.getFileName());
        assertEquals(1048576L, imageFile.getFileLength());
        assertEquals("jpg", imageFile.getSuffix());
    }
}
