package com.facishare.fsc.core.model;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for QRCode model
 */
public class QRCodeTest {

    @Test
    public void testDefaultConstructor() {
        QRCode qrCode = new QRCode();
        assertNotNull(qrCode);
        assertNull(qrCode.getEa());
        assertNull(qrCode.getEmployeeId());
        assertNull(qrCode.getContent());
        assertNull(qrCode.getSize());
        assertNull(qrCode.getPath());
        assertNull(qrCode.getFileName());
        assertFalse(qrCode.isEncode());
        assertFalse(qrCode.isNeedCdn());
    }

    @Test
    public void testAllArgsConstructor() {
        String ea = "test_ea";
        Integer employeeId = 123;
        String content = "test content";
        String size = "200x200";
        String path = "/test/path";
        String fileName = "qr.png";
        boolean encode = true;
        boolean needCdn = true;

        QRCode qrCode = new QRCode(ea, employeeId, content, size, path, fileName, encode, needCdn);

        assertEquals(ea, qrCode.getEa());
        assertEquals(employeeId, qrCode.getEmployeeId());
        assertEquals(content, qrCode.getContent());
        assertEquals(size, qrCode.getSize());
        assertEquals(path, qrCode.getPath());
        assertEquals(fileName, qrCode.getFileName());
        assertEquals(encode, qrCode.isEncode());
        assertEquals(needCdn, qrCode.isNeedCdn());
    }

    @Test
    public void testSettersAndGetters() {
        QRCode qrCode = new QRCode();
        
        String ea = "enterprise_account";
        Integer employeeId = 456;
        String content = "QR code content";
        String size = "300x300";
        String path = "/qr/codes";
        String fileName = "qrcode.jpg";
        boolean encode = false;
        boolean needCdn = false;

        qrCode.setEa(ea);
        qrCode.setEmployeeId(employeeId);
        qrCode.setContent(content);
        qrCode.setSize(size);
        qrCode.setPath(path);
        qrCode.setFileName(fileName);
        qrCode.setEncode(encode);
        qrCode.setNeedCdn(needCdn);

        assertEquals(ea, qrCode.getEa());
        assertEquals(employeeId, qrCode.getEmployeeId());
        assertEquals(content, qrCode.getContent());
        assertEquals(size, qrCode.getSize());
        assertEquals(path, qrCode.getPath());
        assertEquals(fileName, qrCode.getFileName());
        assertEquals(encode, qrCode.isEncode());
        assertEquals(needCdn, qrCode.isNeedCdn());
    }

    @Test
    public void testToString() {
        QRCode qrCode = new QRCode("ea1", 123, "content1", "100x100", "/path1", "file1.png", true, false);
        
        String toString = qrCode.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("ea1"));
        assertTrue(toString.contains("123"));
        assertTrue(toString.contains("content1"));
        assertTrue(toString.contains("100x100"));
        assertTrue(toString.contains("/path1"));
        assertTrue(toString.contains("file1.png"));
        assertTrue(toString.contains("true"));
        assertTrue(toString.contains("false"));
    }

    @Test
    public void testEquals() {
        QRCode qrCode1 = new QRCode("ea1", 123, "content1", "100x100", "/path1", "file1.png", true, false);
        QRCode qrCode2 = new QRCode("ea1", 123, "content1", "100x100", "/path1", "file1.png", true, false);
        QRCode qrCode3 = new QRCode("ea2", 456, "content2", "200x200", "/path2", "file2.png", false, true);

        assertEquals(qrCode1, qrCode2);
        assertNotEquals(qrCode1, qrCode3);
        assertNotEquals(qrCode1, null);
        assertNotEquals(qrCode1, "not a QRCode");
    }

    @Test
    public void testHashCode() {
        QRCode qrCode1 = new QRCode("ea1", 123, "content1", "100x100", "/path1", "file1.png", true, false);
        QRCode qrCode2 = new QRCode("ea1", 123, "content1", "100x100", "/path1", "file1.png", true, false);

        assertEquals(qrCode1.hashCode(), qrCode2.hashCode());
    }

    @Test
    public void testWithNullValues() {
        QRCode qrCode = new QRCode(null, null, null, null, null, null, false, false);
        
        assertNull(qrCode.getEa());
        assertNull(qrCode.getEmployeeId());
        assertNull(qrCode.getContent());
        assertNull(qrCode.getSize());
        assertNull(qrCode.getPath());
        assertNull(qrCode.getFileName());
        assertFalse(qrCode.isEncode());
        assertFalse(qrCode.isNeedCdn());
    }

    @Test
    public void testWithEmptyStrings() {
        QRCode qrCode = new QRCode("", 0, "", "", "", "", true, true);
        
        assertEquals("", qrCode.getEa());
        assertEquals(Integer.valueOf(0), qrCode.getEmployeeId());
        assertEquals("", qrCode.getContent());
        assertEquals("", qrCode.getSize());
        assertEquals("", qrCode.getPath());
        assertEquals("", qrCode.getFileName());
        assertTrue(qrCode.isEncode());
        assertTrue(qrCode.isNeedCdn());
    }

    @Test
    public void testWithSpecialCharacters() {
        QRCode qrCode = new QRCode();
        qrCode.setEa("<EMAIL>");
        qrCode.setContent("Content with special chars: !@#$%^&*()");
        qrCode.setSize("100x100");
        qrCode.setPath("/path/with spaces/and-dashes");
        qrCode.setFileName("file name with spaces.png");

        assertEquals("<EMAIL>", qrCode.getEa());
        assertEquals("Content with special chars: !@#$%^&*()", qrCode.getContent());
        assertEquals("/path/with spaces/and-dashes", qrCode.getPath());
        assertEquals("file name with spaces.png", qrCode.getFileName());
    }

    @Test
    public void testBooleanFields() {
        QRCode qrCode = new QRCode();
        
        // Test default values
        assertFalse(qrCode.isEncode());
        assertFalse(qrCode.isNeedCdn());
        
        // Test setting to true
        qrCode.setEncode(true);
        qrCode.setNeedCdn(true);
        assertTrue(qrCode.isEncode());
        assertTrue(qrCode.isNeedCdn());
        
        // Test setting back to false
        qrCode.setEncode(false);
        qrCode.setNeedCdn(false);
        assertFalse(qrCode.isEncode());
        assertFalse(qrCode.isNeedCdn());
    }

    @Test
    public void testEmployeeIdEdgeCases() {
        QRCode qrCode = new QRCode();
        
        qrCode.setEmployeeId(Integer.MAX_VALUE);
        assertEquals(Integer.valueOf(Integer.MAX_VALUE), qrCode.getEmployeeId());
        
        qrCode.setEmployeeId(Integer.MIN_VALUE);
        assertEquals(Integer.valueOf(Integer.MIN_VALUE), qrCode.getEmployeeId());
        
        qrCode.setEmployeeId(0);
        assertEquals(Integer.valueOf(0), qrCode.getEmployeeId());
    }

    @Test
    public void testLongContent() {
        QRCode qrCode = new QRCode();
        StringBuilder longContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longContent.append("a");
        }
        
        qrCode.setContent(longContent.toString());
        assertEquals(longContent.toString(), qrCode.getContent());
        assertEquals(1000, qrCode.getContent().length());
    }
}
