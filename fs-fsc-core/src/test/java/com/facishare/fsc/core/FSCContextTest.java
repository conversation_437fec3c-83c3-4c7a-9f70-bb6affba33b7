package com.facishare.fsc.core;

import com.facishare.fsc.common.authenticate.AuthInfo;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import javax.servlet.ServletRequest;
import javax.ws.rs.container.ContainerRequestContext;

/**
 * Test class for FSCContext
 */
public class FSCContextTest {

    @Before
    public void setUp() {
        // Clean up ThreadLocal before each test
        FSCContext.removeLocal();
    }

    @After
    public void tearDown() {
        // Clean up ThreadLocal after each test
        FSCContext.removeLocal();
    }

    @Test
    public void testGetLocalCreatesNewInstanceWhenNull() {
        FSCContext context = FSCContext.getLocal();
        assertNotNull(context);

        // Should return the same instance on subsequent calls
        FSCContext context2 = FSCContext.getLocal();
        assertSame(context, context2);
    }

    @Test
    public void testRemoveLocal() {
        FSCContext context1 = FSCContext.getLocal();
        assertNotNull(context1);

        FSCContext.removeLocal();

        FSCContext context2 = FSCContext.getLocal();
        assertNotNull(context2);
        assertNotSame(context1, context2); // Should be a new instance
    }

    @Test
    public void testRequestContextGetterSetter() {
        FSCContext context = FSCContext.getLocal();

        assertNull(context.getRequestContext());

        context.setRequestContext(null);
        assertNull(context.getRequestContext());
    }

    @Test
    public void testServletRequestGetterSetter() {
        FSCContext context = FSCContext.getLocal();

        assertNull(context.getServletRequest());

        context.setServletRequest(null);
        assertNull(context.getServletRequest());
    }

    @Test
    public void testAuthInfoGetterSetter() {
        FSCContext context = FSCContext.getLocal();

        assertNull(context.getAuthInfo());

        context.setAuthInfo(null);
        assertNull(context.getAuthInfo());
    }

    @Test
    public void testFscVersionGetterSetter() {
        FSCContext context = FSCContext.getLocal();

        assertNull(context.getFscVersion());

        context.setFscVersion(null);
        assertNull(context.getFscVersion());
    }

    @Test
    public void testGetCurrentRequestContext() {
        // When no context exists
        FSCContext.removeLocal();
        assertNull(FSCContext.getCurrentRequestContext());

        // When context exists but requestContext is null
        FSCContext context = FSCContext.getLocal();
        assertNull(FSCContext.getCurrentRequestContext());
    }

    @Test
    public void testGetRequestWithNullPointerException() {
        FSCContext.removeLocal();

        try {
            FSCContext.getRequest();
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // Expected behavior when no context exists
        }
    }

    @Test
    public void testGetRequestWithValidContext() {
        FSCContext context = FSCContext.getLocal();
        context.setServletRequest(mockServletRequest);

        assertEquals(mockServletRequest, FSCContext.getRequest());
    }

    @Test
    public void testGetRequestWithNullServletRequest() {
        FSCContext context = FSCContext.getLocal();
        // servletRequest is null by default

        assertNull(FSCContext.getRequest());
    }

    @Test
    public void testThreadLocalIsolation() throws InterruptedException {
        // Set context in main thread
        FSCContext mainContext = FSCContext.getLocal();
        mainContext.setRequestContext(mockRequestContext);

        final FSCContext[] otherThreadContext = new FSCContext[1];
        final ContainerRequestContext[] otherThreadRequestContext = new ContainerRequestContext[1];

        Thread otherThread = new Thread(() -> {
            otherThreadContext[0] = FSCContext.getLocal();
            otherThreadRequestContext[0] = otherThreadContext[0].getRequestContext();
        });

        otherThread.start();
        otherThread.join();

        // Contexts should be different instances
        assertNotSame(mainContext, otherThreadContext[0]);

        // Main thread should still have its request context
        assertEquals(mockRequestContext, mainContext.getRequestContext());

        // Other thread should have null request context
        assertNull(otherThreadRequestContext[0]);
    }

    @Test
    public void testMultipleSettersAndGetters() {
        FSCContext context = FSCContext.getLocal();

        // Set all properties
        context.setRequestContext(mockRequestContext);
        context.setServletRequest(mockServletRequest);
        context.setAuthInfo(mockAuthInfo);
        context.setFscVersion(mockFscVersion);

        // Verify all properties
        assertEquals(mockRequestContext, context.getRequestContext());
        assertEquals(mockServletRequest, context.getServletRequest());
        assertEquals(mockAuthInfo, context.getAuthInfo());
        assertEquals(mockFscVersion, context.getFscVersion());

        // Clear all properties
        context.setRequestContext(null);
        context.setServletRequest(null);
        context.setAuthInfo(null);
        context.setFscVersion(null);

        // Verify all properties are null
        assertNull(context.getRequestContext());
        assertNull(context.getServletRequest());
        assertNull(context.getAuthInfo());
        assertNull(context.getFscVersion());
    }

    @Test
    public void testStaticMethodsWithDifferentContextStates() {
        // Test getCurrentRequestContext with different states
        FSCContext.removeLocal();
        assertNull(FSCContext.getCurrentRequestContext());

        FSCContext context = FSCContext.getLocal();
        assertNull(FSCContext.getCurrentRequestContext());

        context.setRequestContext(mockRequestContext);
        assertEquals(mockRequestContext, FSCContext.getCurrentRequestContext());

        FSCContext.removeLocal();
        assertNull(FSCContext.getCurrentRequestContext());
    }

    @Test
    public void testContextPersistenceAcrossMethodCalls() {
        // Set context in one method call
        FSCContext context1 = FSCContext.getLocal();
        context1.setAuthInfo(mockAuthInfo);

        // Get context in another method call
        FSCContext context2 = FSCContext.getLocal();

        // Should be the same instance with same data
        assertSame(context1, context2);
        assertEquals(mockAuthInfo, context2.getAuthInfo());
    }

    @Test
    public void testRemoveLocalMultipleTimes() {
        FSCContext context = FSCContext.getLocal();
        context.setAuthInfo(mockAuthInfo);

        // Remove multiple times should not cause issues
        FSCContext.removeLocal();
        FSCContext.removeLocal();
        FSCContext.removeLocal();

        // Getting context again should create new instance
        FSCContext newContext = FSCContext.getLocal();
        assertNotSame(context, newContext);
        assertNull(newContext.getAuthInfo());
    }
}
