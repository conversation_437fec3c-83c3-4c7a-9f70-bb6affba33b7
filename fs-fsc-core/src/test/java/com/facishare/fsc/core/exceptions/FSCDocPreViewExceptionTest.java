package com.facishare.fsc.core.exceptions;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for FSCDocPreViewException
 */
public class FSCDocPreViewExceptionTest {

    @Test
    public void testConstructorWithMessageAndCode() {
        String message = "Document preview error";
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        FSCDocPreViewException exception = new FSCDocPreViewException(message, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testConstructorWithMessageCauseAndCode() {
        String message = "Document preview error with cause";
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        Throwable cause = new RuntimeException("Preview service unavailable");
        FSCDocPreViewException exception = new FSCDocPreViewException(message, cause, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    public void testConstructorWithCauseAndCode() {
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        Throwable cause = new IllegalArgumentException("Invalid document format");
        FSCDocPreViewException exception = new FSCDocPreViewException(cause, code);

        assertEquals(code, exception.code);
        assertEquals(cause, exception.getCause());
        assertNotNull(exception.getMessage());
        // Message should come from ExceptionConstant.getExceptionTip(code)
        assertEquals(ExceptionConstant.getExceptionTip(code), exception.getMessage());
    }

    @Test
    public void testExceptionInheritance() {
        FSCDocPreViewException exception = new FSCDocPreViewException("Test", ExceptionConstant.FSC_DOC_PRE_VIEW);

        assertTrue(exception instanceof FSCRemoteException);
        assertTrue(exception instanceof FSCException);
        assertTrue(exception instanceof RuntimeException);
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
    }

    @Test
    public void testExceptionWithNullMessage() {
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        FSCDocPreViewException exception = new FSCDocPreViewException((String)null, code);

        assertEquals(code, exception.code);
        assertNull(exception.getMessage());
    }

    @Test
    public void testExceptionWithEmptyMessage() {
        String message = "";
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        FSCDocPreViewException exception = new FSCDocPreViewException(message, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
    }

    @Test
    public void testExceptionWithNullCause() {
        String message = "Test message";
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        FSCDocPreViewException exception = new FSCDocPreViewException(message, null, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testExceptionChaining() {
        RuntimeException rootCause = new RuntimeException("Root cause");
        IllegalArgumentException intermediateCause = new IllegalArgumentException("Intermediate cause", rootCause);
        FSCDocPreViewException exception = new FSCDocPreViewException("Final message", intermediateCause, ExceptionConstant.FSC_DOC_PRE_VIEW);

        assertEquals(intermediateCause, exception.getCause());
        assertEquals(rootCause, exception.getCause().getCause());
    }

    @Test
    public void testExceptionWithDocPreViewCode() {
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        FSCDocPreViewException exception = new FSCDocPreViewException("Document preview failed", code);
        assertEquals(code, exception.code);
        assertEquals("Document preview failed", exception.getMessage());
    }

    @Test
    public void testExceptionToString() {
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        FSCDocPreViewException exception = new FSCDocPreViewException("Preview error", code);

        String toString = exception.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("FSCDocPreViewException"));
        assertTrue(toString.contains("Preview error"));
    }

    @Test
    public void testExceptionStackTrace() {
        FSCDocPreViewException exception = new FSCDocPreViewException("Test message", ExceptionConstant.FSC_DOC_PRE_VIEW);

        StackTraceElement[] stackTrace = exception.getStackTrace();
        assertNotNull(stackTrace);
        assertTrue(stackTrace.length > 0);

        // The first element should be this test method
        assertEquals("testExceptionStackTrace", stackTrace[0].getMethodName());
    }

    @Test
    public void testExceptionWithZeroCode() {
        FSCDocPreViewException exception = new FSCDocPreViewException("Test", 0);
        assertEquals(0, exception.code);
        assertEquals("Test", exception.getMessage());
    }

    @Test
    public void testExceptionWithNegativeCode() {
        int code = -1;
        FSCDocPreViewException exception = new FSCDocPreViewException("Test", code);
        assertEquals(code, exception.code);
        assertEquals("Test", exception.getMessage());
    }

    @Test
    public void testExceptionWithLargeCode() {
        int code = Integer.MAX_VALUE;
        FSCDocPreViewException exception = new FSCDocPreViewException("Test", code);
        assertEquals(code, exception.code);
        assertEquals("Test", exception.getMessage());
    }

    @Test
    public void testExceptionMessageFromConstant() {
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        FSCDocPreViewException exception1 = new FSCDocPreViewException(new RuntimeException(), code);

        // Message should come from ExceptionConstant
        assertEquals(ExceptionConstant.getExceptionTip(code), exception1.getMessage());
    }

    @Test
    public void testExceptionWithDifferentCodes() {
        // Although this exception is specifically for doc preview, it can technically accept any code
        int[] testCodes = {
            ExceptionConstant.FSC_DOC_PRE_VIEW,
            ExceptionConstant.FSC_IMAGE_CODE,
            ExceptionConstant.FSC_FILE_CODE
        };

        for (int code : testCodes) {
            FSCDocPreViewException exception = new FSCDocPreViewException("Test message", code);
            assertEquals(code, exception.code);
        }
    }
}
