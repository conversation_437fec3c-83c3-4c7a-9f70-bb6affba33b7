package com.facishare.fsc.core.exceptions;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for ExceptionConstant
 */
public class ExceptionConstantTest {

    @Test
    public void testExceptionCodeConstants() {
        assertEquals(20001, ExceptionConstant.FSC_DOC_PRE_VIEW);
        assertEquals(20002, ExceptionConstant.FSC_IMAGE_CODE);
        assertEquals(20003, ExceptionConstant.FSC_FILE_CODE);
        assertEquals(20004, ExceptionConstant.FSC_GDS);
        assertEquals(20005, ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE);
        assertEquals(20006, ExceptionConstant.DUBBO_ARG_ERROR);
        assertEquals(20007, ExceptionConstant.FORM_ARG_ERROR);
        assertEquals(20008, ExceptionConstant.INVALID_EXTENSION);
        assertEquals(20009, ExceptionConstant.FILE_TYPE_CHECK_IO_ERROR);
    }

    @Test
    public void testExceptionCodeUniqueness() {
        int[] codes = {
            ExceptionConstant.FSC_DOC_PRE_VIEW,
            ExceptionConstant.FSC_IMAGE_CODE,
            ExceptionConstant.FSC_FILE_CODE,
            ExceptionConstant.FSC_GDS,
            ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE,
            ExceptionConstant.DUBBO_ARG_ERROR,
            ExceptionConstant.FORM_ARG_ERROR,
            ExceptionConstant.INVALID_EXTENSION,
            ExceptionConstant.FILE_TYPE_CHECK_IO_ERROR
        };

        // Check that all codes are unique
        for (int i = 0; i < codes.length; i++) {
            for (int j = i + 1; j < codes.length; j++) {
                assertNotEquals("Exception codes should be unique", codes[i], codes[j]);
            }
        }
    }

    @Test
    public void testExceptionCodeRange() {
        // All exception codes should be in the 20000 range
        assertTrue(ExceptionConstant.FSC_DOC_PRE_VIEW >= 20000 && ExceptionConstant.FSC_DOC_PRE_VIEW < 30000);
        assertTrue(ExceptionConstant.FSC_IMAGE_CODE >= 20000 && ExceptionConstant.FSC_IMAGE_CODE < 30000);
        assertTrue(ExceptionConstant.FSC_FILE_CODE >= 20000 && ExceptionConstant.FSC_FILE_CODE < 30000);
        assertTrue(ExceptionConstant.FSC_GDS >= 20000 && ExceptionConstant.FSC_GDS < 30000);
        assertTrue(ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE >= 20000 && ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE < 30000);
        assertTrue(ExceptionConstant.DUBBO_ARG_ERROR >= 20000 && ExceptionConstant.DUBBO_ARG_ERROR < 30000);
        assertTrue(ExceptionConstant.FORM_ARG_ERROR >= 20000 && ExceptionConstant.FORM_ARG_ERROR < 30000);
        assertTrue(ExceptionConstant.INVALID_EXTENSION >= 20000 && ExceptionConstant.INVALID_EXTENSION < 30000);
        assertTrue(ExceptionConstant.FILE_TYPE_CHECK_IO_ERROR >= 20000 && ExceptionConstant.FILE_TYPE_CHECK_IO_ERROR < 30000);
    }

    @Test
    public void testGetExceptionTipWithValidCode() {
        // Test with known exception codes
        String tip1 = ExceptionConstant.getExceptionTip(ExceptionConstant.FSC_DOC_PRE_VIEW);
        assertNotNull(tip1);
        assertFalse(tip1.isEmpty());

        String tip2 = ExceptionConstant.getExceptionTip(ExceptionConstant.FSC_IMAGE_CODE);
        assertNotNull(tip2);
        assertFalse(tip2.isEmpty());
    }

    @Test
    public void testGetExceptionTipWithInvalidCode() {
        // Test with unknown exception code
        String tip = ExceptionConstant.getExceptionTip(99999);
        assertEquals("General exception information", tip);
    }

    @Test
    public void testGetExceptionTipWithZeroCode() {
        String tip = ExceptionConstant.getExceptionTip(0);
        assertEquals("General exception information", tip);
    }

    @Test
    public void testGetExceptionTipWithNegativeCode() {
        String tip = ExceptionConstant.getExceptionTip(-1);
        assertEquals("General exception information", tip);
    }

    @Test
    public void testGetExceptionTipConsistency() {
        // Test that calling the same method multiple times returns the same result
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        String tip1 = ExceptionConstant.getExceptionTip(code);
        String tip2 = ExceptionConstant.getExceptionTip(code);
        assertEquals(tip1, tip2);
    }

    @Test
    public void testGetExceptionTipNotNull() {
        // Test that getExceptionTip never returns null
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.FSC_DOC_PRE_VIEW));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.FSC_IMAGE_CODE));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.FSC_FILE_CODE));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.FSC_GDS));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.DUBBO_ARG_ERROR));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.FORM_ARG_ERROR));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.INVALID_EXTENSION));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.FILE_TYPE_CHECK_IO_ERROR));
    }

    @Test
    public void testDefaultExceptionMessage() {
        // Test the default message for unknown codes
        String defaultMessage = "General exception information";
        assertEquals(defaultMessage, ExceptionConstant.getExceptionTip(Integer.MAX_VALUE));
        assertEquals(defaultMessage, ExceptionConstant.getExceptionTip(Integer.MIN_VALUE));
        assertEquals(defaultMessage, ExceptionConstant.getExceptionTip(-999));
        assertEquals(defaultMessage, ExceptionConstant.getExceptionTip(999999));
    }

    @Test
    public void testExceptionCodeSequence() {
        // Test that exception codes are in sequence
        assertEquals(20001, ExceptionConstant.FSC_DOC_PRE_VIEW);
        assertEquals(20002, ExceptionConstant.FSC_IMAGE_CODE);
        assertEquals(20003, ExceptionConstant.FSC_FILE_CODE);
        assertEquals(20004, ExceptionConstant.FSC_GDS);
        assertEquals(20005, ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE);
        assertEquals(20006, ExceptionConstant.DUBBO_ARG_ERROR);
        assertEquals(20007, ExceptionConstant.FORM_ARG_ERROR);
        assertEquals(20008, ExceptionConstant.INVALID_EXTENSION);
        assertEquals(20009, ExceptionConstant.FILE_TYPE_CHECK_IO_ERROR);
    }
}
