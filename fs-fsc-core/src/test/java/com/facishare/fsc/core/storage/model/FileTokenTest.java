package com.facishare.fsc.core.storage.model;

import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Date;

/**
 * Test class for FileToken
 */
public class FileTokenTest {

    private FileToken fileToken;

    @Before
    public void setUp() {
        fileToken = new FileToken();
    }

    @Test
    public void testDefaultConstructor() {
        assertNotNull(fileToken);
        assertNull(fileToken.get_id());
        assertNull(fileToken.getEA());
        assertNull(fileToken.getEaYearMonth());
        assertNull(fileToken.getFileToken());
        assertNull(fileToken.getFileType());
        assertNull(fileToken.getFileName());
        assertNull(fileToken.getFilePath());
        assertNull(fileToken.getCreateTime());
        assertNull(fileToken.getWarehouseType());
    }

    @Test
    public void testIdGetterSetter() {
        ObjectId objectId = new ObjectId();
        fileToken.set_id(objectId);
        assertEquals(objectId, fileToken.get_id());
        
        fileToken.set_id(null);
        assertNull(fileToken.get_id());
    }

    @Test
    public void testEAGetterSetter() {
        String ea = "test_enterprise";
        fileToken.setEA(ea);
        assertEquals(ea, fileToken.getEA());
        
        fileToken.setEA(null);
        assertNull(fileToken.getEA());
        
        fileToken.setEA("");
        assertEquals("", fileToken.getEA());
    }

    @Test
    public void testEaYearMonthGetterSetter() {
        String eaYearMonth = "202312";
        fileToken.setEaYearMonth(eaYearMonth);
        assertEquals(eaYearMonth, fileToken.getEaYearMonth());
        
        fileToken.setEaYearMonth(null);
        assertNull(fileToken.getEaYearMonth());
        
        fileToken.setEaYearMonth("");
        assertEquals("", fileToken.getEaYearMonth());
    }

    @Test
    public void testFileTokenGetterSetter() {
        String token = "abc123def456";
        fileToken.setFileToken(token);
        assertEquals(token, fileToken.getFileToken());
        
        fileToken.setFileToken(null);
        assertNull(fileToken.getFileToken());
        
        fileToken.setFileToken("");
        assertEquals("", fileToken.getFileToken());
    }

    @Test
    public void testFileTypeGetterSetter() {
        String fileType = "image/jpeg";
        fileToken.setFileType(fileType);
        assertEquals(fileType, fileToken.getFileType());
        
        fileToken.setFileType(null);
        assertNull(fileToken.getFileType());
        
        fileToken.setFileType("");
        assertEquals("", fileToken.getFileType());
    }

    @Test
    public void testFileNameGetterSetter() {
        String fileName = "test_file.jpg";
        fileToken.setFileName(fileName);
        assertEquals(fileName, fileToken.getFileName());
        
        fileToken.setFileName(null);
        assertNull(fileToken.getFileName());
        
        fileToken.setFileName("");
        assertEquals("", fileToken.getFileName());
    }

    @Test
    public void testFilePathGetterSetter() {
        String filePath = "/path/to/file.jpg";
        fileToken.setFilePath(filePath);
        assertEquals(filePath, fileToken.getFilePath());
        
        fileToken.setFilePath(null);
        assertNull(fileToken.getFilePath());
        
        fileToken.setFilePath("");
        assertEquals("", fileToken.getFilePath());
    }

    @Test
    public void testCreateTimeGetterSetter() {
        Date createTime = new Date();
        fileToken.setCreateTime(createTime);
        assertEquals(createTime, fileToken.getCreateTime());
        
        fileToken.setCreateTime(null);
        assertNull(fileToken.getCreateTime());
    }

    @Test
    public void testWarehouseTypeGetterSetter() {
        String warehouseType = "A";
        fileToken.setWarehouseType(warehouseType);
        assertEquals(warehouseType, fileToken.getWarehouseType());
        
        fileToken.setWarehouseType(null);
        assertNull(fileToken.getWarehouseType());
        
        fileToken.setWarehouseType("");
        assertEquals("", fileToken.getWarehouseType());
    }

    @Test
    public void testAllFieldsSetAndGet() {
        ObjectId objectId = new ObjectId();
        String ea = "enterprise_account";
        String eaYearMonth = "202312";
        String token = "file_token_123";
        String fileType = "application/pdf";
        String fileName = "document.pdf";
        String filePath = "/documents/document.pdf";
        Date createTime = new Date();
        String warehouseType = "N";

        fileToken.set_id(objectId);
        fileToken.setEA(ea);
        fileToken.setEaYearMonth(eaYearMonth);
        fileToken.setFileToken(token);
        fileToken.setFileType(fileType);
        fileToken.setFileName(fileName);
        fileToken.setFilePath(filePath);
        fileToken.setCreateTime(createTime);
        fileToken.setWarehouseType(warehouseType);

        assertEquals(objectId, fileToken.get_id());
        assertEquals(ea, fileToken.getEA());
        assertEquals(eaYearMonth, fileToken.getEaYearMonth());
        assertEquals(token, fileToken.getFileToken());
        assertEquals(fileType, fileToken.getFileType());
        assertEquals(fileName, fileToken.getFileName());
        assertEquals(filePath, fileToken.getFilePath());
        assertEquals(createTime, fileToken.getCreateTime());
        assertEquals(warehouseType, fileToken.getWarehouseType());
    }

    @Test
    public void testWithSpecialCharacters() {
        String ea = "企业@账号#123";
        String fileName = "文件名 with spaces & special chars!.txt";
        String filePath = "/路径/with spaces/and-special@chars#/";

        fileToken.setEA(ea);
        fileToken.setFileName(fileName);
        fileToken.setFilePath(filePath);

        assertEquals(ea, fileToken.getEA());
        assertEquals(fileName, fileToken.getFileName());
        assertEquals(filePath, fileToken.getFilePath());
    }

    @Test
    public void testWithLongStrings() {
        StringBuilder longString = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longString.append("a");
        }
        String longValue = longString.toString();

        fileToken.setEA(longValue);
        fileToken.setFileName(longValue);
        fileToken.setFilePath(longValue);

        assertEquals(longValue, fileToken.getEA());
        assertEquals(longValue, fileToken.getFileName());
        assertEquals(longValue, fileToken.getFilePath());
    }

    @Test
    public void testDateHandling() {
        // Test with current date
        Date now = new Date();
        fileToken.setCreateTime(now);
        assertEquals(now, fileToken.getCreateTime());

        // Test with past date
        Date pastDate = new Date(System.currentTimeMillis() - 86400000); // 1 day ago
        fileToken.setCreateTime(pastDate);
        assertEquals(pastDate, fileToken.getCreateTime());

        // Test with future date
        Date futureDate = new Date(System.currentTimeMillis() + 86400000); // 1 day from now
        fileToken.setCreateTime(futureDate);
        assertEquals(futureDate, fileToken.getCreateTime());
    }

    @Test
    public void testObjectIdHandling() {
        // Test with new ObjectId
        ObjectId id1 = new ObjectId();
        fileToken.set_id(id1);
        assertEquals(id1, fileToken.get_id());

        // Test with another ObjectId
        ObjectId id2 = new ObjectId();
        fileToken.set_id(id2);
        assertEquals(id2, fileToken.get_id());
        assertNotEquals(id1, fileToken.get_id());
    }

    @Test
    public void testCommonFileTypes() {
        String[] fileTypes = {
            "image/jpeg", "image/png", "image/gif",
            "application/pdf", "text/plain", "application/json",
            "video/mp4", "audio/mp3"
        };

        for (String fileType : fileTypes) {
            fileToken.setFileType(fileType);
            assertEquals(fileType, fileToken.getFileType());
        }
    }

    @Test
    public void testCommonWarehouseTypes() {
        String[] warehouseTypes = {"A", "N"};

        for (String warehouseType : warehouseTypes) {
            fileToken.setWarehouseType(warehouseType);
            assertEquals(warehouseType, fileToken.getWarehouseType());
        }
    }

    @Test
    public void testEaYearMonthFormats() {
        String[] yearMonths = {
            "202301", "202312", "202401", "202412",
            "2023-01", "2023-12", "23-01", "23-12"
        };

        for (String yearMonth : yearMonths) {
            fileToken.setEaYearMonth(yearMonth);
            assertEquals(yearMonth, fileToken.getEaYearMonth());
        }
    }
}
