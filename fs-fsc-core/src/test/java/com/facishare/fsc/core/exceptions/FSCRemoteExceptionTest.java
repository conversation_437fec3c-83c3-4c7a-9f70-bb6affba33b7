package com.facishare.fsc.core.exceptions;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for FSCRemoteException
 */
public class FSCRemoteExceptionTest {

    @Test
    public void testConstructorWithMessageAndCode() {
        String message = "Remote service error";
        int code = ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE;
        FSCRemoteException exception = new FSCRemoteException(message, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testConstructorWithMessageCauseAndCode() {
        String message = "Remote service error with cause";
        int code = ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE;
        Throwable cause = new RuntimeException("Network error");
        FSCRemoteException exception = new FSCRemoteException(message, cause, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    public void testConstructorWithCauseAndCode() {
        int code = ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE;
        Throwable cause = new IllegalArgumentException("Invalid request");
        FSCRemoteException exception = new FSCRemoteException(cause, code);

        assertEquals(code, exception.code);
        assertEquals(cause, exception.getCause());
        assertNotNull(exception.getMessage());
        // Message should come from ExceptionConstant.getExceptionTip(code)
        assertEquals(ExceptionConstant.getExceptionTip(code), exception.getMessage());
    }

    @Test
    public void testExceptionInheritance() {
        FSCRemoteException exception = new FSCRemoteException("Test", ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE);

        assertTrue(exception instanceof FSCException);
        assertTrue(exception instanceof RuntimeException);
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
    }

    @Test
    public void testExceptionWithNullMessage() {
        int code = ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE;
        FSCRemoteException exception = new FSCRemoteException((String)null, code);

        assertEquals(code, exception.code);
        assertNull(exception.getMessage());
    }

    @Test
    public void testExceptionWithEmptyMessage() {
        String message = "";
        int code = ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE;
        FSCRemoteException exception = new FSCRemoteException(message, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
    }

    @Test
    public void testExceptionWithNullCause() {
        String message = "Test message";
        int code = ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE;
        FSCRemoteException exception = new FSCRemoteException(message, null, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testExceptionChaining() {
        RuntimeException rootCause = new RuntimeException("Root cause");
        IllegalArgumentException intermediateCause = new IllegalArgumentException("Intermediate cause", rootCause);
        FSCRemoteException exception = new FSCRemoteException("Final message", intermediateCause, ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE);

        assertEquals(intermediateCause, exception.getCause());
        assertEquals(rootCause, exception.getCause().getCause());
    }

    @Test
    public void testExceptionWithDifferentCodes() {
        int[] testCodes = {
            ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE,
            ExceptionConstant.FSC_GDS,
            ExceptionConstant.DUBBO_ARG_ERROR
        };

        for (int code : testCodes) {
            FSCRemoteException exception = new FSCRemoteException("Test message", code);
            assertEquals(code, exception.code);
        }
    }

    @Test
    public void testExceptionToString() {
        int code = ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE;
        FSCRemoteException exception = new FSCRemoteException("Remote error", code);

        String toString = exception.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("FSCRemoteException"));
        assertTrue(toString.contains("Remote error"));
    }

    @Test
    public void testExceptionStackTrace() {
        FSCRemoteException exception = new FSCRemoteException("Test message", ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE);

        StackTraceElement[] stackTrace = exception.getStackTrace();
        assertNotNull(stackTrace);
        assertTrue(stackTrace.length > 0);

        // The first element should be this test method
        assertEquals("testExceptionStackTrace", stackTrace[0].getMethodName());
    }

    @Test
    public void testExceptionWithZeroCode() {
        FSCRemoteException exception = new FSCRemoteException("Test", 0);
        assertEquals(0, exception.code);
        assertEquals("Test", exception.getMessage());
    }

    @Test
    public void testExceptionWithNegativeCode() {
        int code = -1;
        FSCRemoteException exception = new FSCRemoteException("Test", code);
        assertEquals(code, exception.code);
        assertEquals("Test", exception.getMessage());
    }

    @Test
    public void testExceptionWithLargeCode() {
        int code = Integer.MAX_VALUE;
        FSCRemoteException exception = new FSCRemoteException("Test", code);
        assertEquals(code, exception.code);
        assertEquals("Test", exception.getMessage());
    }

    @Test
    public void testExceptionMessageFromConstant() {
        int code = ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE;
        FSCRemoteException exception1 = new FSCRemoteException(new RuntimeException(), code);

        // Message should come from ExceptionConstant
        assertEquals(ExceptionConstant.getExceptionTip(code), exception1.getMessage());
    }
}
