package com.facishare.fsc.core.model;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for Result model
 */
public class ResultTest {

    @Test
    public void testOkWithData() {
        String testData = "test data";
        Result<String> result = Result.ok(testData);

        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(200), result.getCode());
        assertEquals("success request", result.getMessage());
        assertEquals(testData, result.getData());
    }

    @Test
    public void testOkWithNullData() {
        Result<String> result = Result.ok(null);

        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(200), result.getCode());
        assertEquals("success request", result.getMessage());
        assertNull(result.getData());
    }

    @Test
    public void testOkWithComplexData() {
        QRCode qrCode = new QRCode("ea", 123, "content", "size", "path", "file", true, false);
        Result<QRCode> result = Result.ok(qrCode);

        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(200), result.getCode());
        assertEquals("success request", result.getMessage());
        assertEquals(qrCode, result.getData());
    }

    @Test
    public void testError() {
        int errorCode = 404;
        String errorMessage = "Not found";
        Result<String> result = Result.error(errorCode, errorMessage);

        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(errorCode), result.getCode());
        assertEquals(errorMessage, result.getMessage());
        assertNull(result.getData());
    }

    @Test
    public void testErrorWithNullMessage() {
        int errorCode = 500;
        Result<String> result = Result.error(errorCode, null);

        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(errorCode), result.getCode());
        assertNull(result.getMessage());
        assertNull(result.getData());
    }

    @Test
    public void testErrorWithEmptyMessage() {
        int errorCode = 400;
        String errorMessage = "";
        Result<String> result = Result.error(errorCode, errorMessage);

        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(errorCode), result.getCode());
        assertEquals(errorMessage, result.getMessage());
        assertNull(result.getData());
    }

    @Test
    public void testGettersAndSetters() {
        Result<String> result = Result.ok("test");
        
        // Test initial values
        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(200), result.getCode());
        assertEquals("success request", result.getMessage());
        assertEquals("test", result.getData());

        // Test setters
        result.setSuccess(false);
        result.setCode(500);
        result.setMessage("Internal error");
        result.setData("error data");

        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(500), result.getCode());
        assertEquals("Internal error", result.getMessage());
        assertEquals("error data", result.getData());
    }

    @Test
    public void testWithDifferentDataTypes() {
        // Test with Integer
        Result<Integer> intResult = Result.ok(42);
        assertEquals(Integer.valueOf(42), intResult.getData());

        // Test with Boolean
        Result<Boolean> boolResult = Result.ok(true);
        assertEquals(Boolean.TRUE, boolResult.getData());

        // Test with Long
        Result<Long> longResult = Result.ok(123456789L);
        assertEquals(Long.valueOf(123456789L), longResult.getData());
    }

    @Test
    public void testErrorCodes() {
        // Test common HTTP error codes
        Result<String> result400 = Result.error(400, "Bad Request");
        assertEquals(Integer.valueOf(400), result400.getCode());

        Result<String> result401 = Result.error(401, "Unauthorized");
        assertEquals(Integer.valueOf(401), result401.getCode());

        Result<String> result403 = Result.error(403, "Forbidden");
        assertEquals(Integer.valueOf(403), result403.getCode());

        Result<String> result404 = Result.error(404, "Not Found");
        assertEquals(Integer.valueOf(404), result404.getCode());

        Result<String> result500 = Result.error(500, "Internal Server Error");
        assertEquals(Integer.valueOf(500), result500.getCode());
    }

    @Test
    public void testSuccessResultProperties() {
        Result<String> result = Result.ok("success data");
        
        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(200), result.getCode());
        assertEquals("success request", result.getMessage());
        assertNotNull(result.getData());
    }

    @Test
    public void testErrorResultProperties() {
        Result<String> result = Result.error(404, "Resource not found");
        
        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(404), result.getCode());
        assertEquals("Resource not found", result.getMessage());
        assertNull(result.getData());
    }

    @Test
    public void testToString() {
        Result<String> successResult = Result.ok("test data");
        String successToString = successResult.toString();
        assertNotNull(successToString);
        assertTrue(successToString.contains("true"));
        assertTrue(successToString.contains("200"));
        assertTrue(successToString.contains("success request"));
        assertTrue(successToString.contains("test data"));

        Result<String> errorResult = Result.error(500, "error message");
        String errorToString = errorResult.toString();
        assertNotNull(errorToString);
        assertTrue(errorToString.contains("false"));
        assertTrue(errorToString.contains("500"));
        assertTrue(errorToString.contains("error message"));
    }

    @Test
    public void testEquals() {
        Result<String> result1 = Result.ok("data");
        Result<String> result2 = Result.ok("data");
        Result<String> result3 = Result.ok("different data");
        Result<String> result4 = Result.error(404, "error");

        assertEquals(result1, result2);
        assertNotEquals(result1, result3);
        assertNotEquals(result1, result4);
        assertNotEquals(result1, null);
        assertNotEquals(result1, "not a Result");
    }

    @Test
    public void testHashCode() {
        Result<String> result1 = Result.ok("data");
        Result<String> result2 = Result.ok("data");

        assertEquals(result1.hashCode(), result2.hashCode());
    }

    @Test
    public void testWithLongMessage() {
        StringBuilder longMessage = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longMessage.append("This is a very long error message. ");
        }
        
        Result<String> result = Result.error(500, longMessage.toString());
        assertEquals(longMessage.toString(), result.getMessage());
    }

    @Test
    public void testWithSpecialCharactersInMessage() {
        String specialMessage = "Error with special chars: !@#$%^&*()_+-={}[]|\\:;\"'<>?,./";
        Result<String> result = Result.error(400, specialMessage);
        assertEquals(specialMessage, result.getMessage());
    }

    @Test
    public void testWithUnicodeInMessage() {
        String unicodeMessage = "错误信息: 文件未找到 🚫";
        Result<String> result = Result.error(404, unicodeMessage);
        assertEquals(unicodeMessage, result.getMessage());
    }

    @Test
    public void testGenericTypeConsistency() {
        // Test that generic type is maintained
        Result<Integer> intResult = Result.ok(123);
        assertTrue(intResult.getData() instanceof Integer);

        Result<String> stringResult = Result.ok("test");
        assertTrue(stringResult.getData() instanceof String);
    }
}
