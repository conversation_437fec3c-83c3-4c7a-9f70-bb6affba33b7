package com.facishare.fsc.core.exceptions;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for FSCException
 */
public class FSCExceptionTest {

    @Test
    public void testConstructorWithCode() {
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        FSCException exception = new FSCException(code);

        assertEquals(code, exception.code);
        assertNotNull(exception.getMessage());
        assertFalse(exception.getMessage().isEmpty());
    }

    @Test
    public void testConstructorWithMessageAndCode() {
        String message = "Test exception message";
        int code = ExceptionConstant.FSC_IMAGE_CODE;
        FSCException exception = new FSCException(message, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
    }

    @Test
    public void testConstructorWithMessageCauseAndCode() {
        String message = "Test exception with cause";
        int code = ExceptionConstant.FSC_FILE_CODE;
        Throwable cause = new RuntimeException("Root cause");
        FSCException exception = new FSCException(message, cause, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    public void testConstructorWithCauseAndCode() {
        int code = ExceptionConstant.FSC_GDS;
        Throwable cause = new IllegalArgumentException("Argument error");
        FSCException exception = new FSCException(cause, code);

        assertEquals(code, exception.code);
        assertEquals(cause, exception.getCause());
        assertNotNull(exception.getMessage());
        // Message should come from ExceptionConstant.getExceptionTip(code)
        assertEquals(ExceptionConstant.getExceptionTip(code), exception.getMessage());
    }

    @Test
    public void testExceptionIsRuntimeException() {
        FSCException exception = new FSCException(ExceptionConstant.FSC_DOC_PRE_VIEW);
        assertTrue(exception instanceof RuntimeException);
    }

    @Test
    public void testExceptionWithNullMessage() {
        int code = ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE;
        FSCException exception = new FSCException((String)null, code);

        assertEquals(code, exception.code);
        assertNull(exception.getMessage());
    }

    @Test
    public void testExceptionWithEmptyMessage() {
        String message = "";
        int code = ExceptionConstant.DUBBO_ARG_ERROR;
        FSCException exception = new FSCException(message, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
    }

    @Test
    public void testExceptionWithNullCause() {
        String message = "Test message";
        int code = ExceptionConstant.FORM_ARG_ERROR;
        FSCException exception = new FSCException(message, null, code);

        assertEquals(code, exception.code);
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testExceptionCodeField() {
        int[] testCodes = {
            ExceptionConstant.FSC_DOC_PRE_VIEW,
            ExceptionConstant.FSC_IMAGE_CODE,
            ExceptionConstant.FSC_FILE_CODE,
            ExceptionConstant.FSC_GDS,
            ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE,
            ExceptionConstant.DUBBO_ARG_ERROR,
            ExceptionConstant.FORM_ARG_ERROR,
            ExceptionConstant.INVALID_EXTENSION,
            ExceptionConstant.FILE_TYPE_CHECK_IO_ERROR
        };

        for (int code : testCodes) {
            FSCException exception = new FSCException(code);
            assertEquals(code, exception.code);
        }
    }

    @Test
    public void testExceptionWithZeroCode() {
        FSCException exception = new FSCException(0);
        assertEquals(0, exception.code);
        assertNotNull(exception.getMessage());
    }

    @Test
    public void testExceptionWithNegativeCode() {
        int code = -1;
        FSCException exception = new FSCException(code);
        assertEquals(code, exception.code);
        assertNotNull(exception.getMessage());
    }

    @Test
    public void testExceptionWithLargeCode() {
        int code = Integer.MAX_VALUE;
        FSCException exception = new FSCException(code);
        assertEquals(code, exception.code);
        assertNotNull(exception.getMessage());
    }

    @Test
    public void testExceptionChaining() {
        RuntimeException rootCause = new RuntimeException("Root cause");
        IllegalArgumentException intermediateCause = new IllegalArgumentException("Intermediate cause", rootCause);
        FSCException exception = new FSCException("Final message", intermediateCause, ExceptionConstant.FSC_DOC_PRE_VIEW);

        assertEquals(intermediateCause, exception.getCause());
        assertEquals(rootCause, exception.getCause().getCause());
    }

    @Test
    public void testExceptionMessageFromConstant() {
        int code = ExceptionConstant.FSC_DOC_PRE_VIEW;
        FSCException exception1 = new FSCException(code);
        FSCException exception2 = new FSCException(new RuntimeException(), code);

        // Both should have the same message from ExceptionConstant
        assertEquals(exception1.getMessage(), exception2.getMessage());
        assertEquals(ExceptionConstant.getExceptionTip(code), exception1.getMessage());
    }

    @Test
    public void testExceptionToString() {
        int code = ExceptionConstant.FSC_IMAGE_CODE;
        FSCException exception = new FSCException("Test message", code);

        String toString = exception.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("FSCException"));
        assertTrue(toString.contains("Test message"));
    }

    @Test
    public void testExceptionStackTrace() {
        FSCException exception = new FSCException("Test message", ExceptionConstant.FSC_FILE_CODE);

        StackTraceElement[] stackTrace = exception.getStackTrace();
        assertNotNull(stackTrace);
        assertTrue(stackTrace.length > 0);

        // The first element should be this test method
        assertEquals("testExceptionStackTrace", stackTrace[0].getMethodName());
    }
}
