package com.facishare.fsc.core;

import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Test class for WarehouseType constants
 */
public class WarehouseTypeTest {

    @Test
    public void testWarehouseTypeConstants() {
        assertEquals("A", WarehouseType.A);
        assertEquals("N", WarehouseType.N);
    }

    @Test
    public void testConstantsAreNotNull() {
        assertNotNull(WarehouseType.A);
        assertNotNull(WarehouseType.N);
    }

    @Test
    public void testConstantsAreNotEmpty() {
        assertFalse(WarehouseType.A.isEmpty());
        assertFalse(WarehouseType.N.isEmpty());
    }

    @Test
    public void testConstantsUniqueness() {
        assertNotEquals(WarehouseType.A, WarehouseType.N);
    }

    @Test
    public void testConstantsLength() {
        assertEquals(1, WarehouseType.A.length());
        assertEquals(1, WarehouseType.N.length());
    }

    @Test
    public void testConstantsAreUpperCase() {
        assertEquals(WarehouseType.A.toUpperCase(), WarehouseType.A);
        assertEquals(WarehouseType.N.toUpperCase(), WarehouseType.N);
    }

    @Test
    public void testConstantsStringComparison() {
        assertTrue(WarehouseType.A.equals("A"));
        assertTrue(WarehouseType.N.equals("N"));
        assertFalse(WarehouseType.A.equals("a"));
        assertFalse(WarehouseType.N.equals("n"));
    }

    @Test
    public void testConstantsInSwitchStatement() {
        String result = getWarehouseDescription(WarehouseType.A);
        assertEquals("企业间", result);

        result = getWarehouseDescription(WarehouseType.N);
        assertEquals("企业内", result);

        result = getWarehouseDescription("X");
        assertEquals("未知", result);
    }

    @Test
    public void testConstantsInHashSet() {
        Set<String> warehouseTypes = new HashSet<>();
        warehouseTypes.add(WarehouseType.A);
        warehouseTypes.add(WarehouseType.N);

        assertEquals(2, warehouseTypes.size());
        assertTrue(warehouseTypes.contains("A"));
        assertTrue(warehouseTypes.contains("N"));
        assertFalse(warehouseTypes.contains("G"));
    }

    @Test
    public void testConstantsInArray() {
        String[] types = {WarehouseType.A, WarehouseType.N};
        assertEquals(2, types.length);
        assertEquals("A", types[0]);
        assertEquals("N", types[1]);
    }

    @Test
    public void testConstantsWithStringBuilder() {
        StringBuilder sb = new StringBuilder();
        sb.append(WarehouseType.A).append("_").append(WarehouseType.N);
        assertEquals("A_N", sb.toString());
    }

    @Test
    public void testConstantsWithStringFormat() {
        String formatted = String.format("Type: %s, Internal: %s", WarehouseType.A, WarehouseType.N);
        assertEquals("Type: A, Internal: N", formatted);
    }

    @Test
    public void testConstantsCharAt() {
        assertEquals('A', WarehouseType.A.charAt(0));
        assertEquals('N', WarehouseType.N.charAt(0));
    }

    @Test
    public void testConstantsStartsWith() {
        assertTrue("A_file_123".startsWith(WarehouseType.A));
        assertTrue("N_file_456".startsWith(WarehouseType.N));
        assertFalse("G_file_789".startsWith(WarehouseType.A));
        assertFalse("G_file_789".startsWith(WarehouseType.N));
    }

    @Test
    public void testConstantsInternedStrings() {
        // Test that constants are properly interned
        String a1 = WarehouseType.A;
        String a2 = "A";
        assertEquals(a1, a2);
        assertTrue(a1.equals(a2));
    }

    @Test
    public void testConstantsCompareTo() {
        assertTrue(WarehouseType.A.compareTo(WarehouseType.N) < 0); // A comes before N
        assertTrue(WarehouseType.N.compareTo(WarehouseType.A) > 0); // N comes after A
        assertEquals(0, WarehouseType.A.compareTo("A"));
        assertEquals(0, WarehouseType.N.compareTo("N"));
    }

    @Test
    public void testConstantsHashCode() {
        assertEquals("A".hashCode(), WarehouseType.A.hashCode());
        assertEquals("N".hashCode(), WarehouseType.N.hashCode());
        assertNotEquals(WarehouseType.A.hashCode(), WarehouseType.N.hashCode());
    }

    @Test
    public void testConstantsToCharArray() {
        char[] aChars = WarehouseType.A.toCharArray();
        char[] nChars = WarehouseType.N.toCharArray();

        assertEquals(1, aChars.length);
        assertEquals(1, nChars.length);
        assertEquals('A', aChars[0]);
        assertEquals('N', nChars[0]);
    }

    @Test
    public void testConstantsGetBytes() {
        byte[] aBytes = WarehouseType.A.getBytes();
        byte[] nBytes = WarehouseType.N.getBytes();

        assertEquals(1, aBytes.length);
        assertEquals(1, nBytes.length);
        assertEquals((byte)'A', aBytes[0]);
        assertEquals((byte)'N', nBytes[0]);
    }

    @Test
    public void testConstantsSubstring() {
        assertEquals("A", WarehouseType.A.substring(0));
        assertEquals("N", WarehouseType.N.substring(0));
        assertEquals("", WarehouseType.A.substring(1));
        assertEquals("", WarehouseType.N.substring(1));
    }

    @Test
    public void testConstantsReplace() {
        assertEquals("B", WarehouseType.A.replace('A', 'B'));
        assertEquals("M", WarehouseType.N.replace('N', 'M'));
        assertEquals("A", WarehouseType.A.replace('X', 'Y')); // No change
        assertEquals("N", WarehouseType.N.replace('X', 'Y')); // No change
    }

    @Test
    public void testConstantsContains() {
        assertTrue(WarehouseType.A.contains("A"));
        assertTrue(WarehouseType.N.contains("N"));
        assertFalse(WarehouseType.A.contains("B"));
        assertFalse(WarehouseType.N.contains("M"));
    }

    @Test
    public void testConstantsIndexOf() {
        assertEquals(0, WarehouseType.A.indexOf('A'));
        assertEquals(0, WarehouseType.N.indexOf('N'));
        assertEquals(-1, WarehouseType.A.indexOf('B'));
        assertEquals(-1, WarehouseType.N.indexOf('M'));
    }

    @Test
    public void testConstantsLastIndexOf() {
        assertEquals(0, WarehouseType.A.lastIndexOf('A'));
        assertEquals(0, WarehouseType.N.lastIndexOf('N'));
        assertEquals(-1, WarehouseType.A.lastIndexOf('B'));
        assertEquals(-1, WarehouseType.N.lastIndexOf('M'));
    }

    @Test
    public void testConstantsMatches() {
        assertTrue(WarehouseType.A.matches("[A-Z]"));
        assertTrue(WarehouseType.N.matches("[A-Z]"));
        assertFalse(WarehouseType.A.matches("[a-z]"));
        assertFalse(WarehouseType.N.matches("[a-z]"));
    }

    @Test
    public void testConstantsRegionMatches() {
        assertTrue(WarehouseType.A.regionMatches(0, "A", 0, 1));
        assertTrue(WarehouseType.N.regionMatches(0, "N", 0, 1));
        assertFalse(WarehouseType.A.regionMatches(0, "B", 0, 1));
        assertFalse(WarehouseType.N.regionMatches(0, "M", 0, 1));
    }

    private String getWarehouseDescription(String warehouseType) {
        switch (warehouseType) {
            case "A":
                return "企业间";
            case "N":
                return "企业内";
            default:
                return "未知";
        }
    }
}
