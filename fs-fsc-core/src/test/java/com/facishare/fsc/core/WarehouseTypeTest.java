package com.facishare.fsc.core;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for WarehouseType constants
 */
public class WarehouseTypeTest {

    @Test
    public void testWarehouseTypeConstants() {
        assertEquals("A", WarehouseType.A);
        assertEquals("N", WarehouseType.N);
    }

    @Test
    public void testConstantsAreNotNull() {
        assertNotNull(WarehouseType.A);
        assertNotNull(WarehouseType.N);
    }

    @Test
    public void testConstantsAreNotEmpty() {
        assertFalse(WarehouseType.A.isEmpty());
        assertFalse(WarehouseType.N.isEmpty());
    }

    @Test
    public void testConstantsUniqueness() {
        assertNotEquals(WarehouseType.A, WarehouseType.N);
    }

    @Test
    public void testConstantsLength() {
        assertEquals(1, WarehouseType.A.length());
        assertEquals(1, WarehouseType.N.length());
    }

    @Test
    public void testConstantsAreUpperCase() {
        assertEquals(WarehouseType.A.toUpperCase(), WarehouseType.A);
        assertEquals(WarehouseType.N.toUpperCase(), WarehouseType.N);
    }

    @Test
    public void testConstantsStringComparison() {
        assertTrue(WarehouseType.A.equals("A"));
        assertTrue(WarehouseType.N.equals("N"));
        assertFalse(WarehouseType.A.equals("a"));
        assertFalse(WarehouseType.N.equals("n"));
    }

    @Test
    public void testConstantsInSwitchStatement() {
        String result = getWarehouseDescription(WarehouseType.A);
        assertEquals("企业间", result);
        
        result = getWarehouseDescription(WarehouseType.N);
        assertEquals("企业内", result);
        
        result = getWarehouseDescription("X");
        assertEquals("未知", result);
    }

    private String getWarehouseDescription(String warehouseType) {
        switch (warehouseType) {
            case "A":
                return "企业间";
            case "N":
                return "企业内";
            default:
                return "未知";
        }
    }
}
