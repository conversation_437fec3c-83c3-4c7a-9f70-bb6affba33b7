package com.facishare.fsc.core.model;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for RichTextPostData model
 */
public class RichTextPostDataTest {

    @Test
    public void testDefaultConstructor() {
        RichTextPostData data = new RichTextPostData();
        assertNotNull(data);
        assertNull(data.content);
        assertNull(data.title);
        assertFalse(data.isFreeView); // default boolean value is false
    }

    @Test
    public void testFieldAssignment() {
        RichTextPostData data = new RichTextPostData();
        
        data.content = "Test content";
        data.title = "Test title";
        data.isFreeView = true;

        assertEquals("Test content", data.content);
        assertEquals("Test title", data.title);
        assertTrue(data.isFreeView);
    }

    @Test
    public void testToString() {
        RichTextPostData data = new RichTextPostData();
        data.content = "Sample content";
        data.title = "Sample title";
        data.isFreeView = false;

        String toString = data.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("RichTextPostData{"));
        assertTrue(toString.contains("content='Sample content'"));
        assertTrue(toString.contains("title='Sample title'"));
        assertTrue(toString.contains("isFreeView=false"));
    }

    @Test
    public void testToStringWithNullValues() {
        RichTextPostData data = new RichTextPostData();
        data.content = null;
        data.title = null;
        data.isFreeView = true;

        String toString = data.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("content='null'"));
        assertTrue(toString.contains("title='null'"));
        assertTrue(toString.contains("isFreeView=true"));
    }

    @Test
    public void testToStringWithEmptyValues() {
        RichTextPostData data = new RichTextPostData();
        data.content = "";
        data.title = "";
        data.isFreeView = false;

        String toString = data.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("content=''"));
        assertTrue(toString.contains("title=''"));
        assertTrue(toString.contains("isFreeView=false"));
    }

    @Test
    public void testWithLongContent() {
        RichTextPostData data = new RichTextPostData();
        
        StringBuilder longContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longContent.append("This is a very long content. ");
        }
        
        data.content = longContent.toString();
        data.title = "Long Content Title";
        data.isFreeView = true;

        assertEquals(longContent.toString(), data.content);
        assertEquals("Long Content Title", data.title);
        assertTrue(data.isFreeView);
    }

    @Test
    public void testWithSpecialCharacters() {
        RichTextPostData data = new RichTextPostData();
        
        data.content = "Content with special chars: !@#$%^&*()_+-={}[]|\\:;\"'<>?,./";
        data.title = "Title with special chars: !@#$%";
        data.isFreeView = true;

        assertEquals("Content with special chars: !@#$%^&*()_+-={}[]|\\:;\"'<>?,./", data.content);
        assertEquals("Title with special chars: !@#$%", data.title);
        assertTrue(data.isFreeView);
    }

    @Test
    public void testWithHtmlContent() {
        RichTextPostData data = new RichTextPostData();
        
        data.content = "<html><body><h1>Title</h1><p>Paragraph with <b>bold</b> text</p></body></html>";
        data.title = "HTML Content";
        data.isFreeView = false;

        assertEquals("<html><body><h1>Title</h1><p>Paragraph with <b>bold</b> text</p></body></html>", data.content);
        assertEquals("HTML Content", data.title);
        assertFalse(data.isFreeView);
    }

    @Test
    public void testWithUnicodeContent() {
        RichTextPostData data = new RichTextPostData();
        
        data.content = "Unicode content: 中文内容 🎉 العربية русский";
        data.title = "Unicode Title: 标题";
        data.isFreeView = true;

        assertEquals("Unicode content: 中文内容 🎉 العربية русский", data.content);
        assertEquals("Unicode Title: 标题", data.title);
        assertTrue(data.isFreeView);
    }

    @Test
    public void testBooleanField() {
        RichTextPostData data = new RichTextPostData();
        
        // Test default value
        assertFalse(data.isFreeView);
        
        // Test setting to true
        data.isFreeView = true;
        assertTrue(data.isFreeView);
        
        // Test setting back to false
        data.isFreeView = false;
        assertFalse(data.isFreeView);
    }

    @Test
    public void testWithWhitespaceContent() {
        RichTextPostData data = new RichTextPostData();
        
        data.content = "   Content with leading and trailing spaces   ";
        data.title = "\t\nTitle with tabs and newlines\t\n";
        data.isFreeView = true;

        assertEquals("   Content with leading and trailing spaces   ", data.content);
        assertEquals("\t\nTitle with tabs and newlines\t\n", data.title);
        assertTrue(data.isFreeView);
    }

    @Test
    public void testToStringFormat() {
        RichTextPostData data = new RichTextPostData();
        data.content = "test";
        data.title = "title";
        data.isFreeView = true;

        String toString = data.toString();
        String expected = "RichTextPostData{content='test', title='title', isFreeView=true}";
        assertEquals(expected, toString);
    }

    @Test
    public void testFieldsArePublic() {
        RichTextPostData data = new RichTextPostData();
        
        // Test that fields are directly accessible (public)
        data.content = "Direct access content";
        data.title = "Direct access title";
        data.isFreeView = true;

        assertEquals("Direct access content", data.content);
        assertEquals("Direct access title", data.title);
        assertTrue(data.isFreeView);
    }

    @Test
    public void testWithJsonLikeContent() {
        RichTextPostData data = new RichTextPostData();
        
        data.content = "{\"key\": \"value\", \"number\": 123, \"boolean\": true}";
        data.title = "JSON Content";
        data.isFreeView = false;

        assertEquals("{\"key\": \"value\", \"number\": 123, \"boolean\": true}", data.content);
        assertEquals("JSON Content", data.title);
        assertFalse(data.isFreeView);
    }

    @Test
    public void testWithMultilineContent() {
        RichTextPostData data = new RichTextPostData();
        
        data.content = "Line 1\nLine 2\nLine 3\n";
        data.title = "Multiline\nTitle";
        data.isFreeView = true;

        assertEquals("Line 1\nLine 2\nLine 3\n", data.content);
        assertEquals("Multiline\nTitle", data.title);
        assertTrue(data.isFreeView);
    }
}
