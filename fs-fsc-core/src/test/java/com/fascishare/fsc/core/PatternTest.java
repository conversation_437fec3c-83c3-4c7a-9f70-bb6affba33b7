package com.fascishare.fsc.core;

import org.junit.Assert;
import org.junit.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by zhaifj on 2017/1/17.
 */
public class PatternTest {
    private Pattern pattern = Pattern.compile("\\.jpg\\d?\\.jpg");
    public final Pattern AVATAR_PATH_PATTERN = Pattern.compile("(^N_).*([0-9a-f]{32})");

    @Test
    public void testAvatar(){
        String path1="N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50";
        Matcher matcher=AVATAR_PATH_PATTERN.matcher(path1);
        matcher.find();
        System.out.print(matcher.end()+":"+path1.length()+":"+path1.substring(0,matcher.end()-1));
    }


    @Test
    public void testReplace(){
        String path1="N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg4.jpg";
        String path2="N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg2.jpg";
        String path3="N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg3.jpg";
        String path4="N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg4";
        String path="N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg";
        Assert.assertTrue(pattern.matcher(path1).replaceAll(".jpg").equals("N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg"));
        Assert.assertTrue(pattern.matcher(path2).replaceAll(".jpg").equals("N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg"));
        Assert.assertTrue(pattern.matcher(path3).replaceAll(".jpg").equals("N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg"));
        Assert.assertTrue(pattern.matcher(path4).replaceAll(".jpg").equals("N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg4"));
        Assert.assertTrue(pattern.matcher(path).replaceAll(".jpg").equals("N_201608_28_6f6cb0345eea40d3beb2897cb0b52f50.thumb.1.jpg"));



    }
}
