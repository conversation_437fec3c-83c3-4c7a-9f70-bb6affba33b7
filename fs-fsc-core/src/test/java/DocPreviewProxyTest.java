import com.facishare.fsc.common.authenticate.AuthInfo;
import com.facishare.fsc.core.model.GetPreviewInfo;
import com.facishare.fsc.core.remote.DocPreviewProxy;
import com.facishare.fsc.core.remote.NFileStorageProxy;
import com.facishare.fsi.proxy.model.global.config.GetEnterpriseConfigByEA;
import com.facishare.fsi.proxy.service.GlobalConfigService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by <PERSON> on 16/5/9.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring.xml")
public class DocPreviewProxyTest {
    @Autowired
    private GlobalConfigService globalConfigService;
    @Autowired
    private NFileStorageProxy storageProxy;
    private String docPath;
    private DocPreviewProxy docPreviewProxy;
    String ea = "fssdetest";
    String employeeId = "2673";

    @Before
    public void setUp() throws Exception {
        ea = "fssdetest";
        employeeId = "2673";
        AuthInfo authInfo = new AuthInfo();
        authInfo.setEnterpriseAccount(ea);
        authInfo.setEmployeeAccount(employeeId);
        GetEnterpriseConfigByEA.Args arg = new GetEnterpriseConfigByEA.Args();
        arg.EnterpriseAccount = ea;
//        docPath = storageProxy.tempFileUpload(ea, employeeId, IOUtils.toByteArray(new FileInputStream(DocPreviewProxy.class.getResource("/test.doc").getFile())),"FSC",true,null,false);
        docPath = storageProxy.saveFileFromTempFile(ea, employeeId, "doc", null, docPath, false);
        docPreviewProxy = new DocPreviewProxy();
        docPreviewProxy.setConfigName("fs-fsc-proxy-config");
        docPreviewProxy.init();
    }

//    @Test
//    public void getPreviewInfo() {
//        String securityGroup = null;
//        GetPreviewInfo.ResultData result = docPreviewProxy.getPreviewInfo(ea,employeeId, docPath, securityGroup);
//        Assert.assertTrue(result.PageCount > 0);
//
//    }

//    @Test
//    public void getDocumentPage() {
//        getPreviewInfo();
//        docPreviewProxy.getDocumentPage(ea,employeeId,docPath, null, 1, 100, 100);
//    }
}
