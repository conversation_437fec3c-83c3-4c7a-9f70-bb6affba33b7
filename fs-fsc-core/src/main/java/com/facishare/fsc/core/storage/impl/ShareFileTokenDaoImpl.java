package com.facishare.fsc.core.storage.impl;

import com.facishare.fsc.core.storage.ShareFileTokenDao;
import com.facishare.fsc.core.storage.model.FileTokenFields;
import com.facishare.fsc.core.storage.model.ShareFileToken;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON> on 16/5/13.
 */
@Service("shareFileTokenDaoImpl")
public class ShareFileTokenDaoImpl extends BaseDao implements ShareFileTokenDao  {
    @Override
    public ShareFileToken find(String token) {
        Query<ShareFileToken> query=createQuery(shareContentDB,ShareFileToken.class);
        query.field(FileTokenFields.fileToken).equal(token);
        return query.get();
    }
}
