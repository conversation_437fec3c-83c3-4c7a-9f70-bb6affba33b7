package com.facishare.fsc.core.model;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/18.
 */
public class ChunkFileUploadDataResult {
    private Boolean IsSuccess;
    private String Message;

    public Boolean getSuccess() {
        return IsSuccess;
    }

    public void setSuccess(Boolean success) {
        IsSuccess = success;
    }

    public String getMessage() {
        return Message;
    }

    public void setMessage(String message) {
        Message = message;
    }

    public ChunkFileUploadDataResult(Boolean isSuccess, String message) {
        IsSuccess = isSuccess;
        Message = message;
    }

    public ChunkFileUploadDataResult() {
    }
}


