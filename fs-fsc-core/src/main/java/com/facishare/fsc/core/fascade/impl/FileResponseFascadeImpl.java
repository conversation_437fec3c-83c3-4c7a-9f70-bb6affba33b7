package com.facishare.fsc.core.fascade.impl;

import com.facishare.fsc.common.utils.AES256Utils;
import com.facishare.fsc.common.utils.BrowserUtils;
import com.facishare.fsc.common.utils.DocumentFormat;
import com.facishare.fsc.common.utils.FSCUtils;
import com.facishare.fsc.common.utils.FileHelper;
import com.facishare.fsc.common.utils.FileTypeDectetUtils;
import com.facishare.fsc.common.utils.ImageUtils;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.common.utils.QRCodeUtils;
import com.facishare.fsc.core.FSCContext;
import com.facishare.fsc.core.FSSwitch;
import com.facishare.fsc.core.FilePathFilter;
import com.facishare.fsc.core.WarehouseType;
import com.facishare.fsc.core.fascade.FileResponseFascade;
import com.facishare.fsc.core.model.ChunkFileUploadCompleteResult;
import com.facishare.fsc.core.model.ChunkFileUploadDataResult;
import com.facishare.fsc.core.model.ChunkFileUploadStartResult;
import com.facishare.fsc.core.model.CompatibleChunkDownloader;
import com.facishare.fsc.core.model.CompatibleChunkDownloaderExtendToken;
import com.facishare.fsc.core.model.GetDocumentPageData;
import com.facishare.fsc.core.model.GetPreviewInfo;
import com.facishare.fsc.core.model.RangeDownloadResult;
import com.facishare.fsc.core.model.RichTextPostData;
import com.facishare.fsc.core.model.SameCodeResult;
import com.facishare.fsc.core.model.UploadAvatarResult;
import com.facishare.fsc.core.model.vo.EmailMsgPreviewVo;
import com.facishare.fsc.core.model.vo.FileDownloadQueryVo;
import com.facishare.fsc.core.model.vo.MsgFileVo;
import com.facishare.fsc.core.processor.AsyncFileBatchDownloadProcessor;
import com.facishare.fsc.core.remote.DownloadSpeedLimiter;
import com.facishare.fsc.core.remote.NFileStorageProxy;
import com.facishare.fsc.core.repository.FileRepository;
import com.facishare.fsc.core.repository.model.FileMetaData;
import com.facishare.fsc.core.repository.model.ViewRichTextResult;
import com.facishare.fsc.core.storage.model.DownloadFileToken;
import com.facishare.fsc.core.storage.model.QRImageStatus;
import com.facishare.fsi.proxy.exception.FsiClientException;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NBatchDownloadFileAsyn;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileGetOriginNamesRequest;
import com.facishare.stone.sdk.response.StoneFileGetOriginNamesResponse;
import com.facishare.warehouse.api.dubbo.FilePackedService;
import com.facishare.warehouse.api.model.FilePackedArg;
import com.facishare.warehouse.api.model.FilePackedResult;
import com.fxiaoke.common.Guard;
import com.fxiaoke.common.SizeFormatter;
import com.fxiaoke.mimetype.FileMimeTypeHelper;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.api.IConfigFactory;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import javax.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.jboss.resteasy.spi.ResteasyProviderFactory;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.simplejavamail.outlookmessageparser.OutlookMessageParser;
import org.simplejavamail.outlookmessageparser.model.OutlookFileAttachment;
import org.simplejavamail.outlookmessageparser.model.OutlookMessage;
import org.simplejavamail.outlookmessageparser.model.OutlookMsgAttachment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.container.AsyncResponse;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by Aaron on 16/5/13.
 */
@Service
@Slf4j
public class FileResponseFascadeImpl implements FileResponseFascade {
  @Autowired
  private FileRepository fileRepository;
  @Autowired
  private AsyncFileBatchDownloadProcessor asyncFileBatchDownloadManager;
  @Autowired
  private FilePackedService filePackedService;
  @Autowired
  private FilePathFilter filePathFilter;
  @Autowired
  private StoneProxyApi stoneProxyApi;
  @Autowired
  private NFileStorageProxy nFileStorageProxy;
  @Autowired
  private DownloadSpeedLimiter speedLimiter;

  private String preview_token_prefix = "";
  private String preview_token_encrypt_key = "";
  private int maxPackFileSize = 500;//单位M
  @ReloadableProperty("shared_token_exp_mills")
  private long sharedTokenExpMills = 3600;
  @ReloadableProperty("needRealTimeThumbnailSwitch")
  private int needRealTimeThumbnailSwitch = 0;
  @ReloadableProperty("warehouseBatchUrl")
  private String warehouseBatchUrl = "";

  private String xssAspectStr = "onclick,onload,script";

  private String qrShowSkey = "";

  private static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
  private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("fsc");
  @PostConstruct
  private void init() {
    IConfigFactory factory = ConfigFactory.getInstance();
    factory.getConfig("fs-netdisk-warehouse", config -> {
      preview_token_prefix = config.get("preview_token_prefix");
      preview_token_encrypt_key = config.get("preview_token_encrypt_key");
      maxPackFileSize = config.getInt("maxPackFileSize", 500);
    });
    factory.getConfig("fs-fsc-fileshare", (IConfig config) -> qrShowSkey = config.get("qrShowSkey"));
    factory.getConfig("fs-fsc-cgi-config", (IConfig config) -> xssAspectStr = config.get("xssAspectStr"));
  }


  @Override
  public Response.ResponseBuilder getMp3ByPath(FileDownloadQueryVo fileDownloadQueryVo) {
    String ea = fileDownloadQueryVo.getEa();
    String sourceUser = fileDownloadQueryVo.getSourceUser();
    String path = fileDownloadQueryVo.getPath();
    log.info( "getMp3ByPath:EA:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, fileDownloadQueryVo.getSecurityGroup());
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    fileDownloadQueryVo.setPath(filePathFilter.format(fileDownloadQueryVo.getPath()));

    if (!filePathFilter.isValid(fileDownloadQueryVo.getPath())) {
      return responseBuilder.status(Response.Status.BAD_REQUEST);
    }
    byte[] data = fileRepository.getMp3ByPath(ea, sourceUser, fileDownloadQueryVo.getPath(), fileDownloadQueryVo.getSecurityGroup());
    if (data == null) {
      responseBuilder.status(Response.Status.NOT_FOUND);
    } else {
      responseBuilder
        .header(HttpHeaders.CONTENT_TYPE, "audio/mp3")
        .header("Content-Disposition", "attachment; filename=" + fileDownloadQueryVo.getPath())
        .header(HttpHeaders.CONTENT_LENGTH, data.length);
      InputStream stream = speedLimiter.wrapWithSpeedLimit(data, ea, sourceUser, path);
      responseBuilder.entity(stream);
    }
    return responseBuilder;
  }

  @Override
  public Response.ResponseBuilder getByPath(FileDownloadQueryVo fileDownloadQueryVo) {
    return getByPath(fileDownloadQueryVo.getEa(),
      fileDownloadQueryVo.getSourceUser(),
      fileDownloadQueryVo.getPath(),
      fileDownloadQueryVo.getSecurityGroup(),
      fileDownloadQueryVo.getFiletype(),
      fileDownloadQueryVo.getUa(),
      fileDownloadQueryVo.isParse());
  }

  private Response.ResponseBuilder getByPath(String ea, String sourceUser, String path, String securityGroup, String filetype, String ua, boolean parse) {
    log.info( "getByPath:EA:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup);
    //path没有扩展名的查下库
    if (!Strings.isNullOrEmpty(path) && path.indexOf('.') == -1 && (path.startsWith("N_") || path.startsWith("TN_"))) {
      try {
        path += "." + nFileStorageProxy.getExtensionName(ea, sourceUser, securityGroup, path);
      } catch (Exception e) {
        log.warn("获取扩展名失败,ea:{},path:{},msg:{}", ea, path, e.getMessage());
      }
    }
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    path = filePathFilter.format(path);
    if (!Strings.isNullOrEmpty(path) && path.contains(":")) {
      path = path.substring(path.indexOf(":") + 1);
    }
    if (!filePathFilter.isValid(path)) {
      responseBuilder.status(Response.Status.BAD_REQUEST);
    } else {
      CompatibleChunkDownloader compatibleChunkDownloader = fileRepository.getByPathCompatibleChunk(ea, sourceUser, path, securityGroup, filetype, ua);
      //流式处理
      if (compatibleChunkDownloader.isChunk() && compatibleChunkDownloader.isComplete()) {
        getPreviewResponseByStream(path);
        fileRepository.getChunkFileByStream(ea, sourceUser, path, securityGroup, compatibleChunkDownloader.getChunkIndexs());
        return null;
      }
      //传统基于ResponseBuilder内存处理
      byte[] data = compatibleChunkDownloader.getData();
      if (data == null) {
        if (needRealTimeThumbnailSwitch == 1) {
          try {
            if (path.startsWith("N_")) {
              //判断是否符合business的情形
              boolean needRealTimeThumbnail = fileRepository.checkNeedRealTimeThumbnail(ea, path);
              if (needRealTimeThumbnail) {
                data = fileRepository.getThumbnailData(ea, sourceUser, path);
                if (data == null) {
                  responseBuilder.status(Response.Status.NOT_FOUND);
                  return responseBuilder;
                } else {
                  getPreviewResponse(ea, sourceUser, path, parse, responseBuilder, data);
                }
              } else {
                responseBuilder.status(Response.Status.NOT_FOUND);
                return responseBuilder;
              }
            } else {
              responseBuilder.status(Response.Status.NOT_FOUND);
              return responseBuilder;
            }
          } catch (Exception e) {
            log.warn("checkNeedRealTimeThumbnail fail", e);
            responseBuilder.status(Response.Status.NOT_FOUND);
            return responseBuilder;
          }
        } else {
          responseBuilder.status(Response.Status.NOT_FOUND);
          return responseBuilder;
        }
      }
      getPreviewResponse(ea, sourceUser, path, parse, responseBuilder, data);
    }
    return responseBuilder;
  }

  private void getPreviewResponseByStream(String path) {
    log.info( "getPreviewResponse:path:{}", path);
    DocumentFormat docType = FileHelper.getDocumentFormat(FileHelper.getFileExt(path), false);
    HttpServletResponse response = ResteasyProviderFactory.getContextData(HttpServletResponse.class);
    switch (docType) {
      case Text:
        response.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
        response.addHeader(HttpHeaders.CONTENT_ENCODING, "UTF-8");//TXT类型的编码需要猜测,不一定是UTF-8

        break;

      case Pdf:
        response.addHeader(HttpHeaders.CONTENT_TYPE, "application/pdf");
        response.addHeader(HttpHeaders.CONTENT_ENCODING, "UTF-8");//TXT类型的编码需要猜测,不一定是UTF-8

        break;

      case Image:
        String mime = FileHelper.getMime(FileHelper.getFileExt(path));
        if (Strings.isNullOrEmpty(mime)) {
          mime = "image/jpeg";
        }
        response.addHeader(HttpHeaders.CONTENT_TYPE, mime);
        response.addHeader(HttpHeaders.CONTENT_ENCODING, "UTF-8");
        response.addHeader(HttpHeaders.CACHE_CONTROL, "max-age=*********");
        break;
    }
  }


  private String getRequestHeader(String key) {
    String value = Objects.requireNonNull(FSCContext.getCurrentRequestContext()).getHeaders().getFirst(key);
    return Strings.isNullOrEmpty(value) ? FSCContext.getCurrentRequestContext().getHeaders().getFirst(key.toLowerCase()) : value;
  }

  private void getPreviewResponse(String ea, String user, String path, boolean parse, Response.ResponseBuilder responseBuilder, byte[] data) {
    log.info( "getPreviewResponse:path:{}", path);
    DocumentFormat docType = FileHelper.getDocumentFormat(FileHelper.getFileExt(path), parse);
    switch (docType) {
      case Html:
        if (data.length == 0) {
          responseBuilder.entity("");
        } else {
          responseBuilder.entity(speedLimiter.wrapWithSpeedLimit(data, ea, user, path));
          responseBuilder.header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_HTML + ";charset=UTF-8");
          responseBuilder.header(HttpHeaders.CONTENT_ENCODING, "UTF-8");
        }
        break;
      case Msg:
        if (data.length == 0) {
          responseBuilder.entity("");
        } else {
          String html = msgParseToPreview(new ByteArrayInputStream(data));
          responseBuilder.entity(html);
          responseBuilder.header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_HTML + ";charset=UTF-8");
        }
        break;
      case Text:
        if (data.length == 0) {
          responseBuilder.entity("");
        } else {
          String coding = charset(new ByteArrayInputStream(data));
          responseBuilder.header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN + ";charset=" + coding);
          responseBuilder.entity(speedLimiter.wrapWithSpeedLimit(data, ea, user, path));
        }
        break;
      case Pdf:
        if (data.length == 0) {
          responseBuilder.entity("");
        } else {
          responseBuilder.header(HttpHeaders.CONTENT_TYPE, "application/pdf");
          responseBuilder.header(HttpHeaders.CONTENT_ENCODING, "UTF-8");
          responseBuilder.entity(speedLimiter.wrapWithSpeedLimit(data, ea, user, path));
        }
        break;
      case Mp3:
        String range = getRequestHeader("Range");
        boolean noRange = Strings.isNullOrEmpty(range);
        range = Objects.toString(range, "bytes=0-");
        int unitIndex = range.indexOf('=');
        if (unitIndex == -1) {
          log.warn("downloadByRange Param:{},{},{},range fmt error", ea, path, range);
          responseBuilder.status(Response.Status.REQUESTED_RANGE_NOT_SATISFIABLE);
        }
        String rangeList = range.substring(unitIndex + 1);
        if (rangeList.indexOf(',') != -1) {
          log.warn("downloadByRange Param:{},{},{},not support multipart range", ea, path, range);
          responseBuilder.status(Response.Status.REQUESTED_RANGE_NOT_SATISFIABLE);
        }
        try {
          int splitIndex = rangeList.indexOf('-');
          int start = Integer.parseInt(rangeList.substring(0, splitIndex));
          int end = splitIndex == rangeList.length() - 1 ? 0 : Integer.parseInt(rangeList.substring(splitIndex + 1)) + 1;
          RangeDownloadResult result = downloadByRange(FileDownloadQueryVo
                                                         .builder()
                                                         .ea(ea)
                                                         .sourceUser("E.1000")
                                                         .path(path)
                                                         .ua(getRequestHeader("User-Agent"))
                                                         .byteStartIndex(start)
                                                         .byteEndIndex(end)
                                                         .securityGroup("")
                                                         .build());

          byte[] bytes = result.getRangeData();
          responseBuilder
            .status(noRange ? Response.Status.OK : Response.Status.PARTIAL_CONTENT)
            .header("Content-Range", "bytes " + result.getStart() + '-' + (result.getEnd() - 1) + '/' + result.getTotalSize())
            .header("Accept-Ranges", "bytes")
            .header(HttpHeaders.CONTENT_LENGTH, bytes.length)
            .header(HttpHeaders.ETAG, FileHelper.getMD5Fast(bytes))
            .header(HttpHeaders.CONTENT_TYPE, FileHelper.getMime(FilenameUtils.getExtension(path)))
            .header(HttpHeaders.CACHE_CONTROL, "private, max-age=120")
            .entity(speedLimiter.wrapWithSpeedLimit(bytes, ea, user, path));
        } catch (Exception e) {
          log.error("downloadByRange Exception:{},{},{}", ea, path, range, e);
          responseBuilder.status(Response.Status.NOT_FOUND);
        }
        break;
      case Image:
        String mime;
        if (FileMimeTypeHelper.isHeif(data)) {
          //转化heic为jpg
          data = fileRepository.convertHeicToJpg(ea, data);
        }
        if (FileMimeTypeHelper.isWebp(data)) {
          mime = "image/webp";
        } else if (FileMimeTypeHelper.isJpeg(data)) {
          mime = "image/jpeg";
        } else if (FileMimeTypeHelper.isPng(data)) {
          mime = "image/png";
        } else if (FileMimeTypeHelper.isGif(data)) {
          mime = "image/gif";
        } else {
          mime = FileHelper.getMime(FileHelper.getFileExt(path));
          if (Strings.isNullOrEmpty(mime)) {
            mime = "image/jpeg";
          } else if ("image/svg+xml".equalsIgnoreCase(mime)) {
            //svg的需要过滤下脚本
            String encoding = "UTF-8";
            String svgHtml = IOUtils.toString(data, encoding);
            if (hasXssAspect(svgHtml)) {
              mime = MediaType.TEXT_PLAIN;
            }
          }
        }
        responseBuilder.header(HttpHeaders.CONTENT_TYPE, mime + ";charset=UTF-8");
        responseBuilder.header(HttpHeaders.CONTENT_ENCODING, "UTF-8");
        responseBuilder.header(HttpHeaders.CACHE_CONTROL, "max-age=*********");
        responseBuilder.header(HttpHeaders.CONTENT_LENGTH, data.length);
        responseBuilder.entity(speedLimiter.wrapWithSpeedLimit(data, ea, user, path));
        break;
      default:
        mime = FileHelper.getMime(
          Strings.isNullOrEmpty(FileHelper.getFileExt(path)) ? FileMimeTypeHelper.getMimeType(data).getMimeType() : FileHelper.getFileExt(path));
        if (Strings.isNullOrEmpty(mime) || mime.contains("text")) {
          mime = MediaType.TEXT_PLAIN;
        }
        responseBuilder.header(HttpHeaders.CONTENT_TYPE, mime);
        responseBuilder.header(HttpHeaders.CONTENT_ENCODING, "UTF-8");
        responseBuilder.header(HttpHeaders.CONTENT_LENGTH, data.length);
        responseBuilder.entity(speedLimiter.wrapWithSpeedLimit(data, ea, user, path));
    }
  }

  /**
   * msg -> html
   *
   * @param inputStream
   * @return
   */
  public String msgParseToPreview(InputStream inputStream) {
    EmailMsgPreviewVo emailMsgPreviewVo = new EmailMsgPreviewVo();
    try {
      OutlookMessageParser outlookMessageParser = new OutlookMessageParser();
      OutlookMessage msg = outlookMessageParser.parseMsg(inputStream);
      emailMsgPreviewVo.setFrom(msg.getFromName() + "(" + msg.getFromEmail() + ")");
      emailMsgPreviewVo.setCc(msg.getDisplayCc().trim());
      emailMsgPreviewVo.setSubject(msg.getSubject());
      emailMsgPreviewVo.setSentDate(formatDate(msg.getDate(), "yyyy-MM-dd HH:mm"));
      emailMsgPreviewVo.setTo(msg.getDisplayTo());
      List<MsgFileVo> attachList = new ArrayList<>();
      for (int i = 0; i < msg.getOutlookAttachments().size(); i++) {
        if (msg.getOutlookAttachments().get(i) instanceof OutlookMsgAttachment) {
          continue;
        }
        OutlookFileAttachment attachment = (OutlookFileAttachment) msg.getOutlookAttachments().get(i);
        String attachName = attachment.getLongFilename();
        byte[] data = attachment.getData();
        if (data != null && data.length != 0) {
          MsgFileVo fileVo = new MsgFileVo();
          fileVo.setFileName(attachName);
          fileVo.setFileLength(data.length);
          fileVo.setData(data);
          attachList.add(fileVo);
        }
      }
      emailMsgPreviewVo.setAttachments(attachList);
      String bodyText = msg.getBodyText();
      //防止空指针
      if (bodyText != null) {
        Document doc = Jsoup.parse(msg.getConvertedBodyHTML());
        List<MsgFileVo> newAttachList = new ArrayList<>();
        newAttachList.addAll(attachList);
        // 对邮件中图片进行处理,这里的处理方式是把附件进行转码.然后在页面展示处理
        Elements imgList = doc.select("img");
        for (Element element : imgList) {
          String src = element.attr("src");
          if (src.indexOf("cid:") < 0) {
            continue;
          }
          String imgAttach = src.substring(4);
          MsgFileVo fileVo = null;
          for (MsgFileVo tmp : attachList) {
            if (imgAttach.contains(tmp.getFileName()) || tmp.getFileName().contains(imgAttach)) {
              fileVo = tmp;
              break;
            }
          }
          if (fileVo == null) {
            continue;
          }
          String base64 = null;
          try {
            base64 = Base64.getEncoder().encodeToString(fileVo.getData());
          } catch (Exception e) {
            log.error( "image base64 encode fail!");
          }
          if (StringUtils.isNotBlank(base64)) {
            String srcBase64 = "data:image/png;base64," + base64;
            element.attr("src", srcBase64);
            if (newAttachList != null && newAttachList.size() > 0 && newAttachList.contains(fileVo)) {
              newAttachList.remove(fileVo);
            }
          }
        }
        // 内容
        Elements bodyList = doc.select("body");
        if (!bodyList.isEmpty()) {
          Element bodyEle = bodyList.first();
          if (bodyEle.html().length() > 0) {
            emailMsgPreviewVo.setContent(bodyEle.html());
          }
        }
      } else {
        emailMsgPreviewVo.setContent("");
      }
    } catch (IOException e1) {
      log.error( "msg preview fail!");
    }

    StringBuilder contentBuilder = new StringBuilder();
    contentBuilder.append("</br>").append("</br>");
    contentBuilder.append("<p>");
    if (!Strings.isNullOrEmpty(emailMsgPreviewVo.getSentDate())) {
      contentBuilder.append("send time:" + emailMsgPreviewVo.getSentDate() + "</br>");
    }
    if (!Strings.isNullOrEmpty(emailMsgPreviewVo.getFrom())) {
      contentBuilder.append("sender:" + emailMsgPreviewVo.getFrom() + "</br>");
    }
    if (!Strings.isNullOrEmpty(emailMsgPreviewVo.getCc())) {
      contentBuilder.append("Cc:" + emailMsgPreviewVo.getCc() + "</br>");
    }
    if (!Strings.isNullOrEmpty(emailMsgPreviewVo.getTo())) {
      contentBuilder.append("recipient:" + emailMsgPreviewVo.getTo() + "</br>");
    }
    if (!Strings.isNullOrEmpty(emailMsgPreviewVo.getSubject())) {
      contentBuilder.append("theme:" + emailMsgPreviewVo.getSubject() + "</br>");
    }
    if (!Strings.isNullOrEmpty(emailMsgPreviewVo.getContent())) {
      contentBuilder.append("<p>" + "&nbsp;&nbsp;&nbsp;&nbsp;" + emailMsgPreviewVo.getContent() + "<p>");
    }
    return contentBuilder.toString();
  }

  public static String formatDate(Date date, String format) {
    return FastDateFormat.getInstance(format, null, null).format(date);
  }

  @Override
  public Response.ResponseBuilder viewImage(FileDownloadQueryVo fileDownloadQueryVo) {
    return getByPath(fileDownloadQueryVo);
  }

  private boolean hasXssAspect(String xml) {
    log.info("xssAspectStr:{}", xssAspectStr);
    List<String> xssAspectList = Arrays.asList(xssAspectStr.split(","));
    return xssAspectList.stream().anyMatch(xml::contains);
  }

  @Override
  public Response.ResponseBuilder downloadByPath(FileDownloadQueryVo fileDownloadQueryVo, String name) {
    log.info( "downloadByPath:{}", fileDownloadQueryVo);
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    fileDownloadQueryVo.setPath(filePathFilter.format(fileDownloadQueryVo.getPath()));
    if (Strings.isNullOrEmpty(name) || !filePathFilter.isValid(fileDownloadQueryVo.getPath())) {
      return builder.status(Response.Status.BAD_REQUEST);
    }

    CompatibleChunkDownloader compatibleChunkDownloader = fileRepository.getByPathCompatibleChunk(fileDownloadQueryVo.getEa(),
      fileDownloadQueryVo.getSourceUser(),
      fileDownloadQueryVo.getPath(),
      fileDownloadQueryVo.getSecurityGroup(),
      fileDownloadQueryVo.getFiletype(),
      fileDownloadQueryVo.getUa());

    //流式处理
    if (compatibleChunkDownloader.isChunk() && compatibleChunkDownloader.isComplete()) {
      HttpServletResponse response = ResteasyProviderFactory.getContextData(HttpServletResponse.class);
      response.addHeader(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
      try {
        byte[] firstChunkData = fileRepository.downloadFirstChunkFile(fileDownloadQueryVo.getEa(),
          fileDownloadQueryVo.getSourceUser(),
          fileDownloadQueryVo.getPath());
        if (FileMimeTypeHelper.isJpeg(firstChunkData)) {
          name = FilenameUtils.getBaseName(name) + ".jpg";
        }
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), name));
      } catch (Exception e) {
        log.error("下载异常,设置响应头失败!{},{},{},{}",
          fileDownloadQueryVo.getEa(),
          fileDownloadQueryVo.getSourceUser(),
          fileDownloadQueryVo.getPath(),
          fileDownloadQueryVo.getSecurityGroup(),
          e);
      }
      fileRepository.getChunkFileByStream(fileDownloadQueryVo.getEa(),
        fileDownloadQueryVo.getSourceUser(),
        fileDownloadQueryVo.getPath(),
        fileDownloadQueryVo.getSecurityGroup(),
        compatibleChunkDownloader.getChunkIndexs());
      return null;
    }

    //传统基于ResponseBuilder内存处理
    byte[] data = compatibleChunkDownloader.getData();
    if (data == null) {
      return builder.status(Response.Status.NOT_FOUND);
    }

    try {
      if (FileMimeTypeHelper.isJpeg(data)) {
        name = FilenameUtils.getBaseName(name) + ".jpg";
      }
      builder.header(HttpHeaders.CONTENT_DISPOSITION,
        BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), name, fileDownloadQueryVo.isPreview()));
    } catch (Exception e) {
      log.error("下载异常,设置响应头失败!{},{},{},{}",
        fileDownloadQueryVo.getEa(),
        fileDownloadQueryVo.getSourceUser(),
        fileDownloadQueryVo.getPath(),
        fileDownloadQueryVo.getSecurityGroup(),
        e);
    }
    if (fileDownloadQueryVo.isPreview()) {
      getPreviewResponse(fileDownloadQueryVo.getEa(), fileDownloadQueryVo.getSourceUser(), fileDownloadQueryVo.getPath(), false, builder, data);
    } else {
      builder.header(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
      builder.entity(data);
      builder.header(HttpHeaders.CONTENT_LENGTH,data.length);
    }
    return builder;
  }

  public static void main(String[] args) {
    System.out.println(new String(Base64.getUrlDecoder().decode("aHR0cDovLzE3Mi4xNy40LjIzMDo0NTU2NS9mcy13YXJlaG91c2UtYmF0Y2gvZG93bmxvYWQuZG8_a2V5PWQzMjc3OWVhMWY5ODYwZmQ1MjVhZTQyODczNjM3OGQw")));
  }

  @Override
  public Response.ResponseBuilder downloadAvatarByPath(FileDownloadQueryVo fileDownloadQueryVo, String name, String oldUglyIndex, String autofit) {
    log.info( "downloadAvatarByPath:{},name:{},oldUglyIndex:{},autofit:{}", fileDownloadQueryVo, name, oldUglyIndex, autofit);
    String path = fileDownloadQueryVo.getPath();
    if (!Strings.isNullOrEmpty(path) && path.contains(":")) {
      path = path.substring(path.indexOf(":") + 1);
    }
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    if (Strings.isNullOrEmpty(path)) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String ea = fileDownloadQueryVo.getEa();
    String securityGroup = fileDownloadQueryVo.getSecurityGroup();
    String sourceUser = fileDownloadQueryVo.getSourceUser();
    path = filePathFilter.format(path);
    if (!filePathFilter.isValid(path)) {
      try {
        if (Strings.isNullOrEmpty(path)) {
          return builder.status(Response.Status.BAD_REQUEST);
        }
        String key = new String(Base64.getUrlDecoder().decode(path));
        if (!Strings.isNullOrEmpty(name)) {
          name = decodeStr(name);
        } else {
          name = "BatchDownload.zip";
        }
        if (key.contains("ali") && !key.startsWith("https")) {
          String urlSuffix = key.replace("http://", "");
          key = warehouseBatchUrl + "/" + urlSuffix.substring(urlSuffix.indexOf("/") + 1);
          log.info("finalUrl:{}", key);
        }
        return batchDownloadByDocumentByStream(builder, key, name, ea);
      } catch (IllegalArgumentException e) {
        return builder.status(Response.Status.BAD_REQUEST);
      }
    }

    if (Strings.isNullOrEmpty(name)) {
      StoneFileGetOriginNamesRequest stoneFileGetOriginNamesRequest = new StoneFileGetOriginNamesRequest();
      stoneFileGetOriginNamesRequest.setPathList(Lists.newArrayList(path));
      stoneFileGetOriginNamesRequest.setEa(ea);
      try {
        if (path.startsWith("N_")) {
          StoneFileGetOriginNamesResponse response = stoneProxyApi.getFileName(stoneFileGetOriginNamesRequest);
          if (response.getPathOriginNames() != null && !Strings.isNullOrEmpty(response.getPathOriginNames().get(path))) {
            name = response.getPathOriginNames().get(path);
          }
        }
        if (Strings.isNullOrEmpty(name)) {
          name = path;
        }
      } catch (FRestClientException e) {
        log.warn("获取文件名失败,path:{},ea:{}", path, ea);
        name = path;
      }
    }

    CompatibleChunkDownloader compatibleChunkDownloader = fileRepository.downloadHeaderIconByPathCompatibleChunk(ea,
      sourceUser,
      path,
      securityGroup,
      oldUglyIndex,
      autofit,
      fileDownloadQueryVo.getFiletype(),
      fileDownloadQueryVo.getUa());

    HttpServletResponse response = ResteasyProviderFactory.getContextData(HttpServletResponse.class);
    response.addHeader("fs-isoriginalpic", String.valueOf(compatibleChunkDownloader.isOriginImage()));
    //基于流式处理
    if (compatibleChunkDownloader.isChunk() && compatibleChunkDownloader.isComplete()) {
      response.addHeader(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
      if (!Strings.isNullOrEmpty(name)) {
        try {
          byte[] firstChunkData = fileRepository.downloadFirstChunkFile(ea, sourceUser, path);
          if (FileMimeTypeHelper.isJpeg(firstChunkData)) {
            name = FilenameUtils.getBaseName(name) + ".jpg";
          }
          response.addHeader(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), name));
        } catch (Exception e) {
          log.error("下载异常,设置响应头失败!{},{},{},{}", ea, sourceUser, path, securityGroup, e);
        }
      }
      fileRepository.getChunkFileByStream(ea, sourceUser, path, securityGroup, compatibleChunkDownloader.getChunkIndexs());
      return null;
    }

    //传统基于ResponseBuilder内存处理
    byte[] data = compatibleChunkDownloader.getData();
    if (data == null) {
      if (needRealTimeThumbnailSwitch == 1) {
        //判断是否符合business的情形
        try {
          if (path.startsWith("N_")) {
            boolean needRealTimeThumbnail = fileRepository.checkNeedRealTimeThumbnail(ea, path);
            if (needRealTimeThumbnail) {
              //TODO 加蜂眼计数
              data = fileRepository.getThumbnailData(ea, sourceUser, path);
              if (data == null) {
                builder.status(Response.Status.NOT_FOUND);
              } else {
                setBuilder(builder, name, data, ea, sourceUser, path);
                builder.header("Entity-Length", data.length);
                builder.header(HttpHeaders.CONTENT_LENGTH, data.length);
              }
            } else {
              builder.status(Response.Status.NOT_FOUND);
            }
          } else {
            builder.status(Response.Status.NOT_FOUND);
          }
        } catch (Exception e) {
          log.warn("checkNeedRealTimeThumbnail fail", e);
          builder.status(Response.Status.NOT_FOUND);
        }
      } else {
        builder.status(Response.Status.NOT_FOUND);
      }
    } else {
      setBuilder(builder, name, data, ea, sourceUser, path);
      builder.header("Entity-Length", data.length);
      builder.header(HttpHeaders.CONTENT_LENGTH, data.length);
    }
    return builder;
  }


  private String decodeStr(String str) {
    return str;
  }

  private void setBuilder(Response.ResponseBuilder builder, String name, byte[] data, String ea, String user, String path) {
    if (!Strings.isNullOrEmpty(name)) {
      if (FileMimeTypeHelper.isJpeg(data)) {
        name = FilenameUtils.getBaseName(name) + ".jpg";
      }
      builder.header(HttpHeaders.CONTENT_TYPE, FileTypeDectetUtils.getMimeType(data, "application/octet-stream"));
      try {
        builder.header(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), name));
        builder.header("Accept-Ranges", "bytes");
      } catch (Exception e) {
        log.error("format fileName fail:{}", name, e);
      }
    }
    builder.header(HttpHeaders.CONTENT_LENGTH, data != null ? data.length : 0);
    if (data != null) {
      if (data.length > 1024 * 1024 * 10) {
        log.warn("TooLargeFile,ea:{},user:{},path:{},size:{}", ea, user, path, SizeFormatter.format(data.length));
      }
      InputStream in = speedLimiter.wrapWithSpeedLimit(data, ea, user, path);
      builder.entity(in);
    }
  }

  @Override
  public Response.ResponseBuilder getByAvatarAndSize(String enterpriseAccount, String ticketUserId, String path, String size, String filetype, String ua) {
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    path = filePathFilter.format(path);

    if (!filePathFilter.isValid(path)) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    if (Strings.isNullOrEmpty(size) || !"30_30".equalsIgnoreCase(size) || !path.startsWith("N_")) {
      builder = getByPath(FileDownloadQueryVo.builder().ea(enterpriseAccount).sourceUser(ticketUserId).path(path).build());
    } else {
      try {
        String nbp = path.split("\\.")[0];
        String extension = path.split("\\.")[1];
        String thumbPath = String.format("%s.thumb.99.%s", nbp, extension);
        byte[] data = fileRepository.getByPath(enterpriseAccount, ticketUserId, thumbPath, null, filetype, ua);
        if (data == null) {
          String nPath = fileRepository.addThumbNail(enterpriseAccount, ticketUserId, thumbPath, null, Lists.newArrayList(), 30, 30);
          if (nPath == null) {
            builder = builder.status(Response.Status.NOT_FOUND);
          }
          builder = getByPath(FileDownloadQueryVo.builder().ea(enterpriseAccount).sourceUser(ticketUserId).path(nPath).build());
        } else {
          builder.entity(data);
          builder.header(HttpHeaders.CACHE_CONTROL, "max-age=*********");
        }
      } catch (Exception e) {
        log.error( "getByAvatarAndSize", e);
        return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
      }
    }
    return builder.type("image/jpeg");
  }

  @Override
  public Response.ResponseBuilder batchDownloadByDocumentByStream(Response.ResponseBuilder builder, String downloadUrl, String name, String ea) {
    log.info( "batchDownloadByDocumentByStream:,downloadUrl:{}", downloadUrl);
    String finalKey = downloadUrl;
    if (!finalKey.startsWith("https")) {
      String urlSuffix = downloadUrl.replace("http://", "");
      finalKey = warehouseBatchUrl + "/" + urlSuffix.substring(urlSuffix.indexOf("/") + 1);
      //todo 兼容脏数据的代码 下一个人看到的时候可以删掉这块逻辑
      if (finalKey.contains("fs-warehouse-batch-")) {
        finalKey = processDirtyData(finalKey);
      }
    }
    log.info("finalUrl:{}", finalKey);
    fileRepository.downloadZipByUrl(builder, BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), name), finalKey, ea);
    return builder;
  }

  @Override
  public Response.ResponseBuilder downloadByToken(String ea, String sourceUser, String fileToken, String fileType, String ua) {
    log.info( "downloadByToken:EA:{},sourceUser:{},fileToken:{}", ea, sourceUser, fileToken);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    if (Strings.isNullOrEmpty(fileToken)) {
      responseBuilder.status(Response.Status.BAD_REQUEST);
    } else {
      FileMetaData fileMetaData;
      ContainerRequestContext context = FSCContext.getCurrentRequestContext();
      if (fileToken.startsWith(preview_token_prefix)) {
        //网盘专用
        Guard guard = new Guard(preview_token_encrypt_key);
        String npathCode = fileToken.replace(preview_token_prefix, "");
        try {
          String npath = guard.decode(npathCode);
          //给审计日志准备参数
          context.setProperty("Path", npath);
          context.setProperty("isNetDisk", true);
          CompatibleChunkDownloader compatibleChunkDownloader = fileRepository.downloadHeaderIconByPathCompatibleChunk(ea,
            sourceUser,
            npath,
            "XiaoKeNetDisk",
            "",
            "",
            fileType,
            ua);
          if (compatibleChunkDownloader != null && compatibleChunkDownloader.getData() != null) {
            byte[] data = compatibleChunkDownloader.getData();
            InputStream stream = speedLimiter.wrapWithSpeedLimit(data, ea, sourceUser, npath);
            responseBuilder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM);
            responseBuilder.header(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(context, npath));
            responseBuilder.header(HttpHeaders.CONTENT_LENGTH, data.length);
            responseBuilder.header("Entity-Length", data.length);
            responseBuilder.entity(stream);
          } else {
            responseBuilder.status(Response.Status.NOT_FOUND);
          }
        } catch (Exception ex) {
          log.error("ea:{},fileToken:{}", ea, fileToken, ex);
          responseBuilder.status(Response.Status.BAD_REQUEST);
        }
        return responseBuilder;
      }
      CompatibleChunkDownloaderExtendToken extendToken = fileRepository.downloadByTokenCompatibleChunk(ea,
        sourceUser,
        fileToken,
        fileType,
        ua);
      fileMetaData = extendToken.getFileMetaData();
      CompatibleChunkDownloader compatibleChunkDownloader = extendToken.getCompatibleChunkDownloader();
      //给审计日志准备参数
      String path = extendToken.getPath();
      context.setProperty("Path", path);
      context.setProperty("isNetDisk", Objects.equals(extendToken.getSecurityGroup(), "XiaoKeNetDisk"));
      //基于流式处理
      if (compatibleChunkDownloader != null && compatibleChunkDownloader.isChunk() && compatibleChunkDownloader.isComplete()) {
        //设置响应头
        HttpServletResponse response = ResteasyProviderFactory.getContextData(HttpServletResponse.class);
        response.addHeader(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION,
          BrowserUtils.getFullDispositionName(context, fileMetaData.getName()));
        fileRepository.getChunkFileByStream(ea,
          sourceUser, path,
          extendToken.getSecurityGroup(),
          compatibleChunkDownloader.getChunkIndexs());
        return null;
      }
      //传统基于ResponseBuilder内存处理
      log.info( "downloadByToken:{}", fileMetaData);
      if (fileMetaData == null || fileMetaData.getData() == null) {
        responseBuilder.status(Response.Status.NOT_FOUND);
      } else {
        responseBuilder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM);
        responseBuilder.header(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(context, fileMetaData.getName()));
        byte[] data = fileMetaData.getData();
        responseBuilder.header(HttpHeaders.CONTENT_LENGTH, data.length);
        responseBuilder.header("Entity-Length", data.length);
        InputStream stream = speedLimiter.wrapWithSpeedLimit(data, ea, sourceUser, path);
        responseBuilder.entity(stream);
      }
    }
    return responseBuilder;
  }

  @Override
  public Response.ResponseBuilder shareFileDownloadByToken(String fileToken, String filetype, String ua) {
    log.info( "shareFileDownloadByToken:fileToken:{}", fileToken);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    if (Strings.isNullOrEmpty(fileToken)) {
      responseBuilder.status(Response.Status.BAD_REQUEST);
    } else {
      CompatibleChunkDownloaderExtendToken compatibleChunkDownloaderExtendToken = fileRepository.shareFileDownloadByTokenCompatibleChunk(fileToken,
        filetype,
        ua);
      FileMetaData metaData = compatibleChunkDownloaderExtendToken.getFileMetaData();
      CompatibleChunkDownloader compatibleChunkDownloader = compatibleChunkDownloaderExtendToken.getCompatibleChunkDownloader();

      //基于流式输出
      if (compatibleChunkDownloader != null && compatibleChunkDownloader.isChunk() && compatibleChunkDownloader.isComplete()) {
        //设置响应头
        HttpServletResponse response = ResteasyProviderFactory.getContextData(HttpServletResponse.class);
        response.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM);
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), metaData.getName()));
        fileRepository.getChunkFileByStream(compatibleChunkDownloaderExtendToken.getEA(),
          compatibleChunkDownloaderExtendToken.getSourceUser(),
          compatibleChunkDownloaderExtendToken.getPath(),
          compatibleChunkDownloaderExtendToken.getSecurityGroup(),
          compatibleChunkDownloader.getChunkIndexs());
        return null;
      }

      //基于ResponseBuilder内存输出
      log.info( "shareFileDownloadByToken:metaData:{}", metaData);
      if (metaData != null && metaData.getData() != null) {
        responseBuilder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM);
        responseBuilder.header(HttpHeaders.CONTENT_DISPOSITION, BrowserUtils.getFullDispositionName(FSCContext.getCurrentRequestContext(), metaData.getName()));
        responseBuilder.entity(metaData.getData());
      } else {
        responseBuilder.status(Response.Status.NOT_FOUND);
      }
    }
    return responseBuilder;
  }

  @Override
  public Response.ResponseBuilder docPreviewByPath(FileDownloadQueryVo fileDownloadQueryVo) {
    log.info(
      "docPreviewByPath:ea:{},sourceUser:{},path:{},securityGroup:{}",
      fileDownloadQueryVo.getEa(),
      fileDownloadQueryVo.getSourceUser(),
      fileDownloadQueryVo.getPath(),
      fileDownloadQueryVo.getSecurityGroup());
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    fileDownloadQueryVo.setPath(filePathFilter.format(fileDownloadQueryVo.getPath()));
    if (!filePathFilter.isValid(fileDownloadQueryVo.getPath())) {
      builder.status(Response.Status.BAD_REQUEST);
    } else {
      GetPreviewInfo.ResultData data = fileRepository.docPreviewByPath(fileDownloadQueryVo.getEa(),
        fileDownloadQueryVo.getSourceUser(),
        fileDownloadQueryVo.getPath(),
        fileDownloadQueryVo.getSecurityGroup());
      log.info( "docPreviewByPath:GetPreviewInfo.ResultData:{}", data);

      if (data == null) {
        builder.status(Response.Status.BAD_REQUEST);
      } else {
        builder.entity(JsonUtils.toJson(data));
        builder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
      }

    }
    return builder;
  }

  @Override
  public Response.ResponseBuilder docPreviewByToken(String ea, String sourceUser, String fileToken) {
    log.info( "docPreviewByToken:ea:{},sourceUser:{},fileToken:{}", ea, sourceUser, fileToken);
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    if (Strings.isNullOrEmpty(fileToken)) {
      builder.status(Response.Status.BAD_REQUEST);
    } else {
      GetPreviewInfo.ResultData data = fileRepository.docPreviewByToken(ea, sourceUser, fileToken);
      log.info( "docPreviewByToken:GetPreviewInfo.ResultData:{}", data);
      if (data == null) {
        builder.status(Response.Status.BAD_REQUEST);
      } else {
        builder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
        builder.entity(JsonUtils.toJson(data));
      }
    }
    return builder;
  }

  @Override
  public Response.ResponseBuilder docPageByPath(String ea, String sourceUser, String path, String securityGroup, int pageIndex, int width,
                                                int maxContentLength) {
    log.info(
      "docPageByPath:ea:{},sourceUser:{},path:{},securityGroup:{},pageIndex:{}," + "maxContentLength:{}",
      ea,
      sourceUser,
      securityGroup,
      width,
      maxContentLength);
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    GetDocumentPageData.ResultData resultData = fileRepository.docPageByPath(ea, sourceUser, path, securityGroup, pageIndex, width, maxContentLength);
    byte[] data = null;
    if (resultData == null || resultData.buffer == null) {
      String msg = "Failed to read document preview";
      log.error( "docPageByPath:{} ,doc fetch failure,create blank ", path);
      String ext = FileHelper.getFileExt(path);
      if ("xls".equalsIgnoreCase(ext) || "xlsx".equalsIgnoreCase(ext)) {
        builder.header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_HTML);
        builder.header(HttpHeaders.CONTENT_ENCODING, "UTF-8");
        builder.entity(msg);
      } else {
        data = ImageUtils.createTextImage(msg, width, 200);
      }
      builder.entity(data);
    } else {
      log.info( "docPageByPath:{},content_type:{}", path, resultData.ContentType);
      if (!Strings.isNullOrEmpty(resultData.ContentType)) {
        builder.header(HttpHeaders.CONTENT_TYPE, resultData.ContentType);
      }
      data = resultData.buffer;
      builder.entity(data);
    }
    return builder;
  }

  @Override
  public Response.ResponseBuilder docPageByToken(String ea, String sourceUser, String token, int pageIndex, int width, int maxContentLength) {
    log.info(
      "docPageByToken:ea:{},sourceUser:{},token:{},pageIndex:{},width:{},maxContent:{}",
      ea,
      sourceUser,
      token,
      pageIndex,
      width,
      maxContentLength);
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    if (Strings.isNullOrEmpty(token)) {
      builder.status(Response.Status.BAD_REQUEST);
    } else {
      byte[] data;
      FileMetaData fileMetaData = fileRepository.docPageByToken(ea, sourceUser, token, pageIndex, width, maxContentLength);
      if (fileMetaData != null && "preview".equalsIgnoreCase(fileMetaData.getFileType())) {
        if (fileMetaData.getData() == null) {
          String msg = "Failed to read document preview";
          log.error( "docPageByPath ,doc fetch failure");
          String ext = FileHelper.getFileExt(fileMetaData.getToken().getFilePath());
          if ("xls".equalsIgnoreCase(ext) || "xlsx".equalsIgnoreCase(ext)) {
            builder.header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_HTML);
            builder.header(HttpHeaders.CONTENT_ENCODING, "UTF-8");
            builder.entity(msg);
          } else {
            data = ImageUtils.createTextImage(msg, width, 200);
            builder.entity(data);
          }
        } else {
          log.info( "docPageByToken:token:{},content_type:{},name:{}", token, fileMetaData.getFileType(), fileMetaData.getName());
          builder.header(HttpHeaders.CONTENT_TYPE, fileMetaData.getContentType());
          data = fileMetaData.getData();
          builder.entity(data);
        }

      } else {
        log.error( "docPageByToken ,doc fetch failure");
        builder.status(Response.Status.BAD_REQUEST);
      }
    }
    return builder;
  }

  @Override
  public Response.ResponseBuilder getByToken(String ea, String sourceUser, String fileToken, String filetype, String ua) {
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    if (Strings.isNullOrEmpty(fileToken)) {
      responseBuilder.status(Response.Status.BAD_REQUEST);
      return responseBuilder;
    } else {
      if (fileToken.startsWith(preview_token_prefix)) {
        Guard guard = new Guard(preview_token_encrypt_key);
        String npathCode = fileToken.replace(preview_token_prefix, "");
        try {
          String npath = guard.decode(npathCode);
          //给审计日志准备参数
          FSCContext.getCurrentRequestContext().setProperty("Path",npath);
          FSCContext.getCurrentRequestContext().setProperty("isNetDisk",true);
          log.info( "getByToken:ea:{},sourceUser:{},fileToken:{},npath:{}", ea, sourceUser, fileToken, npath);
          byte[] bytes = fileRepository.getByPath(ea, sourceUser, npath, "XiaoKeNetDisk", filetype, ua);
          if (bytes != null && bytes.length > 0) {
            getPreviewResponse(ea, sourceUser, npath, false, responseBuilder, bytes);
          } else {
            responseBuilder.status(Response.Status.NOT_FOUND);
          }
        } catch (Exception ex) {
          responseBuilder.status(Response.Status.BAD_REQUEST);
        }
        return responseBuilder;
      } else {
        CompatibleChunkDownloaderExtendToken compatibleChunkDownloaderExtendToken = fileRepository.downloadByTokenCompatibleChunk(ea,
          sourceUser,
          fileToken,
          filetype,
          ua);
        FileMetaData fileMetaData = compatibleChunkDownloaderExtendToken.getFileMetaData();
        CompatibleChunkDownloader compatibleChunkDownloader = compatibleChunkDownloaderExtendToken.getCompatibleChunkDownloader();
        //给审计日志准备参数
        FSCContext.getCurrentRequestContext().setProperty("Path",compatibleChunkDownloaderExtendToken.getPath());
        FSCContext.getCurrentRequestContext().setProperty("isNetDisk",Objects.equals(compatibleChunkDownloaderExtendToken.getSecurityGroup(),"XiaoKeNetDisk"));
        //基于流式处理
        if (compatibleChunkDownloader != null && compatibleChunkDownloader.isChunk() && compatibleChunkDownloader.isComplete()) {
          getPreviewResponseByStream(compatibleChunkDownloaderExtendToken.getPath());
          fileRepository.getChunkFileByStream(ea,
            sourceUser,
            compatibleChunkDownloaderExtendToken.getPath(),
            compatibleChunkDownloaderExtendToken.getSecurityGroup(),
            compatibleChunkDownloader.getChunkIndexs());
          return null;
        }
        //传统基于ResponseBuilder对象内存输出
        if (fileMetaData == null || fileMetaData.getData() == null) {
          responseBuilder.status(Response.Status.NOT_FOUND);
        } else {
          getPreviewResponse(ea, sourceUser, fileMetaData.getToken().getFilePath(), false, responseBuilder, fileMetaData.getData());
        }
        return responseBuilder;
      }
    }
  }

  @Override
  public Response.ResponseBuilder checkFileExist(String ea, String sourceUser, String hashValue, int size, String business) {
    log.info( "checkFileExist:ea:{},sourceUser:{},hashValue:{},size:{},business:{}", ea, sourceUser, hashValue, size, business);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    if (Strings.isNullOrEmpty(hashValue)) {
      responseBuilder.status(Response.Status.NOT_FOUND);
    }
    SameCodeResult sameCodeResult = fileRepository.checkFileExist(ea, sourceUser, hashValue, size, business);
    responseBuilder.entity(JsonUtils.toJson(sameCodeResult));
    responseBuilder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
    responseBuilder.header(HttpHeaders.CONTENT_ENCODING, "UTF-8");
    return responseBuilder;
  }

  @Override
  public Response.ResponseBuilder findEnterpriseRichText(String ea, String sourceId) {
    log.info( "findEnterpriseRichText:ea:{},sourceId:{}", ea, sourceId);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    if (Strings.isNullOrEmpty(sourceId)) {
      responseBuilder.status(Response.Status.NOT_FOUND);
    }
    ViewRichTextResult richText = fileRepository.findEnterpriseRichText(ea, sourceId);
    if (richText == null) {
      return responseBuilder.status(Response.Status.NOT_FOUND);
    }
    responseBuilder.entity(JsonUtils.toJson(richText));
    responseBuilder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
    responseBuilder.header(HttpHeaders.CONTENT_ENCODING, "UTF-8");
    return responseBuilder;
  }

  @Override
  public Response.ResponseBuilder findQRImage(String qrCode, int size) {
    log.info( "findQRImage:qrCode:{},size:{}", qrCode, size);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);

    String qrtUrl;
    try {
      qrtUrl = URLDecoder.decode(new String(Base64.getDecoder().decode(qrCode)), "UTF-8");
    } catch (UnsupportedEncodingException | IllegalArgumentException e) {
      log.warn( "findQRImage:{}", qrCode, e);
      return responseBuilder.status(Response.Status.BAD_REQUEST);
    }

    try {
      int minSize = 200;
      size = Math.min(minSize, size);
      String qrt = qrtUrl.split("\\?")[1].split("=")[1];
      if (Strings.isNullOrEmpty(qrt)) {
        return responseBuilder.status(Response.Status.NOT_FOUND);
      }
      QRImageStatus qrImageStatus = fileRepository.findQRImageStatus(qrt);
      if (qrImageStatus != null) {
        return responseBuilder.entity(QRCodeUtils.encodeToQRCode(qrtUrl, size, size)).header(HttpHeaders.CONTENT_TYPE, "image/png");
      } else {
        return responseBuilder.status(Response.Status.NOT_FOUND);
      }
    } catch (Exception e) {
      log.error( "findQRImage:{}", qrCode, e);
      return responseBuilder.status(Response.Status.NOT_FOUND);
    }
  }

  @Override
  public Response.ResponseBuilder qrShow(String encryptUrl, int size) {
    log.info( "qrShow:encryptUrl:{},size:{}", encryptUrl, size);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    int defaultSize = 200;
    if (size > 1000 || size < 30) {
      size = defaultSize;
    }
    if (Strings.isNullOrEmpty(encryptUrl)) {
      return responseBuilder.status(Response.Status.BAD_REQUEST);
    }
    Guard guard = new Guard(qrShowSkey);
    try {
      String originalUrl = guard.decode(encryptUrl);
      return responseBuilder.entity(QRCodeUtils.encodeToQRCode(originalUrl, size, size)).header(HttpHeaders.CONTENT_TYPE, "image/png");

    } catch (Exception e) {
      log.info( "encryptUrl:{} is invalid!", encryptUrl);
      return responseBuilder.status(Response.Status.BAD_REQUEST);
    }
  }

  @Override
  public Response.ResponseBuilder nBatchDownload(String ea, String downloadUser, String document, String securityGroup) {
    log.info( "nBatchDownload:ea:{},downloadUser:{},document:{}", ea, downloadUser, document);
    Response.ResponseBuilder builder = Response.status(Response.Status.OK).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
    try {
      byte[] data = fileRepository.nBatchDownload(ea, downloadUser, document, securityGroup);
      String tNPath = fileRepository.nTempFileUpload(ea, downloadUser, data, "", false, null, null, false);
      Map<String, String> result = Maps.newHashMap();
      result.put("TempFileName", tNPath);
      result.put("FileExtension", "zip");
      result.put("Code", "0");
      log.info( "nBatchDownload:result:{}", result);
      return builder.entity(JsonUtils.toJson(result));
    } catch (FsiClientException e) {
      return builder.status(Response.Status.BAD_REQUEST).entity(e.getMessage());
    }
  }

  @Override
  public String nTempFileUpload(String ea, String sourceUser, byte[] data, String ext, String business, boolean needThumbnail, String[] water,
                                                  boolean needCdn) {
    log.info( "nTempFileUpload start,ea:{},sourceUser:{},size:{},ext:{}", ea, sourceUser,data.length, ext);
    String path = fileRepository.nTempFileUpload(ea, sourceUser, data, business, needThumbnail, water, ext, needCdn);
    log.info( "nTempFileUpload success,ea:{},sourceUser:{},size:{},path:{}",ea,sourceUser,data.length,path);
    return path;
  }

  @Override
  public Response.ResponseBuilder aTempFileUpload(String enterpriseAccount, int employeeId, String business, byte[] data, String ext) {
    log.info( "aTempFileUpload: start ea:{},employeeId:{},business:{},dataSize:{},ext:{}", enterpriseAccount, employeeId, business, SizeFormatter.format(data.length), ext);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    String path = fileRepository.aTempFileUpload(enterpriseAccount, employeeId, business, data);
    Map<String, String> map = Maps.newHashMap();
    log.info( "TempFileName:{}", path);
    map.put("TempFileName", path);
    map.put("FileExtension", ext);
    log.info( "aTempFileUpload: success,ea:{},employeeId:{},business:{},path:{}", enterpriseAccount, employeeId, business, path);
    return responseBuilder.status(Response.Status.OK).entity(JsonUtils.toJson(map)).header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
  }

  @Override
  public Response.ResponseBuilder gTempFileUpload(String sourceUser, byte[] data, String extension) {
    log.info( "gTempFileUpload: start,sourceUser:{},size:{},ext:{}", sourceUser,data.length, extension);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    String gPath = fileRepository.gTempFileUpload(sourceUser, data);
    Map<String, String> map = Maps.newHashMap();
    map.put("TempFileName", gPath);
    map.put("FileExtension", extension);
    log.info( "gTempFileUpload: success,,sourceUser:{},gPath:{}",sourceUser,gPath);
    return responseBuilder.status(Response.Status.OK).entity(JsonUtils.toJson(map)).header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
  }

  public Response.ResponseBuilder saveEnterpriseRichText(String ea, String sourceUser, RichTextPostData richTextPostData) {
    log.info( "saveEnterpriseRichText:ea:{},sourceUser:{},richTextPostData:{}", ea, sourceUser, richTextPostData);
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    String content = richTextPostData.content;
    String title = richTextPostData.title;
    Boolean isFreeView = richTextPostData.isFreeView;
    if (Strings.isNullOrEmpty(content) || title.length() > 50) {
      return builder.status(Response.Status.BAD_REQUEST);
    }
    String sourceId = fileRepository.saveEnterpriseRichText(ea, sourceUser, title, content, isFreeView);
    Map<String, String> result = Maps.newHashMap();
    result.put("SourceId", sourceId);
    builder.entity(JsonUtils.toJson(result)).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
    return builder;
  }

  @Override
  public void asyncBatchDownloadByDocument(AsyncResponse response, String fileToken, String enterpriseAccount, String sourceUser, String securityGroup,
                                           String downloadXml, String warehouseType) {
    Map<String, Object> map = Maps.newHashMap();
    try {
      NBatchDownloadFileAsyn.Result result = new NBatchDownloadFileAsyn.Result();
      if (FSSwitch.BATCH_DOWNLOAD_DUBBO_SERVER_ABLE || WarehouseType.A.equals(warehouseType)) {
        FilePackedArg filePackedArg = new FilePackedArg();
        filePackedArg.setDownloadSecurityGroup(securityGroup);
        filePackedArg.setDocuments(downloadXml);
        filePackedArg.setDownloadUser(sourceUser);
        filePackedArg.setEa(enterpriseAccount);
        filePackedArg.setWarehouseType(warehouseType);
        filePackedArg.setEmployId(Integer.parseInt(sourceUser.substring(sourceUser.indexOf(".") + 1)));
        String nativeIpPort = FSCUtils.getNativeIpPort();
        filePackedArg.setTag(nativeIpPort);
        filePackedArg.setBizType("FSC");
        FilePackedResult packedResult = filePackedService.getFilePackedResult(filePackedArg);
        result.setCode(packedResult.getCode());
        result.setDownloadURL(packedResult.getDownloadURL());
        result.setKey(packedResult.getKey());
        result.setMessage(packedResult.getMessage());
      } else {
        result = fileRepository.asyncBatchDownload(enterpriseAccount, sourceUser, securityGroup, downloadXml);
      }
      log.info( "asyncBatchDownloadByDocument FileToken is:{},package key is:{},result:{}", fileToken, result.getKey(), result);
      switch (result.getCode()) {
        case 0:
          map.put("status", 0);
          map.put("token", Base64.getUrlEncoder().encodeToString(result.getDownloadURL().getBytes()));
          response.resume(Response.ok().entity(JsonUtils.toJson(map)).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON).build());
          break;
        case -100:
          asyncFileBatchDownloadManager.listenerBatchDownloadStatus(response, result.getKey());
          break;
        default:
          map.put("status", 3);
          map.put("message", result.getMessage());
          response.resume(Response.ok().entity(JsonUtils.toJson(map)).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON).build());
          log.error( "asyncBatchDownloadByDocument:result:{},xml:{}", result, downloadXml);
          break;
      }
    } catch (Exception e) {
      map.put("status", 3);
      map.put("message", "file download fail");
      response.resume(Response.ok().entity(JsonUtils.toJson(map)).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON).build());
      log.error( "asyncBatchDownloadByDocument:xml:{}", downloadXml, e);
    }
  }

  @Override
  public void asyncBatchDownloadByToken(AsyncResponse response, String enterpriseAccount, String sourceUser, String fileToken) {
    DownloadFileToken downloadFileToken = fileRepository.getDownloadFileDetailByToken(enterpriseAccount, sourceUser, fileToken);
    if (downloadFileToken == null) {
      Map<String, Object> map = Maps.newHashMap();
      map.put("status", 3);
      map.put("message", "The information in the network disk does not exist," + fileToken);
      response.resume(Response.ok().entity(JsonUtils.toJson(map)).encoding("UTF-8").header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON).build());
    } else {
      String doc = downloadFileToken.getZippedFilesStructure();
      String securityGroup = downloadFileToken.getDownloadSecurityGroup();
      String downloadUser = downloadFileToken.getDownloadUser();
      this.asyncBatchDownloadByDocument(response,
        downloadFileToken.getFileToken(),
        downloadFileToken.getEA(),
        downloadUser,
        securityGroup,
        doc,
        downloadFileToken.getWarehouseType());
    }
  }

  @Override
  public Response.ResponseBuilder getAvatarByToken(String avatarToken, String index, Map<String, String> operate) {
    try {
      //            EA:employeeId:path
      String avatarDetail = AES256Utils.decode(avatarToken);
      String[] details = avatarDetail.split(":");
      if (details.length != 3) {
        return Response.status(Response.Status.BAD_REQUEST);
      }
      String enterprise = details[0];
      String employee = "E." + details[1];
      String path = details[2];
      if (!path.contains("\\.")) {
        if (!Strings.isNullOrEmpty(index)) {
          path += index + ".jpg";
        } else {
          path += ".jpg";
        }
      }
      return getByPath(FileDownloadQueryVo.builder().ea(enterprise).sourceUser(employee).path(path).build());
    } catch (Exception e) {
      log.error( "getAvatarByToken:token:{}", avatarToken, e);
      return Response.status(Response.Status.BAD_REQUEST);
    }
  }

  @Override
  public Response.ResponseBuilder getFileBySharedToken(String token, Map<String, String> operate) {
    try {
      //            EA:employeeId:path
      String avatarDetail = AES256Utils.decode(token);
      String[] details = avatarDetail.split(":");
      if (details.length < 3) {
        return Response.status(Response.Status.BAD_REQUEST);
      }
      String enterprise = details[0];
      String employee = "E." + details[1];
      String path = details[2];
      String securityGroup = null;
      if (details.length > 3) {
        securityGroup = details[3];
      }
      long createTime;
      long expireTime = 0;
      if (details.length > 4) {
        createTime = Long.parseLong(details[4]);
        if (details.length > 5) {
          expireTime = Long.parseLong(details[5]);
        }
        if (System.currentTimeMillis() - createTime > (expireTime > 0 ? expireTime : sharedTokenExpMills)) {
          return Response.status(Response.Status.BAD_REQUEST);
        }
      }

      DocumentFormat docType = FileHelper.getDocumentFormat(FileHelper.getFileExt(path), false);
      if (docType == DocumentFormat.Video) {
        Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
        String range = getRequestHeader("Range");
        boolean noRange = Strings.isNullOrEmpty(range);
        range = Objects.toString(range, "bytes=0-");
        int unitIndex = range.indexOf('=');
        if (unitIndex == -1) {
          log.warn("downloadByRange Param:{},{},{},range fmt error", enterprise, path, range);
          responseBuilder.status(Response.Status.REQUESTED_RANGE_NOT_SATISFIABLE);
        }
        String rangeList = range.substring(unitIndex + 1);
        if (rangeList.indexOf(',') != -1) {
          log.warn("downloadByRange Param:{},{},{},not support multipart range", enterprise, path, range);
          responseBuilder.status(Response.Status.REQUESTED_RANGE_NOT_SATISFIABLE);
        }
        try {
          int splitIndex = rangeList.indexOf('-');
          int start = Integer.parseInt(rangeList.substring(0, splitIndex));
          int end = splitIndex == rangeList.length() - 1 ? 0 : Integer.valueOf(rangeList.substring(splitIndex + 1)) + 1;
          RangeDownloadResult result = downloadByRange(FileDownloadQueryVo.builder()
            .ea(enterprise)
            .sourceUser(employee)
            .path(path)
            .ua(getRequestHeader("User-Agent"))
            .byteStartIndex(start)
            .byteEndIndex(end)
            .securityGroup("")
            .build());

          StreamingOutput stream = os -> {
            os.write(result.getRangeData());
            os.flush();
          };
          responseBuilder.status(noRange ? Response.Status.OK : Response.Status.PARTIAL_CONTENT)
            .header("Content-Range", "bytes " + result.getStart() + '-' + (result.getEnd() - 1) + '/' + result.getTotalSize())
            .header("Accept-Ranges", "bytes")
            .header(HttpHeaders.CONTENT_LENGTH, result.getRangeData().length)
            .header(HttpHeaders.ETAG, FileHelper.getMD5Fast(result.getRangeData()))
            .header(HttpHeaders.CONTENT_TYPE, FileHelper.getMime(FilenameUtils.getExtension(path)))
            .header(HttpHeaders.CACHE_CONTROL, "private, max-age=120")
            .entity(stream);

        } catch (Exception e) {
          log.error("downloadByRange Exception:{},{},{}", enterprise, path, range, e);
          responseBuilder.status(Response.Status.NOT_FOUND);
        }
        return responseBuilder;
      }
      return getByPath(FileDownloadQueryVo.builder().ea(enterprise).sourceUser(employee).path(path).securityGroup(securityGroup).build());
    } catch (Exception e) {
      log.error( "getFileBySharedToken:token:{}", token, e.getMessage());
      return Response.status(Response.Status.BAD_REQUEST);
    }
  }


  @Override
  public Response.ResponseBuilder downloadFileBySharedToken(String token, String name, boolean preview) {
    try {
      //            EA:employeeId:path
      String avatarDetail = AES256Utils.decode(token);
      String[] details = avatarDetail.split(":");
      if (details.length < 3) {
        return Response.status(Response.Status.BAD_REQUEST);
      }
      String enterprise = details[0];
      String employee = "E." + details[1];
      String path = details[2];
      String securityGroup = null;
      if (details.length > 3) {
        securityGroup = details[3];
      }
      long createTime;
      long expireTime = 0;
      if (details.length > 4) {
        createTime = Long.parseLong(details[4]);
        if (details.length > 5) {
          expireTime = Long.parseLong(details[5]);
        }
        if (System.currentTimeMillis() - createTime > (expireTime > 0 ? expireTime : sharedTokenExpMills)) {
          return Response.status(Response.Status.BAD_REQUEST);
        }
      }
      name = Strings.isNullOrEmpty(name) ? path : name;
      log.info( "downloadFileBySharedToken:ea:{},sourceUser:{},path:{},securityGroup:{},preview:{},name:{}", enterprise, employee, path, securityGroup, preview, name);
      return downloadByPath(FileDownloadQueryVo.builder()
              .ea(enterprise)
              .sourceUser(employee)
              .path(path)
              .securityGroup(securityGroup)
              .preview(preview)
              .build(),
        name);
    } catch (Exception e) {
      log.error( "downloadFileBySharedToken:token:{}", token, e);
      return Response.status(Response.Status.BAD_REQUEST);
    }
  }

  @Override
  public Response.ResponseBuilder chunkFileUploadData(String enterpriseAccount, String path, int chunkIndex, byte[] data) {
    log.info( "chunkFileUploadData:ea:{},path:{},chunkIndex:{},data:{}", enterpriseAccount, path, chunkIndex, data.length);
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    try {
      ChunkFileUploadDataResult result = fileRepository.chunkFileUploadData(enterpriseAccount, path, chunkIndex, data);
      return builder.status(Response.Status.OK).entity(JsonUtils.toJson(result)).header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
    } catch (Exception e) {
      log.error( "chunkFileUploadData:ea:{},path:{},chunkIndex:{},data:{}", enterpriseAccount, path, chunkIndex, data.length, e);
      return Response.status(Response.Status.BAD_REQUEST);
    }
  }

  @Override
  public Response.ResponseBuilder chunkFileUploadComplete(String enterpriseAccount, String ticketUserId, String path, String business) {
    log.info( "chunkFileUploadComplete:ea:{},ticketUserId:{},path:{},business:{}", enterpriseAccount, ticketUserId, path, business);
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    try {
      ChunkFileUploadCompleteResult result = fileRepository.chunkFileUploadComplete(enterpriseAccount, ticketUserId, path, business);
      return builder.status(Response.Status.OK).entity(JsonUtils.toJson(result)).header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
    } catch (Exception e) {
      log.error( "chunkFileUploadComplete:ea:{},ticketUserId:{},path:{},business:{}", enterpriseAccount, ticketUserId, path, business, e);
      return Response.status(Response.Status.BAD_REQUEST);
    }

  }

  @Override
  public Response.ResponseBuilder chunkFileUploadStart(String enterpriseAccount, String ticketUserId, String code, String extension, int chunkCount,
                                                       int chunkSize, int lastChunkSize, String business, boolean needThumbnail, String hashRule) {
    log.info("chunkFileUploadStart start,ea:{},ticketUserId:{},code:{},extension:{},chunkCount:{},chunkSize:{},lastChunkSize:{},business:{},needThumbnail:{},hashRule:{}",
      enterpriseAccount,
      ticketUserId,
      code,
      extension,
      chunkCount,
      chunkSize,
      lastChunkSize,
      business,
      needThumbnail,
      hashRule);
    Response.ResponseBuilder builder = Response.status(Response.Status.OK);
    try {
      ChunkFileUploadStartResult result = fileRepository.chunkFileUploadStart(enterpriseAccount,
        ticketUserId,
        code,
        extension,
        chunkCount,
        chunkSize,
        lastChunkSize,
        business,
        needThumbnail,
        hashRule);
      log.info("chunkFileUploadStart success,result:{}", result);
      return builder.status(Status.OK).entity(JsonUtils.toJson(result)).header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
    } catch (Exception e) {
      log.error("chunkFileUploadStart fail,ea:{},ticketUserId:{},code:{},extension:{},chunkCount:{},chunkSize:{},lastChunkSize:{},business:{},needThumbnail:{},hashRule:{}",
        enterpriseAccount,
        ticketUserId,
        code,
        extension,
        chunkCount,
        chunkSize,
        lastChunkSize,
        business,
        needThumbnail,
        hashRule,
        e);
      return Response.status(Response.Status.BAD_REQUEST);
    }
  }

  @Override
  public Response.ResponseBuilder avatarFileUpload(String ea, String sourceUser, byte[] data, String ext, String business, boolean rpcMDS) {
    log.info( "avatarFileUpload:ea:{},sourceUser:{},dataSize:{},ext:{},business:{},rpcMDS:{}", ea, sourceUser, data.length, ext, business, rpcMDS);
    Response.ResponseBuilder responseBuilder = Response.status(Response.Status.OK);
    try {
      String path = fileRepository.avatarFileUpload(ea, sourceUser, data, ext, business, rpcMDS);
      Map<String, String> map = Maps.newHashMap();
      map.put("Path", path);
      map.put("FileExtension", ext);
      return responseBuilder.status(Response.Status.OK).entity(JsonUtils.toJson(map)).header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
    } catch (Exception e) {
      log.error(
        "avatarFileUpload:ea:{},sourceUser:{},dataSize:{},ext:{},business:{},rpcMDS{}",
        ea,
        sourceUser,
        SizeFormatter.format(data.length),
        ext,
        business,
        rpcMDS,
        e);
      return Response.status(Response.Status.BAD_REQUEST);
    }

  }

  @Override
  public UploadAvatarResult avatarFileUpload1(String ea, String sourceUser, byte[] data, String ext, String business, boolean rpcMDS) {
    log.info( "avatarFileUpload1:ea:{},sourceUser:{},dataSize:{},ext:{},business:{},rpcMDS{}", ea, sourceUser, data.length, ext, business, rpcMDS);
    try {
      String path = fileRepository.avatarFileUpload(ea, sourceUser, data, ext, business, rpcMDS);
      if (Strings.isNullOrEmpty(path)) {
        return new UploadAvatarResult(null, null, false);
      }
      return new UploadAvatarResult(path, ext, true);
    } catch (Exception e) {
      log.error(
        "avatarFileUpload1:ea:{},sourceUser:{},dataSize:{},ext:{},business:{},rpcMDS:{}",
        ea,
        sourceUser,
        data.length,
        ext,
        business,
        rpcMDS,
        e);
      return new UploadAvatarResult(null, null, false);
    }

  }

  @Override
  public RangeDownloadResult downloadByRange(FileDownloadQueryVo fileDownloadQueryVo) {
    return fileRepository.downloadByRange(fileDownloadQueryVo.getEa(),
      fileDownloadQueryVo.getPath(),
      fileDownloadQueryVo.getSourceUser(),
      fileDownloadQueryVo.getSecurityGroup(),
      fileDownloadQueryVo.getByteStartIndex(),
      fileDownloadQueryVo.getByteEndIndex());
  }

  public static String charset(ByteArrayInputStream byteArrayInputStream) {
    String charset = "GBK";
    byte[] first3Bytes = new byte[3];
    try {
      boolean checked = false;
      BufferedInputStream bis = new BufferedInputStream(byteArrayInputStream);
      bis.mark(0);
      int read = bis.read(first3Bytes, 0, 3);
      if (read == -1) {
        bis.close();
        return charset; // 文件编码为 ANSI
      } else if (first3Bytes[0] == (byte) 0xFF && first3Bytes[1] == (byte) 0xFE) {
        charset = "UTF-16LE"; // 文件编码为 Unicode
        checked = true;
      } else if (first3Bytes[0] == (byte) 0xFE && first3Bytes[1] == (byte) 0xFF) {
        charset = "UTF-16BE"; // 文件编码为 Unicode big endian
        checked = true;
      } else if (first3Bytes[0] == (byte) 0xEF && first3Bytes[1] == (byte) 0xBB && first3Bytes[2] == (byte) 0xBF) {
        charset = "UTF-8"; // 文件编码为 UTF-8
        checked = true;
      }
      bis.reset();
      if (!checked) {
        while ((read = bis.read()) != -1) {
          if (read >= 0xF0) {
            break;
          }
          if (0x80 <= read && read <= 0xBF) // 单独出现BF以下的，也算是GBK
          {
            break;
          }
          if (0xC0 <= read && read <= 0xDF) {
            read = bis.read();
            if (0x80 <= read && read <= 0xBF) // 双字节 (0xC0 - 0xDF)
            // (0x80 - 0xBF),也可能在GB编码内
            {
              continue;
            } else {
              break;
            }
          } else if (0xE0 <= read && read <= 0xEF) {
            read = bis.read();
            if (0x80 <= read && read <= 0xBF) {
              read = bis.read();
              if (0x80 <= read && read <= 0xBF) {
                charset = "UTF-8";
                break;
              } else {
                break;
              }
            } else {
              break;
            }
          }
        }
      }
      bis.close();
    } catch (Exception e) {
      log.warn( "获取字符集失败");
      charset = "UTF-8";
    }
    log.info( "采用的字符集为: [" + charset + "]");
    return charset;
  }

  private String processDirtyData(String url) {
    List<String> list = Splitter.on("/").splitToList(url);
    StringBuilder finalUrl = new StringBuilder();
    list.forEach(i -> {
      if (i.contains("fs-warehouse-batch-")) {
        finalUrl.append("fs-warehouse-batch").append("/");
      } else {
        finalUrl.append(i).append("/");
      }
    });
    return finalUrl.substring(0, finalUrl.length() - 1);
  }

}
