package com.facishare.fsc.core.repository.impl;

import com.facishare.fsc.core.exceptions.ExceptionConstant;
import com.facishare.fsc.core.exceptions.FSCRemoteException;
import com.facishare.fsc.core.repository.GlobalRepository;
import com.facishare.fsi.proxy.model.global.AddIndustryContact;
import com.facishare.fsi.proxy.model.global.CheckImageCode;
import com.facishare.fsi.proxy.model.global.PRSignUp;
import com.facishare.fsi.proxy.model.global.PartnerRegister;
import com.facishare.fsi.proxy.model.global.config.GetEnterpriseConfigByEA;
import com.facishare.fsi.proxy.service.GlobalConfigService;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalCause;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Created by Aaron on 16/5/16.
 */
@Service("globalRepository")
@Slf4j
public class GlobalRepositoryImpl implements GlobalRepository {
    private  int CACHE_SIZE = 20000;
    private  int CACHE_MINUTES_TIME=60;
    private LoadingCache<String, GetEnterpriseConfigByEA.EnterpriseConfig> enterpriseConfigCache;
    @Autowired
    private GlobalConfigService globalConfigService;
    @Value("${fsc_enterprie_cache_config}")
    private String configName;

    @PostConstruct
    public void init() {
        ConfigFactory.getInstance().getConfig(configName, config -> loadConfig(config));
    }

    public void loadConfig(IConfig config) {
        log.info("====== start load FSC "+configName+"  config ======");
        this.CACHE_SIZE=config.getInt("cache.size");
        log.info("cache.size:"+ CACHE_SIZE);
        this.CACHE_MINUTES_TIME=config.getInt("cache.minutes.time");
        log.info("====== end load FSC "+configName+"  config ======");
        loadCache();
    }

    public void loadCache(){
        enterpriseConfigCache = CacheBuilder.newBuilder()
                .maximumSize(CACHE_SIZE)
                .expireAfterAccess(CACHE_MINUTES_TIME, TimeUnit.MINUTES)  //
                .removalListener(new EnterpriseConfigCacheRemoveListener())
                .build(new CacheLoader<String, GetEnterpriseConfigByEA.EnterpriseConfig>() {
                    @Override
                    public GetEnterpriseConfigByEA.EnterpriseConfig load(String key) throws Exception {
                        GetEnterpriseConfigByEA.Args arg=new GetEnterpriseConfigByEA.Args();
                        arg.EnterpriseAccount=key;
                        GetEnterpriseConfigByEA.EnterpriseConfig config=null;
                        config=globalConfigService.getEnterpriseConfigByEA(arg).EnterpriseConfig;
                        log.debug("LoadFspAddress EA:{}", key);
                        return config;
                    }
                });
    }

    public GetEnterpriseConfigByEA.EnterpriseConfig getEnterpriseConfigByEA(String enterpriseAccount) {
        try{
            return enterpriseConfigCache.get(enterpriseAccount);
        }catch (ExecutionException e){
            log.error("LoadFspAddress EA:{} ", enterpriseAccount);
            throw new FSCRemoteException("Exception when calling GDS to obtain enterprise information",e,ExceptionConstant.FSC_GDS);
        }
    }

    private class EnterpriseConfigCacheRemoveListener implements RemovalListener<String, GetEnterpriseConfigByEA.EnterpriseConfig> {
        @Override
        public void onRemoval(RemovalNotification<String, GetEnterpriseConfigByEA.EnterpriseConfig> notification) {

            if (notification.getCause() != RemovalCause.EXPLICIT) {
                log.warn("Enterprise monthYear Removed,cause:{} monthYear:{}",
                        notification.getCause(), notification.getValue());
            }
        }
    }
    @Override
    public String createImageCode(String userTag, String clientId, String epxId) {
        CheckImageCode.Arg arg = new CheckImageCode.Arg();
        arg.userTag = userTag;
        arg.clientId = clientId;
        arg.epxId = epxId;
        CheckImageCode.Result result = globalConfigService.checkImageCode(arg);
        if (!result.success) {
            throw new FSCRemoteException("Failed to call the Global service to generate the verification code", ExceptionConstant.FSC_DOC_PRE_VIEW);
        }
        return result.code;
    }

    @Override
    public void prSignUp(String mobile, String name, String company, String post) {
        PRSignUp.Arg arg = new PRSignUp.Arg();
        arg.company = company;
        arg.mobile = mobile;
        arg.name = name;
        arg.post = post;
        globalConfigService.pRSignUp(arg);
    }

    @Override
    public void partnerRegister(String company, String name, String post, String mobile, String product, String intention) {
        PartnerRegister.Arg arg = new PartnerRegister.Arg();
        arg.company = company;
        arg.mobile = mobile;
        arg.name = name;
        arg.post = post;
        arg.intention = intention;
        arg.product = product;
        globalConfigService.partnerRegister(arg);
    }

    @Override
    public boolean addIndustryContact(String contactName, String contactPhone) {
        AddIndustryContact.Arg arg = new AddIndustryContact.Arg();
        arg.contactName = contactName;
        arg.contactPhone = contactPhone;
        return globalConfigService.addIndustryContact(arg).addContactResult;
    }
}
