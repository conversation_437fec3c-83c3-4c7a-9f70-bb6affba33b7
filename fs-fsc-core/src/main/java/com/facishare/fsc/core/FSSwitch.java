package com.facishare.fsc.core;

import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Created by <PERSON> on 16/5/27.
 */
@Component
@Slf4j
public class FSSwitch {
    @Value("${fsc_switch_config}")
    public String configName;
    /**
     * second
     */
    public static volatile  int COMET_FILE_DOWNLOAD_TIMEOUT=30000;
    public static volatile int REAL_TIME_BATCH_DOWNLOAD_MAX_TIMEOUT =60;
    public static volatile boolean BATCH_DOWNLOAD_DUBBO_SERVER_ABLE=false;
    public static volatile boolean AVATAR_DUBBLE_WRITE_ABLE=false;
    public static volatile long ORGINIMAGE_DOWNLOAD_LIMIT_SIZE=512L*1024;
    public static volatile boolean ORGINIMAGE_DOWNLOAD_ABLE=true;
    public static volatile int AVATAR_REDIS_EXPIRETIME=3600*24*7;

    @PostConstruct
    public void init(){
        ConfigFactory.getInstance().getConfig(configName,config -> {
            log.info("======================"+configName+"===================");
            COMET_FILE_DOWNLOAD_TIMEOUT=config.getInt("COMET_FILE_DOWNLOAD_TIMEOUT",30000);
            REAL_TIME_BATCH_DOWNLOAD_MAX_TIMEOUT =config.getInt("REAL_TIME_BATCH_DOWNLOAD_MAX_TIMEOUT",60);
            log.info("COMET_FILE_DOWNLOAD_TIMEOUT:{}ms",COMET_FILE_DOWNLOAD_TIMEOUT);
            BATCH_DOWNLOAD_DUBBO_SERVER_ABLE=config.getBool("BATCH_DOWNLOAD_DUBBO_SERVER_ABLE",false);
            log.info("BATCH_DOWNLOAD_DUBBO_SERVER_ABLE:{}",BATCH_DOWNLOAD_DUBBO_SERVER_ABLE);
            AVATAR_DUBBLE_WRITE_ABLE=config.getBool("AVATAR_DUBBLE_WRITE_ABLE",false);
            log.info("AVATAR_DUBBLE_WRITE_ABLE:{}",AVATAR_DUBBLE_WRITE_ABLE);
            ORGINIMAGE_DOWNLOAD_ABLE=config.getBool("ORGINIMAGE_DOWNLOAD_ABLE",true);
            log.info("ORGINIMAGE_DOWNLOAD_ABLE:{}",ORGINIMAGE_DOWNLOAD_ABLE);
            ORGINIMAGE_DOWNLOAD_LIMIT_SIZE=config.getLong("ORGINIMAGE_DOWNLOAD_LIMIT_SIZE",512L*1024);
            log.info("ORGINIMAGE_DOWNLOAD_LIMIT_SIZE:{}",ORGINIMAGE_DOWNLOAD_LIMIT_SIZE);
            AVATAR_REDIS_EXPIRETIME=config.getInt("AVATAR_REDIS_EXPIRETIME",3600*24*7);
            log.info("AVATAR_REDIS_EXPIRETIME:{}",AVATAR_REDIS_EXPIRETIME);
            log.info("=======================================================");

        });
    }

}
