package com.facishare.fsc.core.repository;

import com.facishare.fsc.core.model.ChunkFileUploadCompleteResult;
import com.facishare.fsc.core.model.ChunkFileUploadDataResult;
import com.facishare.fsc.core.model.ChunkFileUploadStartResult;
import com.facishare.fsc.core.model.CompatibleChunkDownloader;
import com.facishare.fsc.core.model.CompatibleChunkDownloaderExtendToken;
import com.facishare.fsc.core.model.GetDocumentPageData;
import com.facishare.fsc.core.model.GetPreviewInfo;
import com.facishare.fsc.core.model.RangeDownloadResult;
import com.facishare.fsc.core.model.SameCodeResult;
import com.facishare.fsc.core.repository.model.FileMetaData;
import com.facishare.fsc.core.repository.model.ViewRichTextResult;
import com.facishare.fsc.core.storage.model.DownloadFileToken;
import com.facishare.fsc.core.storage.model.QRImageStatus;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NBatchDownloadFileAsyn;

import javax.ws.rs.core.Response;
import java.util.List;

/**
 * Created by Aaron on 16/5/9.
 */
public interface FileRepository {

  /**
   * 下载mp3接口
   */
  byte[] getMp3ByPath(String ea, String sourceUser, String path, String securityGroup);

  /**
   * 下载通用文件
   */
  byte[] getByPath(String ea, String sourceUser, String path, String securityGroup, String filetype, String ua);

  /**
   * 下载图片,和getByPath处理相同
   */
  byte[] viewImage(String ea, String sourceUser, String path, String securityGroup, String filetype, String ua);

  /**
   * 下载头像
   */
  byte[] downloadHeaderIconByPath(String ea,
                                  String sourceUser,
                                  String path,
                                  String securityGroup,
                                  String oldUglyIndex,
                                  String filetype,
                                  String ua);

  /**
   * 通过fileToken来进行文件的下载
   */
  FileMetaData downloadByToken(String ea, String sourceUser, String fileToken, String filetype, String ua);

  /**
   * 外发的文件下载,如外发到微信的,这个不需要验证票据
   */
  FileMetaData shareFileDownloadByToken(String fileToken, String filetype, String ua);

  /**
   * 验证相同md5的文件是否存在
   */
  SameCodeResult checkFileExist(String ea, String sourceUser, String md5Value, long size, String business);

  GetPreviewInfo.ResultData docPreviewByToken(String ea, String sourceUser, String token);

  GetPreviewInfo.ResultData docPreviewByPath(String ea, String sourceUser, String path, String securityGroup);

  GetDocumentPageData.ResultData docPageByPath(String ea,
                                               String sourceUser,
                                               String path,
                                               String securityGroup,
                                               int pageIndex,
                                               int width,
                                               int maxContentLength);

  FileMetaData docPageByToken(String ea,
                              String sourceUser,
                              String token,
                              int pageIndex,
                              int width,
                              int maxContentLength);

  DownloadFileToken getDownloadFileDetailByToken(String ea, String sourceId, String token);

  ViewRichTextResult findEnterpriseRichText(String ea, String sourceId);

  QRImageStatus findQRImageStatus(String qrt);

  String nTempFileUpload(String ea, String sourceUser, byte[] data, String business, boolean needThumbnail, String[] words, String ext,boolean needCdn);

  String nUploadDirect(String ea,String sourceUser,String npath, byte[] data, String business,String ext,boolean needCdn);

  String gTempFileUpload(String sourceUser, byte[] data);

  byte[] nBatchDownload(String ea, String sourceUser, String doc, String securityGroup);

  String saveEnterpriseRichText(String ea, String sourceUser, String title, String content, Boolean isFreeView);

  String saveFileFromTempFile(String ea,
                              String sourceUser,
                              String path,
                              String ext,
                              List<String> permissions,
                              String securityGroup,
                              boolean isFree);

  NBatchDownloadFileAsyn.Result asyncBatchDownload(String ea,
                                                   String sourceUser,
                                                   String downloadSecurityGroup,
                                                   String downloadXml);

  void downloadZipByUrl(Response.ResponseBuilder builder, String dispositionName, String downloadURL, String ea);

  String addThumbNail(String ea,
                      String sourceUser,
                      String path,
                      String securityGroup,
                      List<String> permession,
                      int width,
                      int height);


  String aTempFileUpload(String enterpriseAccount, int employeeId, String business, byte[] data);

  ChunkFileUploadStartResult chunkFileUploadStart(String ea,
                                                  String sourceUser,
                                                  String code,
                                                  String extension,
                                                  int chunkCount,
                                                  int chunkSize,
                                                  int lastChunkSize,
                                                  String business,
                                                  boolean needThumbnail,
                                                  String hashRule);

  ChunkFileUploadDataResult chunkFileUploadData(String ea, String path, int chunkIndex, byte[] data);

  ChunkFileUploadCompleteResult chunkFileUploadComplete(String ea, String sourceUser, String nPath, String business);

  CompatibleChunkDownloader getByPathCompatibleChunk(String ea,
                                                     String sourceUser,
                                                     String nPath,
                                                     String securityGroup,
                                                     String filetype,
                                                     String ua);

  void getChunkFileByStream(String ea, String sourceUser, String path, String securityGroup, Integer[] chunkIndexArr);

  CompatibleChunkDownloader downloadHeaderIconByPathCompatibleChunk(String ea,
                                                                    String sourceUser,
                                                                    String path,
                                                                    String securityGroup,
                                                                    String oldUglyIndex,
                                                                    String autofit,
                                                                    String filetype,
                                                                    String ua);

  CompatibleChunkDownloaderExtendToken downloadByTokenCompatibleChunk(String ea,
                                                                      String sourceUser,
                                                                      String fileToken,
                                                                      String filetype,
                                                                      String ua);

  CompatibleChunkDownloaderExtendToken shareFileDownloadByTokenCompatibleChunk(String fileToken,
                                                                               String filetype,
                                                                               String ua);

  String avatarFileUpload(String ea, String sourceUser, byte[] data, String ext, String business, boolean rpcMDS);

  boolean checkNeedRealTimeThumbnail(String ea, String nPath);

  byte[] getThumbnailData(String ea, String sourceUser, String nPath);

  RangeDownloadResult downloadByRange(String ea,
                                      String path,
                                      String sourceUser,
                                      String securityGroup,
                                      int byteStartIndex,
                                      int byteEndIndex);


  byte[] downloadFirstChunkFile(String ea, String sourceUser, String nPath);


  byte[] convertHeicToJpg(String ea, byte[] data);
}
