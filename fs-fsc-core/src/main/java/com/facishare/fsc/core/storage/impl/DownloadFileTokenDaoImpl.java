package com.facishare.fsc.core.storage.impl;

import com.facishare.fsc.core.storage.DownloadFileTokenDao;
import com.facishare.fsc.core.storage.model.DownloadFileToken;
import com.facishare.fsc.core.storage.model.FileTokenFields;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON> on 16/5/13.
 */
@Service("downloadFileTokenDao")
public class DownloadFileTokenDaoImpl extends BaseDao implements DownloadFileTokenDao {
    @Override
    public DownloadFileToken find(String ea, String fileToken, String downloadUesr) {
        Query<DownloadFileToken> query = createQuery(shareContentDB,DownloadFileToken.class);
        query.field(FileTokenFields.EA).equal(ea)
                .field(FileTokenFields.fileToken).equal(fileToken)
                .field(FileTokenFields.downloadUser).equal(downloadUesr);
        return query.get();
    }

}
