package com.facishare.fsc.core.storage.model;

import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

/**
 * Created by <PERSON> on 16/5/9.
 */
@Entity(value = "ShareFileTokens",noClassnameStored = true)
public class ShareFileToken extends FileToken {
    @Property(FileTokenFields.shareUser)
    private String shareUser;
    @Property(FileTokenFields.shareUserName)
    private String shareUserName;
    @Property(FileTokenFields.fileSize)
    private String fileSize;
    @Property(FileTokenFields.shareSecurityGroup)
    private String shareSecurityGroup;

    public String getShareUser() {
        return shareUser;
    }

    public void setShareUser(String shareUser) {
        this.shareUser = shareUser;
    }

    public String getShareUserName() {
        return shareUserName;
    }

    public void setShareUserName(String shareUserName) {
        this.shareUserName = shareUserName;
    }

    public String getShareSecurityGroup() {
        return shareSecurityGroup;
    }

    public void setShareSecurityGroup(String shareSecurityGroup) {
        this.shareSecurityGroup = shareSecurityGroup;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

}
