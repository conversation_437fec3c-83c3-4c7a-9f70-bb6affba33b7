package com.facishare.fsc.core.model;

import com.facishare.fsc.core.repository.model.FileMetaData;

/**
 * Created by z<PERSON><PERSON><PERSON> on 16/9/6.
 */
public class CompatibleChunkDownloaderExtendToken extends CompatibleChunkDownloader{

    private CompatibleChunkDownloader CompatibleChunkDownloader;
    private FileMetaData FileMetaData;
    private String Path;
    private String SecurityGroup;
    private String SourceUser;
    private String EA;

    public String getEA() {
        return EA;
    }

    public CompatibleChunkDownloaderExtendToken() {
    }

    public String getSecurityGroup() {
        return SecurityGroup;
    }

    public void setSecurityGroup(String securityGroup) {
        SecurityGroup = securityGroup;
    }

    public String getPath() {
        return Path;
    }

    public void setPath(String path) {
        Path = path;
    }

    public String getSourceUser() {
        return SourceUser;
    }

    public void setSourceUser(String sourceUser) {
        SourceUser = sourceUser;
    }


    public com.facishare.fsc.core.model.CompatibleChunkDownloader getCompatibleChunkDownloader() {
        return CompatibleChunkDownloader;
    }

    public void setCompatibleChunkDownloader(com.facishare.fsc.core.model.CompatibleChunkDownloader compatibleChunkDownloader) {
        CompatibleChunkDownloader = compatibleChunkDownloader;
    }
    public com.facishare.fsc.core.repository.model.FileMetaData getFileMetaData() {
        return FileMetaData;
    }

    public void setFileMetaData(com.facishare.fsc.core.repository.model.FileMetaData fileMetaData) {
        FileMetaData = fileMetaData;
    }

    public void setEA(String EA) {
        this.EA = EA;
    }
}
