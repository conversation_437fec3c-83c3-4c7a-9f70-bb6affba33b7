package com.facishare.fsc.core.model;

/**
 * 头像信息
 * Created by lirui on 2016-10-26 12:14.
 */
public class Avatar {
  private final String path;
  private final char index;
  private final boolean avatar;

  public Avatar(String path, char index, boolean avatar) {
    this.path = path;
    this.index = index;
    this.avatar = avatar;
  }

  public String getPath() {
    return path;
  }

  public char getIndex() {
    return index;
  }

  public boolean isAvatar() {
    return avatar;
  }

  @Override
  public String toString() {
    return "Avatar{" +
      "path='" + path + '\'' +
      ", index=" + index +
      ", avatar=" + avatar +
      '}';
  }
}
