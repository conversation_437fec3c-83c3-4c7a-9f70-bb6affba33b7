package com.facishare.fsc.core.remote;

import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.remote.client.FSHttpClient;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jboss.resteasy.spi.ResteasyProviderFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.function.UnaryOperator;

/**
 * Created by <PERSON> on 16/5/25.
 */
@Service
@Slf4j
public class AsyncFileStorageProxy {
  private static final String DEFAULT_LINK = "https://www.fxiaoke.com/mob/big-file-preview.html";
  private final FSHttpClient httpClient = new FSHttpClient();

  @Autowired
  private DownloadSpeedLimiter speedLimiter;

  public void downloadZipByUrl(Response.ResponseBuilder builder, String dispositionName, String downloadURL, String ea) {

    HttpServletRequest request = ResteasyProviderFactory.getContextData(HttpServletRequest.class);
    HttpServletResponse response = ResteasyProviderFactory.getContextData(HttpServletResponse.class);
    String ua = request.getHeader("User-Agent");
    log.info( "downloadZipByUrl:url:{},current,EA:{},ua:{} ", downloadURL, ea, ua);

    if (downloadURL.endsWith("aliyun")) {
      if (!Strings.isNullOrEmpty(ua) && (ua.contains("Darwin") || ua.contains("Android"))) {
        try {
          response.sendRedirect(DEFAULT_LINK);
        } catch (IOException e) {
          log.error( "redirect defaultLink fail, downloadZipByUrl:url:{}   error", DEFAULT_LINK, e);
        }
        return;
      }

      try {
        String aliDownloadLink = getALIDownloadUrl(builder, downloadURL);
        if (Strings.isNullOrEmpty(aliDownloadLink)) {
          log.error( "get aliyun downloadlink failed, downloadZipByUrl:url:{}   error", downloadURL);
        } else {
          response.sendRedirect(aliDownloadLink);
        }
      } catch (IOException e) {
        log.error( "get aliyun downloadlink failed,  downloadZipByUrl:url:{}   error", downloadURL, e);
      }
      return;
    }

    if (downloadURL.startsWith("https")) {
      try {
        response.sendRedirect(downloadURL);
        return ;
      } catch (IOException e) {
        log.error("s3 async download fail");
        return ;
      }
    }

    try {
      Map<String, String> headers = Maps.newHashMap();
      headers.put(HttpHeaders.CONTENT_DISPOSITION, dispositionName);
      downloadURL = downloadURL + "&ea=" + ea;
      log.info( "downloadZipByUrl:url:{},current,EA:{},ua:{} ", downloadURL, ea, ua);
      String path = downloadURL;
      UnaryOperator<InputStream> wrapper = in -> speedLimiter.wrapWithSpeedLimit(in, ea, null, path);
      httpClient.get(downloadURL, builder, headers, null, wrapper);
    } catch (Exception e) {
      if (!downloadURL.startsWith("http")) {
        log.warn( "async call batchdownload failed, downloadZipByUrl:url:{}   error", downloadURL, e);
      } else {
        log.error( "async call batchdownload failed, downloadZipByUrl:url:{}   error", downloadURL, e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  String getALIDownloadUrl(Response.ResponseBuilder builder, String downloadURL) {
    log.info( "getALIDownloadUrl:url:{}", downloadURL);
    Map<String, String> headers = Maps.newHashMap();
    String aliyunLink = httpClient.getString(downloadURL, builder, headers, null);
    if (!Strings.isNullOrEmpty(aliyunLink)) {
      Map<String, String> resultMap = JsonUtils.fromJson(aliyunLink, Map.class);
      return resultMap.get("url");
    }
    return null;
  }

}
