package com.facishare.fsc.core.exceptions;

/**
 * Created by <PERSON> on 16/5/9.
 */
public class FSCException extends RuntimeException {
    public int code;
    public FSCException(int code)
    {
        super(ExceptionConstant.getExceptionTip(code));
        this.code = code;
    }
    public FSCException(String message,int code)
    {
        super(message);
        this.code = code;
    }

    public FSCException(String message, Throwable cause, int code) {
        super(message, cause);
        this.code = code;
    }

    public FSCException(Throwable cause, int code) {
        super(ExceptionConstant.getExceptionTip(code), cause);
        this.code = code;
    }
}
