package com.facishare.fsc.core.exceptions;

import com.github.autoconf.ConfigFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by <PERSON> on 16/5/9.
 */
public class ExceptionConstant {
    public static final int FSC_DOC_PRE_VIEW = 20001;
    public static final int FSC_IMAGE_CODE = 20002;
    public static final int FSC_FILE_CODE = 20003;
    public static final int FSC_GDS=20004;
    public static final int FSC_HTTP_REMOTE_REQUEST_CODE = 20005;
    public static final int DUBBO_ARG_ERROR = 20006;
    public static final int FORM_ARG_ERROR = 20007;
    public static final int INVALID_EXTENSION = 20008;
    public static final int FILE_TYPE_CHECK_IO_ERROR = 20009;

    private static final Map<String, String> exceptionsMessageMap = new ConcurrentHashMap<String, String>();

    static {
        ConfigFactory.getInstance().getConfig("fs-fsc-constant-dict", config -> {
            exceptionsMessageMap.clear();
            exceptionsMessageMap.putAll(config.getAll());
        });
    }

    public static String getExceptionTip(int code) {
        return exceptionsMessageMap.getOrDefault(code + "", "General exception information");
    }
}
