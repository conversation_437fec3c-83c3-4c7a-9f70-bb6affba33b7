package com.facishare.fsc.core.model;

import com.facishare.fsc.common.utils.JsonUtils;
import com.google.common.base.MoreObjects;

import java.nio.charset.StandardCharsets;

/**
 * Created by <PERSON> on 16/5/9.
 */
public interface GetPreviewInfo {
    class Arg {
        public String EnterpriseAccount;
        public String DocPath;
        public String DownloadSecurityGroup;
        public String DownloadUser;
        public String EnterpriseCreateYearMonth;
        public String FSP;

        public Arg(String EA, String docPath, String downloadSecurityGroup, String downloadUser, String enterpriseCreateYearMonth, String FSP) {
            this.EnterpriseAccount = EA;
            DocPath = docPath;
            DownloadSecurityGroup = downloadSecurityGroup;
            DownloadUser = downloadUser;
            EnterpriseCreateYearMonth = enterpriseCreateYearMonth;
            this.FSP = FSP;
        }
    }

    class Result extends JsonResponse {
        public ResultData Data;

        public static Result getInstance(byte[] data) {
            try {
                return JsonUtils.fromJson(new String(data, StandardCharsets.UTF_8), Result.class);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    class ResultData {
        public String DocumentFormat;
        public String PreviewFormat;
        public int PageCount;
        public String ContentType;

        @Override
        public String toString() {
            return MoreObjects.toStringHelper(this)
                    .add("DocumentFormat", DocumentFormat)
                    .add("PreviewFormat", PreviewFormat)
                    .add("PageCount", PageCount)
                    .add("ContentType", ContentType)
                    .toString();
        }
    }
}
