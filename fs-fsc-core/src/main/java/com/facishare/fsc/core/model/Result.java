package com.facishare.fsc.core.model;

import lombok.Data;

/*
 统一返回结果类
 * @param <T> 返回数据类型 T
 * success 是否成功
 * code 返回码
 * message 返回信息
 * data 返回数据
 */
@Data
public class Result<T> {

  private boolean success;
  private Integer code;
  private String message;
  private T data;

  private Result(boolean success, Integer code, String message) {
    this.success = success;
    this.code = code;
    this.message = message;
  }

  private Result(boolean success, Integer code, String message, T data) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
  }

  public static <T> Result<T> ok(T data) {
    return new Result<>(true, 200, "success request", data);
  }

  public static Result<String> error(int code, String message) {
    return new Result<>(false, code, message);
  }
}
