package com.facishare.fsc.core.remote;

import com.facishare.fsc.common.utils.SourceUserUtils;
import com.facishare.fsc.core.model.RangeDownloadResult;
import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile;
import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFileByRange;
import com.facishare.fsi.proxy.model.warehouse.a.AGetFileMetaData;
import com.facishare.fsi.proxy.model.warehouse.a.ATempFileUpload;
import com.facishare.fsi.proxy.model.warehouse.a.User;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.google.common.base.MoreObjects;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * Created by <PERSON> on 16/5/4.
 */
@Service("aFileStorageProxy")
public class AFileStorageProxy {
  @Autowired
  private AFileStorageService storageService;

  public String tempFileUpload(String ea, int employId, String business, byte[] data) {
    ATempFileUpload.Arg arg = new ATempFileUpload.Arg();
    arg.setUser(new User(ea, employId));
    arg.setData(data);
    arg.setBusiness(Strings.isNullOrEmpty(business) ? "FSC" : business);
    return storageService.tempFileUpload(arg).getTempFileName();
  }

  public byte[] downloadTempFile(String ea, int employId, String tempFileName, String business, String securityGroup) {
    ADownloadFile.Arg arg = new ADownloadFile.Arg();
    arg.setBusiness(Strings.isNullOrEmpty(business) ? "FSC" : business);
    arg.setUser(new User(ea, employId));
    arg.setaPath(tempFileName);
    arg.setFileSecurityGroup(securityGroup);
    return storageService.downloadFile(arg).getData();
  }

  public byte[] downloadMp3(String ea, int employId, String path, String business, String securityGroup) {
    ADownloadFile.Arg arg = new ADownloadFile.Arg();
    arg.setBusiness(Strings.isNullOrEmpty(business) ? "FSC" : business);
    arg.setUser(new User(ea, employId));
    arg.setaPath(path.replace("amr", "mp3"));
    arg.setFileSecurityGroup(securityGroup);
    return storageService.downloadFileOfMp3(arg).getData();
  }

  public byte[] downloadFile(String ea, int employId, String path, String business, String securityGroup) {
    ADownloadFile.Arg arg = new ADownloadFile.Arg();
    arg.setBusiness(Strings.isNullOrEmpty(business) ? "FSC" : business);
    arg.setUser(new User(ea, employId));
    arg.setaPath(path);
    arg.setFileSecurityGroup(securityGroup);
    return storageService.downloadFile(arg).getData();
  }

  public long getFileSize(String ea, int employId, String fileName, String business, String securityGroup) {
    AGetFileMetaData.Arg arg = new AGetFileMetaData.Arg();
    arg.setFileName(fileName);
    arg.setBusiness(Strings.isNullOrEmpty(business) ? "FSC" : business);
    arg.setFileSecurityGroup(securityGroup);
    arg.setUser(new User(ea, employId));
    return storageService.getFileMetaData(arg).getSize();
  }

  public RangeDownloadResult downloadFileByRange(String ea,
                                                 String path,
                                                 String sourceUser,
                                                 String securityGroup,
                                                 int byteStartIndex,
                                                 int byteEndIndex) {
    ADownloadFileByRange.Arg arg = new ADownloadFileByRange.Arg();
    arg.setEa(ea);
    arg.setAPath(path);
    arg.setUser(new User(Objects.toString(ea, SourceUserUtils.getEa(sourceUser)), MoreObjects.firstNonNull(SourceUserUtils
      .getEmployeeId(sourceUser), 0)));
    arg.setFileSecurityGroup(securityGroup);
    arg.setByteStartIndex(byteStartIndex);
    arg.setByteEndIndex(byteEndIndex);
    arg.setBusiness("FSC");
    ADownloadFileByRange.Result result = storageService.downloadFileByRange(arg);
    return RangeDownloadResult.builder()
                              .rangeData(result.getData())
                              .start(result.getStartIndex())
                              .end(result.getEndIndex())
                              .totalSize(result.getTotalSize())
                              .build();
  }
}
