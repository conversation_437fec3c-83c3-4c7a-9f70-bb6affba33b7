package com.facishare.fsc.core.fascade;

import com.facishare.fsc.core.model.RangeDownloadResult;
import com.facishare.fsc.core.model.UploadAvatarResult;
import com.facishare.fsc.core.model.vo.FileDownloadQueryVo;

import javax.ws.rs.container.AsyncResponse;
import javax.ws.rs.core.Response;
import java.util.Map;

/**
 * Created by <PERSON> on 16/5/13.
 */
public interface FileResponseFascade {

  /**
   * 下载mp3
   */
  Response.ResponseBuilder getMp3ByPath(FileDownloadQueryVo fileDownloadQueryVo);

  /**
   * 预览文件
   */
  Response.ResponseBuilder getByPath(FileDownloadQueryVo fileDownloadQueryVo);

  /**
   * 预览图片
   */
  Response.ResponseBuilder viewImage(FileDownloadQueryVo fileDownloadQueryVo);

  /**
   * 下载文件
   */
  Response.ResponseBuilder downloadByPath(FileDownloadQueryVo fileDownloadQueryVo, String name);

  /**
   * 下载头像
   */
  Response.ResponseBuilder downloadAvatarByPath(FileDownloadQueryVo fileDownloadQueryVo,
                                                String name,
                                                String oldUglyIndex,
                                                String autofit);

  /**
   * 通过token下载文件
   */
  Response.ResponseBuilder downloadByToken(String ea,
                                           String sourceUser,
                                           String fileToken,
                                           String filetype,
                                           String ua) throws Exception;

  /**
   * 下载分享的token
   */
  Response.ResponseBuilder shareFileDownloadByToken(String fileToken, String filetype, String ua);

  /**
   * 预览doc
   */
  Response.ResponseBuilder docPreviewByPath(FileDownloadQueryVo fileDownloadQueryVo);

  /**
   * 预览doc
   */
  Response.ResponseBuilder docPreviewByToken(String ea, String sourceUser, String fileToken);

  Response.ResponseBuilder docPageByPath(String ea,
                                         String sourceUser,
                                         String path,
                                         String securityGroup,
                                         int pageIndex,
                                         int width,
                                         int maxContentLength);

  Response.ResponseBuilder docPageByToken(String ea,
                                          String sourceUser,
                                          String token,
                                          int pageIndex,
                                          int width,
                                          int maxContentLength);

  /**
   * 文本和图片预览
   */
  Response.ResponseBuilder getByToken(String ea, String sourceUser, String fileToken, String filetype, String ua);

  /**
   * 验证文件是否存在,只用于企业内部文件
   */
  Response.ResponseBuilder checkFileExist(String ea, String sourceUser, String hashValue, int size, String business);

  Response.ResponseBuilder findEnterpriseRichText(String ea, String sourceId);

  Response.ResponseBuilder findQRImage(String qrt, int size);

  Response.ResponseBuilder qrShow(String encryptUrl, int size);

  Response.ResponseBuilder nBatchDownload(String ea, String sourceUser, String document, String securityGroup);

  String nTempFileUpload(String ea,
                                           String sourceUser,
                                           byte[] data,
                                           String ext,
                                           String business,
                                           boolean nTempFileUpload,
                                           String[] words,
                                            boolean needCdn);

//  Response.ResponseBuilder nUploadDirect(String ea,
//                                           String sourceUser,
//                                           String npath,
//                                           byte[] data,
//                                           String business,
//                                           String ext
//                                         );

  Response.ResponseBuilder gTempFileUpload(String ea, byte[] data, String extension);

  void asyncBatchDownloadByDocument(AsyncResponse response,
                                    String fileToken,
                                    String enterpriseAccount,
                                    String sourceUser,
                                    String securityGroup,
                                    String downloadXml,
                                    String warehouseType);

  void asyncBatchDownloadByToken(AsyncResponse response, String enterpriseAccount, String sourceUser, String fileToken);

  Response.ResponseBuilder getByAvatarAndSize(String enterpriseAccount,
                                              String ticketUserId,
                                              String path,
                                              String size,
                                              String filetype,
                                              String ua);


  Response.ResponseBuilder aTempFileUpload(String enterpriseAccount,
                                           int employeeId,
                                           String business,
                                           byte[] data,
                                           String ext);

  Response.ResponseBuilder batchDownloadByDocumentByStream(Response.ResponseBuilder builder,
                                                           String key,
                                                           String name,
                                                           String ea);

  Response.ResponseBuilder getAvatarByToken(String avatarToken, String index, Map<String, String> operate);

  Response.ResponseBuilder getFileBySharedToken(String token, Map<String, String> operate);

  Response.ResponseBuilder downloadFileBySharedToken(String token, String name, boolean preview);

  Response.ResponseBuilder chunkFileUploadData(String enterpriseAccount, String path, int chunkIndex, byte[] data);

  Response.ResponseBuilder chunkFileUploadComplete(String enterpriseAccount,
                                                   String ticketUserId,
                                                   String path,
                                                   String business);

  Response.ResponseBuilder chunkFileUploadStart(String enterpriseAccount,
                                                String ticketUserId,
                                                String code,
                                                String extension,
                                                int chunkCount,
                                                int chunkSize,
                                                int lastChunkSize,
                                                String business,
                                                boolean needThumbnail,
                                                String hashRule);

  Response.ResponseBuilder avatarFileUpload(String ea,
                                            String sourceUser,
                                            byte[] data,
                                            String ext,
                                            String business,
                                            boolean rpcMDS);

  UploadAvatarResult avatarFileUpload1(String ea,
                                       String sourceUser,
                                       byte[] data,
                                       String ext,
                                       String business,
                                       boolean rpcMDS);


  RangeDownloadResult downloadByRange(FileDownloadQueryVo fileDownloadQueryVo);
}
