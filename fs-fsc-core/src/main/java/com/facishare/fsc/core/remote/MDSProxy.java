package com.facishare.fsc.core.remote;

import com.facishare.fsi.proxy.model.mds.UploadProfileImage;
import com.facishare.fsi.proxy.service.ProfileAvatarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by z<PERSON><PERSON><PERSON> on 16/10/31.
 */
@Component
public class MDSProxy {
    @Autowired
    private ProfileAvatarService profileAvatarService;
    public void uploadProfileAvatar(String ea,Integer employeeId,String path){
        profileAvatarService.uploadProfileAvatar(new UploadProfileImage.Arg(employeeId,path),ea);
    }
}
