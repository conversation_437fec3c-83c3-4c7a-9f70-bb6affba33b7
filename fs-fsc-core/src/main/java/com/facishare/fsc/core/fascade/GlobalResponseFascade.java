package com.facishare.fsc.core.fascade;

import javax.ws.rs.core.Response;

/**
 * Created by <PERSON> on 16/5/16.
 */
public interface GlobalResponseFascade {
    Response.ResponseBuilder createImageCode(String userTag, String clientId, String epxId);
    Response.ResponseBuilder prSignUp(String mobile,String name,String company,String post);
    Response.ResponseBuilder partnerRegister(String company, String name, String post, String mobile, String product, String intention);
    Response.ResponseBuilder addIndustryContact(String contactName, String contactPhone);
}
