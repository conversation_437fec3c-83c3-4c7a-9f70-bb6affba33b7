package com.facishare.fsc.core.model;


import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 16/8/18.
 */
public class ChunkFileUploadStartResult {
    private Boolean IsExit;
    private String Path;
    private List<Chunk> ChunkedList;
    private boolean IsChunkFile;
    private int ChunkSize;

    public boolean isChunkFile() {
        return IsChunkFile;
    }

    public void setChunkFile(boolean chunkFile) {
        IsChunkFile = chunkFile;
    }

    public Boolean getExit() {
        return IsExit;
    }

    public void setExit(Boolean exit) {
        IsExit = exit;
    }

    public String getPath() {
        return Path;
    }

    public void setPath(String path) {
        Path = path;
    }

    public List<Chunk> getChunkedList() {
        return ChunkedList;
    }

    public void setChunkedList(List<Chunk> chunkedList) {
        ChunkedList = chunkedList;
    }

    public int getChunkSize() {
        return ChunkSize;
    }

    public void setChunkSize(int chunkSize) {
        ChunkSize = chunkSize;
    }

    public ChunkFileUploadStartResult(Boolean isExit, String path, List<com.facishare.fsi.proxy.model.warehouse.n.fileupload.Chunk> chunkedList, boolean isChunkFile, int chunkSize) {
        IsExit = isExit;
        Path = path;
        IsChunkFile=isChunkFile;
        ChunkSize=chunkSize;
        if(chunkedList !=null&& chunkedList.size()>0){
            ChunkedList= Lists.newArrayList();
            for (com.facishare.fsi.proxy.model.warehouse.n.fileupload.Chunk completedChunk: chunkedList) {
                ChunkedList.add(new Chunk(completedChunk.getIndex(),completedChunk.getSize()));
            }
        }
    }

}


