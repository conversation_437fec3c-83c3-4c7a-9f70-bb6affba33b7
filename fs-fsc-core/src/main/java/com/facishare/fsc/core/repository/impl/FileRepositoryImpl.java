package com.facishare.fsc.core.repository.impl;

import com.facishare.fsc.common.utils.FileHelper;
import com.facishare.fsc.common.utils.Guid;
import com.facishare.fsc.common.utils.HTMLUtils;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.FSSwitch;
import com.facishare.fsc.core.model.Avatar;
import com.facishare.fsc.core.model.ChunkFileUploadCompleteResult;
import com.facishare.fsc.core.model.ChunkFileUploadDataResult;
import com.facishare.fsc.core.model.ChunkFileUploadStartResult;
import com.facishare.fsc.core.model.CompatibleChunkDownloader;
import com.facishare.fsc.core.model.CompatibleChunkDownloaderExtendToken;
import com.facishare.fsc.core.model.GetDocumentPageData;
import com.facishare.fsc.core.model.GetPreviewInfo;
import com.facishare.fsc.core.model.RangeDownloadResult;
import com.facishare.fsc.core.model.SameCodeResult;
import com.facishare.fsc.core.model.SecurityGroups;
import com.facishare.fsc.core.remote.AsyncFileStorageProxy;
import com.facishare.fsc.core.remote.AvatarCheckService;
import com.facishare.fsc.core.remote.FileStorageProxy;
import com.facishare.fsc.core.remote.RedisService;
import com.facishare.fsc.core.repository.FileRepository;
import com.facishare.fsc.core.repository.model.FileMetaData;
import com.facishare.fsc.core.repository.model.ViewRichTextResult;
import com.facishare.fsc.core.storage.impl.DownloadFileTokenDaoImpl;
import com.facishare.fsc.core.storage.impl.EnterpriseRichTextDaoImpl;
import com.facishare.fsc.core.storage.impl.GlobalRichTextDaoImpl;
import com.facishare.fsc.core.storage.impl.QRImageStatusDaoImpl;
import com.facishare.fsc.core.storage.impl.ShareFileTokenDaoImpl;
import com.facishare.fsc.core.storage.model.DownloadFileToken;
import com.facishare.fsc.core.storage.model.EnterpriseRichText;
import com.facishare.fsc.core.storage.model.QRImageStatus;
import com.facishare.fsc.core.storage.model.RichText;
import com.facishare.fsc.core.storage.model.ShareFileToken;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NBatchDownloadFileAsyn;
import com.fxiaoke.metrics.CounterService;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.CharMatcher;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by Aaron on 16/5/9.
 */
@Service("FileFacadeImpl")
@Slf4j
public class FileRepositoryImpl implements FileRepository {
  private final Pattern pattern = Pattern.compile("[0-9a-f]{32}([0-4])");

  @Autowired
  private FileStorageProxy fileStorageProxy;
  @Autowired
  private AsyncFileStorageProxy asyncFileStorageProxy;
  @Autowired
  private DownloadFileTokenDaoImpl downloadFileTokenDao;
  @Autowired
  private ShareFileTokenDaoImpl shareFileTokenDao;
  @Autowired
  private EnterpriseRichTextDaoImpl enterpriseRichTextDao;
  @Autowired
  private GlobalRichTextDaoImpl globalRichTextDao;
  @Autowired
  private QRImageStatusDaoImpl qrImageStatusDao;
  @Autowired
  private AvatarCheckService avatarCheckService;

  @Autowired
  private RedisService redisService;

  @Autowired
  private CounterService counterService;
  /**
   * 灰度控制开关
   */
  private FsGrayReleaseBiz gray;

  @PostConstruct
  void init() {
    gray = FsGrayRelease.getInstance("fsc");
  }

  /**
   * Mp3下载
   */
  @Override
  public byte[] getMp3ByPath(String ea, String sourceUser, String path, String securityGroup) {
    return fileStorageProxy.downloadMp3(ea, sourceUser, path, securityGroup);
  }

  @Override
  public byte[] getByPath(String ea, String sourceUser, String path, String securityGroup, String filetype, String ua) {
    return fileStorageProxy.downloadFile(ea, sourceUser, path, securityGroup, filetype, ua);
  }

  @Override
  public byte[] viewImage(String ea, String sourceUser, String path, String securityGroup, String filetype, String ua) {
    return getByPath(ea, sourceUser, path, securityGroup, filetype, ua);
  }

  @Override
  public byte[] downloadHeaderIconByPath(String ea, String sourceUser, String path, String securityGroup, String oldUglyIndex, String filetype, String ua) {
    String basePath = FileHelper.getBasePath(path);
    byte[] data;
    if (!Strings.isNullOrEmpty(oldUglyIndex)) {
      String temp = basePath + oldUglyIndex + ".jpg";
      data = fileStorageProxy.downloadFile(ea, sourceUser, temp, securityGroup, filetype, ua);
      if (data == null) {
        temp = basePath + 1 + ".jpg";
        data = fileStorageProxy.downloadFile(ea, sourceUser, temp, securityGroup, filetype, ua);
      }
    } else {
      data = fileStorageProxy.downloadFile(ea, sourceUser, path, securityGroup, filetype, ua);
    }

    return data;
  }

  @Override
  public FileMetaData downloadByToken(String ea, String sourceUser, String token, String filetype, String ua) {
    if (token == null) {
      return null;
    }
    DownloadFileToken fileToken = downloadFileTokenDao.find(ea, token, sourceUser);
    if (fileToken == null) {
      log.info( "downloadByToken:token:{},not found", token);
      return null;
    }
    byte[] data = null;
    switch (fileToken.getFileType().toLowerCase()) {
      case "zip":
        data = fileStorageProxy.batchDownloadFile(ea, sourceUser, fileToken.getZippedFilesStructure(), fileToken.getDownloadSecurityGroup());
        break;
      case "tempfile":
        data = fileStorageProxy.downloadTempFile(ea, sourceUser, fileToken.getFilePath(), filetype);
        break;
      case "single":
      case "preview":
        data = fileStorageProxy.downloadFile(ea, sourceUser, fileToken.getFilePath(), fileToken.getDownloadSecurityGroup(), filetype, ua);
        break;
    }
    FileMetaData metaData = new FileMetaData(fileToken.getFileName(), fileToken.getFileType(), data);
    metaData.setToken(fileToken);
    return metaData;
  }


  @Override
  public FileMetaData shareFileDownloadByToken(String token, String filetype, String ua) {
    ShareFileToken fileToken = shareFileTokenDao.find(token);
    if (fileToken == null) {
      return null;
    }
    return new FileMetaData(fileToken.getFileName(),
      fileToken.getFileType(),
      fileStorageProxy.downloadFile(fileToken.getEA(), fileToken.getShareUser(), fileToken.getFilePath(), fileToken.getShareSecurityGroup(), filetype, ua));
  }

  @Override
  public SameCodeResult checkFileExist(String ea, String sourceUser, String md5Value, long size, String business) {
    return fileStorageProxy.findFileWithSameCode(ea, sourceUser, md5Value, size, business);
  }

  @Override
  public GetPreviewInfo.ResultData docPreviewByPath(String ea, String sourceUser, String path, String securityGroup) {
    GetPreviewInfo.ResultData resultData = null;
    if (path == null) {
      resultData = new GetPreviewInfo.ResultData();
    } else {
      resultData = fileStorageProxy.getPreviewInfo(ea, sourceUser, path, securityGroup);
    }
    return resultData;
  }

  @Override
  public GetPreviewInfo.ResultData docPreviewByToken(String ea, String sourceUser, String token) {
    DownloadFileToken fileToken = downloadFileTokenDao.find(ea, token, sourceUser);
    log.info( "docPreviewByToken:fileToken:{}", JsonUtils.toJson(fileToken));
    if (fileToken == null) {
      return new GetPreviewInfo.ResultData();
    } else if (!fileToken.getFileType().equalsIgnoreCase("preview")) {
      return null;
    }

    return docPreviewByPath(fileToken.getEA(), fileToken.getDownloadUser(), fileToken.getFilePath(), fileToken.getDownloadSecurityGroup());
  }

  @Override
  public GetDocumentPageData.ResultData docPageByPath(String ea, String employeeAccount, String path, String securityGroup, int pageIndex, int width,
                                                      int maxContentLength) {
    return fileStorageProxy.getDocumentPage(ea, employeeAccount, path, securityGroup, pageIndex, width, maxContentLength);
  }

  @Override
  public FileMetaData docPageByToken(String ea, String sourceId, String token, int pageIndex, int width, int maxContentLength) {
    DownloadFileToken fileToken = downloadFileTokenDao.find(ea, token, sourceId);
    log.info( "docPageByToken:fileToken:{}", JsonUtils.toJson(fileToken));
    if (fileToken == null) {
      return null;
    }
    GetDocumentPageData.ResultData data = fileStorageProxy.getDocumentPage(fileToken.getEA(),
      fileToken.getDownloadUser(),
      fileToken.getFilePath(),
      fileToken.getDownloadSecurityGroup(),
      pageIndex,
      width,
      maxContentLength);
    if (data == null) {
      return null;
    }
    FileMetaData metaData = new FileMetaData(fileToken.getFileName(), data.ContentType, fileToken.getFileType(), data.buffer);
    metaData.setToken(fileToken);
    return metaData;
  }

  @Override
  public DownloadFileToken getDownloadFileDetailByToken(String ea, String sourceId, String token) {
    DownloadFileToken fileToken = downloadFileTokenDao.find(ea, token, sourceId);
    log.info( "getDownloadFileDetailByToken:fileToken:{}", JsonUtils.toJson(fileToken));
    return fileToken;
  }

  @Override
  public ViewRichTextResult findEnterpriseRichText(String ea, String sourceId) {
    RichText richText = enterpriseRichTextDao.find(ea, sourceId);
    if (richText != null) {
      return new ViewRichTextResult(StringEscapeUtils.unescapeHtml(richText.getTitle()), StringEscapeUtils.unescapeHtml(richText.getContent()));
    } else {
      return null;
    }
  }

  @Override
  public byte[] nBatchDownload(String ea, String sourceUser, String doc, String securityGroup) {
    return fileStorageProxy.batchDownloadFile(ea, sourceUser, doc, securityGroup);
  }

  @Override
  public QRImageStatus findQRImageStatus(String qrt) {
    return qrImageStatusDao.find(qrt);
  }

  @Override
  public String nTempFileUpload(String ea, String sourceUser, byte[] data, String business, boolean needThumbnail, String[] water, String ext,
                                boolean needCdn) {
    return fileStorageProxy.nTempFileUpload(ea, sourceUser, data, business, needThumbnail, water, ext, needCdn);
  }

  @Override
  public String nUploadDirect(String ea, String sourceUser, String npath, byte[] data, String business, String ext, boolean needCdn) {
    return fileStorageProxy.nUploadDirect(ea, sourceUser, npath, data, business, ext, needCdn);
  }

  @Override
  public String aTempFileUpload(String ea, int employId, String business, byte[] data) {
    return fileStorageProxy.aTempFileUpload(ea, employId, business, data);
  }

  @Override
  public String gTempFileUpload(String sourceUser, byte[] data) {
    return fileStorageProxy.gTempFileUpload(sourceUser, data);
  }

  @Override
  public String saveEnterpriseRichText(String ea, String sourceUser, String title, String content, Boolean isFreeView) {
    content = HTMLUtils.filterHtml(content);
    Set<String> images = HTMLUtils.getRichTextImages(content);
    String newSrc;
    for (String src : images) {
      String tempSrc = src + "&";
      String tempFileName = HTMLUtils.getContent(tempSrc, "TempFileName=", "&");
      String tempFileExt = HTMLUtils.getContent(tempSrc, "TempFileExt=", "&");
      tempFileExt = tempFileExt.replace("\\", "");
      String nPath = this.saveFileFromTempFile(ea, sourceUser, tempFileName, tempFileExt, null, SecurityGroups.RichText, isFreeView);
      log.info( "tempFileName:{} ==> FileName:{}", tempFileName, nPath);
      if (!isFreeView) {
        newSrc = "/FSC/U/RichText/ViewPic?NPath=" + nPath;

      } else {
        newSrc = "/FSC/N/RichText/ViewPic?Path=" + nPath;
      }
      log.info( "src:{} replace FileName:{}", src, newSrc);
      content = content.replaceAll(src.replace("?", "[?]"), newSrc);
      log.info( "result:{} ", content);
    }
    EnterpriseRichText enterpriseRichText = new EnterpriseRichText();
    enterpriseRichText.setEA(ea);
    enterpriseRichText.setContent(StringEscapeUtils.escapeHtml(content));
    enterpriseRichText.setTitle(StringEscapeUtils.escapeHtml(title));
    enterpriseRichText.setSourceId(Guid.getGuid());
    return enterpriseRichTextDao.saveEnterpriseRichText(enterpriseRichText);
  }

  @Override
  public String saveFileFromTempFile(String ea, String sourceUser, String path, String ext, List<String> permissions, String securityGroup, boolean isFree) {
    return fileStorageProxy.saveFileFromTempFile(ea, sourceUser, path, ext, permissions, securityGroup, isFree);
  }

  @Override
  public NBatchDownloadFileAsyn.Result asyncBatchDownload(String ea, String sourceUser, String downloadSecurityGroup, String downloadXml) {
    return fileStorageProxy.asyncBatchDownload(ea, sourceUser, downloadSecurityGroup, downloadXml);
  }

  @Override
  public void downloadZipByUrl(Response.ResponseBuilder builder, String dispositionName, String downloadURL, String ea) {
    asyncFileStorageProxy.downloadZipByUrl(builder, dispositionName, downloadURL, ea);
  }

  @Override
  public String addThumbNail(String ea, String sourceUser, String path, String securityGroup, List<String> permission, int width, int height) {
    return fileStorageProxy.addThumbnail(ea, sourceUser, path, securityGroup, permission, height, width);
  }

  @Override
  public ChunkFileUploadStartResult chunkFileUploadStart(String ea, String sourceUser, String code, String extension, int chunkCount, int chunkSize,
                                                         int lastChunkSize, String business, boolean needThumbnail, String hashRule) {
    return fileStorageProxy.chunkFileUploadStart(ea, sourceUser, code, extension, chunkCount, chunkSize, lastChunkSize, business, needThumbnail, hashRule);
  }

  @Override
  public ChunkFileUploadDataResult chunkFileUploadData(String ea, String path, int chunkIndex, byte[] data) {
    return fileStorageProxy.chunkFileUploadData(ea, path, chunkIndex, data);
  }

  @Override
  public ChunkFileUploadCompleteResult chunkFileUploadComplete(String ea, String sourceUser, String path, String business) {
    return fileStorageProxy.chunkFileUploadComplete(ea, sourceUser, path, business);
  }

  @Override
  public CompatibleChunkDownloader getByPathCompatibleChunk(String ea, String sourceUser, String nPath, String securityGroup, String filetype, String ua) {
    Avatar avatar = null;
    boolean hitGray;
    // sourceUser传的很乱，这里做兼容
    if (sourceUser != null) {
      if (CharMatcher.is('.').countIn(sourceUser) == 2) {
        hitGray = gray.isAllow("avatar-upper", sourceUser);
      } else {
        String euid = sourceUser.charAt(0) == 'E' ? ea + sourceUser.substring(1) : ea + '.' + sourceUser;
        hitGray = gray.isAllow("avatar-upper", euid);
      }
      if (hitGray) {
        avatar = avatarCheckService.detect(nPath, ea);
      }
    }

    if (avatar != null && avatar.isAvatar()) {
      CompatibleChunkDownloader image = fileStorageProxy.downloadFileCompatibleChunk(ea, sourceUser, avatar.getPath() + "0.jpg", securityGroup, filetype, ua);
      if ((!image.isChunk() && image.getData() == null) || (image.isChunk() && !image.isComplete())) {
        counterService.inc("com.facishare.fsc.core.repository.impl.FileRepositoryImpl.getByPathCompatibleChunk.getAvatar0.fail");
        image = fileStorageProxy.downloadFileCompatibleChunk(ea, sourceUser, avatar.getPath() + "1.jpg", securityGroup, filetype, ua);
      }
      return image;
    } else {
      return fileStorageProxy.downloadFileCompatibleChunk(ea, sourceUser, nPath, securityGroup, filetype, ua);
    }
  }

  @Override
  public void getChunkFileByStream(String ea, String sourceUser, String path, String securityGroup, Integer[] chunkIndexArr) {
    fileStorageProxy.downloadChunkFileByStream(ea, sourceUser, path, securityGroup, chunkIndexArr);
  }

  @Override
  public CompatibleChunkDownloader downloadHeaderIconByPathCompatibleChunk(String ea, String sourceUser, String path, String securityGroup, String oldUglyIndex,
                                                                           String autofit, String filetype, String ua) {
    CompatibleChunkDownloader compatibleChunkDownloader;
    String basePath = FileHelper.getBasePath(path);
    String tempPath = path;

    boolean isOrigin = false;
    //协同图片
    if (Strings.isNullOrEmpty(oldUglyIndex) && FSSwitch.ORGINIMAGE_DOWNLOAD_ABLE && !Strings.isNullOrEmpty(autofit) && Boolean.valueOf(autofit)) {

      Matcher matcher = pattern.matcher(basePath);
      if (matcher.find()) {
        String idx = matcher.group(1);
        if (!"1".equals(idx)) {
          long fileSize = fileStorageProxy.getFileSize(ea, sourceUser, basePath.substring(0, basePath.lastIndexOf(idx)) + "1", securityGroup);
          if (fileSize > 0 && fileSize < FSSwitch.ORGINIMAGE_DOWNLOAD_LIMIT_SIZE) {
            String processFix = "";
            try {
              processFix = tempPath.substring(basePath.length());
            } catch (Exception e) {
              log.info("sub fix fail:{}", path);
            }
            basePath = basePath.substring(0, basePath.lastIndexOf(idx)) + "1";
            tempPath = basePath + processFix;
            isOrigin = true;
          }
          log.info("downLoadFileFilterHighQuality:{},{},{}", ea, path, fileSize);
        }
      }
    }

    if (!Strings.isNullOrEmpty(oldUglyIndex)) {
      String temp = basePath + oldUglyIndex + ".jpg";
      compatibleChunkDownloader = fileStorageProxy.downloadFileCompatibleChunk(ea, sourceUser, temp, securityGroup, filetype, ua);
      if ((!compatibleChunkDownloader.isChunk() && compatibleChunkDownloader.getData() == null) ||
        (compatibleChunkDownloader.isChunk() && !compatibleChunkDownloader.isComplete())) {
        temp = basePath + 1 + ".jpg";
        compatibleChunkDownloader = fileStorageProxy.downloadFileCompatibleChunk(ea, sourceUser, temp, securityGroup, filetype, ua);
      }
    } else {
      compatibleChunkDownloader = getByPathCompatibleChunk(ea, sourceUser, tempPath, securityGroup, filetype, ua);
      if (compatibleChunkDownloader != null) {
        compatibleChunkDownloader.setOriginImage(isOrigin);
      }
    }

    return compatibleChunkDownloader;
  }

  @Override
  public CompatibleChunkDownloaderExtendToken downloadByTokenCompatibleChunk(String ea, String sourceUser, String token, String filetype, String ua) {

    CompatibleChunkDownloaderExtendToken compatibleChunkDownloaderExtendToken = new CompatibleChunkDownloaderExtendToken();
    CompatibleChunkDownloader compatibleChunkDownloader = null;
    if (token == null) {
      return compatibleChunkDownloaderExtendToken;
    }
    DownloadFileToken fileToken = downloadFileTokenDao.find(ea, token, sourceUser);
    if (fileToken == null) {
      log.info( "downloadByToken:token:{},not found", token);
      return compatibleChunkDownloaderExtendToken;
    }

    byte[] data = null;
    switch (fileToken.getFileType().toLowerCase()) {
      case "zip":
        data = fileStorageProxy.batchDownloadFile(ea, sourceUser, fileToken.getZippedFilesStructure(), fileToken.getDownloadSecurityGroup());
        compatibleChunkDownloaderExtendToken.setSecurityGroup(fileToken.getDownloadSecurityGroup());
        break;
      case "tempfile":
        compatibleChunkDownloader = fileStorageProxy.downloadTempFileCompatibleChunk(ea, sourceUser, fileToken.getFilePath(), filetype, ua);
        data = compatibleChunkDownloader.getData();
        compatibleChunkDownloaderExtendToken.setPath(fileToken.getFilePath());
        break;
      case "single":
      case "preview":
        compatibleChunkDownloader = fileStorageProxy.downloadFileCompatibleChunk(ea,
          sourceUser,
          fileToken.getFilePath(),
          fileToken.getDownloadSecurityGroup(),
          filetype,
          ua);
        data = compatibleChunkDownloader.getData();
        compatibleChunkDownloaderExtendToken.setPath(fileToken.getFilePath());
        compatibleChunkDownloaderExtendToken.setSecurityGroup(fileToken.getDownloadSecurityGroup());
        break;
    }
    FileMetaData metaData = new FileMetaData(fileToken.getFileName(), fileToken.getFileType(), data);
    metaData.setToken(fileToken);

    compatibleChunkDownloaderExtendToken.setFileMetaData(metaData);
    compatibleChunkDownloaderExtendToken.setCompatibleChunkDownloader(compatibleChunkDownloader);
    return compatibleChunkDownloaderExtendToken;
  }

  @Override
  public CompatibleChunkDownloaderExtendToken shareFileDownloadByTokenCompatibleChunk(String token, String filetype, String ua) {
    CompatibleChunkDownloaderExtendToken compatibleChunkDownloaderExtendToken = new CompatibleChunkDownloaderExtendToken();
    ShareFileToken fileToken = shareFileTokenDao.find(token);
    if (fileToken == null) {
      return null;
    }
    CompatibleChunkDownloader compatibleChunkDownloader = fileStorageProxy.downloadFileCompatibleChunk(fileToken.getEA(),
      fileToken.getShareUser(),
      fileToken.getFilePath(),
      fileToken.getShareSecurityGroup(),
      filetype,
      ua);

    FileMetaData fileMetaData = new FileMetaData(fileToken.getFileName(), fileToken.getFileType(), compatibleChunkDownloader.getData());

    compatibleChunkDownloaderExtendToken.setPath(fileToken.getFilePath());
    compatibleChunkDownloaderExtendToken.setSourceUser(fileToken.getShareUser());
    compatibleChunkDownloaderExtendToken.setSecurityGroup(fileToken.getShareSecurityGroup());
    compatibleChunkDownloaderExtendToken.setFileMetaData(fileMetaData);
    compatibleChunkDownloaderExtendToken.setCompatibleChunkDownloader(compatibleChunkDownloader);
    compatibleChunkDownloaderExtendToken.setEA(fileToken.getEA());
    return compatibleChunkDownloaderExtendToken;
  }

  @Override
  public String avatarFileUpload(String ea, String sourceUser, byte[] data, String ext, String business, boolean rpcMDS) {
    return fileStorageProxy.avatarFileUpload(ea, sourceUser, data, ext, business, rpcMDS);
  }

  @Override
  public boolean checkNeedRealTimeThumbnail(String ea, String nPath) {
    return fileStorageProxy.checkNeeRealTimeThumbnail(ea, nPath);
  }

  @Override
  public byte[] getThumbnailData(String ea, String sourceUser, String nPath) {
    return fileStorageProxy.getThumbnailData(ea, sourceUser, nPath);
  }

  @Override
  public RangeDownloadResult downloadByRange(String ea, String path, String sourceUser, String securityGroup, int byteStartIndex, int byteEndIndex) {
    return fileStorageProxy.downloadFileByRange(ea, path, sourceUser, securityGroup, byteStartIndex, byteEndIndex);
  }

  @Override
  public byte[] downloadFirstChunkFile(String ea, String sourceUser, String nPath) {
    return fileStorageProxy.downloadFirstChunkFile(ea, sourceUser, nPath);
  }

  @Override
  public byte[] convertHeicToJpg(String ea, byte[] data) {
    return fileStorageProxy.convertHeicToJpg(ea, data);
  }
}
