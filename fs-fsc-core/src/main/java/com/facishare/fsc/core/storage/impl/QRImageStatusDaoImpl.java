package com.facishare.fsc.core.storage.impl;

import com.facishare.fsc.core.storage.QRImageStatusDao;
import com.facishare.fsc.core.storage.model.QRImageStatus;
import com.facishare.fsc.core.storage.model.QRImageStatusFields;
import com.github.mongo.support.DatastoreExt;
import org.mongodb.morphia.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON> on 16/5/17.
 */
@Service
public class QRImageStatusDaoImpl extends BaseDao implements QRImageStatusDao {
    @Autowired
    @Qualifier(value = "qrImageMongo")
    DatastoreExt qrImageMongo;
    public QRImageStatus find(String qrt) {
        qrImageMongo = qrImageMongo.use("QRLogin");
        Query<QRImageStatus> query = qrImageMongo.createQuery(QRImageStatus.class);
        query.field(QRImageStatusFields.qRtoken).equal(qrt);
        return query.get();
    }

}
