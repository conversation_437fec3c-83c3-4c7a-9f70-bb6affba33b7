package com.facishare.fsc.core.remote;

import com.facishare.fsc.common.utils.FSCUtils;
import com.facishare.fsc.core.model.ChunkFileUploadCompleteResult;
import com.facishare.fsc.core.model.ChunkFileUploadDataResult;
import com.facishare.fsc.core.model.ChunkFileUploadStartResult;
import com.facishare.fsc.core.model.CompatibleChunkDownloader;
import com.facishare.fsc.core.model.RangeDownloadResult;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.ConvertHeicToJpg;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NBatchDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NBatchDownloadFileAsyn;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NCheckNeedRealTimeThumbnail;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NChunkFilePullData;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NChunkFilePullStart;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NConvertFileToMp3;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NConvertFileToMp3.Arg;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFileByRange;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NFindFileWithSameCode;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetThumbnail;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetThumbnailData;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NSaveFileFromTempFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileDownload;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempUploadByChunkComplete;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempUploadByChunkPushData;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempUploadByChunkStart;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.jboss.resteasy.spi.ResteasyProviderFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created by Aaron on 16/5/4.
 */
@Service("nFileStorageProxy")
@Slf4j
public class NFileStorageProxy {

  @Autowired
  private NFileStorageService storageService;
  @Autowired
  private DownloadSpeedLimiter downloadSpeedLimiter;

  public RangeDownloadResult downloadFileByRange(String ea,
                                                 String path,
                                                 String sourceUser,
                                                 String securityGroup,
                                                 int byteStartIndex,
                                                 int byteEndIndex) {
    NDownloadFileByRange.Arg arg = new NDownloadFileByRange.Arg();
    arg.setEa(ea);
    arg.setNPath(path);
    arg.setDownloadUser(sourceUser);
    arg.setDownloadSecurityGroup(securityGroup);
    arg.setByteStartIndex(byteStartIndex);
    arg.setByteEndIndex(byteEndIndex);
    arg.setBusiness("FSC");
    NDownloadFileByRange.Result result = storageService.nDownloadFileByRange(arg, ea);
    return RangeDownloadResult.builder()
                              .rangeData(result.getData())
                              .start(result.getStartIndex())
                              .end(result.getEndIndex())
                              .totalSize(result.getTotalSize())
                              .build();
  }

  public byte[] downloadFile(String ea,
                             String sourceUser,
                             String nPath,
                             String securityGroup,
                             String filetype,
                             String ua) {
    NDownloadFile.Arg arg = new NDownloadFile.Arg();
    arg.setDownloadSecurityGroup(securityGroup);
    arg.setDownloadUser(sourceUser);
    arg.setEa(ea);
    arg.setnPath(nPath);
    arg.setUa(ua);
    arg.setFiletype(filetype);
    return storageService.nDownloadFile(arg, ea).getData();
  }

  public byte[] downloadMp3(String ea, String sourceUser, String nPath, String securityGroup) {
    NDownloadFile.Arg arg = new NDownloadFile.Arg();
    arg.setDownloadSecurityGroup(securityGroup);
    arg.setDownloadUser(sourceUser);
    arg.setEa(ea);
    arg.setnPath(nPath);
    return storageService.nDownloadFileOfMp3(arg, ea).getData();
  }

  public byte[] batchDownloadFile(String ea, String sourceUser, String documents, String securityGroup) {
    NBatchDownloadFile.Arg arg = new NBatchDownloadFile.Arg();
    arg.setEa(ea);
    arg.setDownloadUser(sourceUser);
    arg.setDocuments(documents);
    arg.setDownloadSecurityGroup(securityGroup);
    return storageService.nBatchDownloadFile(arg, ea).getData();
  }

  public byte[] downloadTempFile(String ea, String sourceUser, String tempFileName, String filetype) {
    NTempFileDownload.Arg arg = new NTempFileDownload.Arg();
    arg.setEa(ea);
    arg.setSourceUser(sourceUser);
    arg.setTempFileName(tempFileName);
    arg.setFiletype(filetype);
    return storageService.nTempFileDownload(arg, ea).getData();
  }

  public String saveFileFromTempFile(String ea,
                                     String sourceUser,
                                     String tempFileName,
                                     String ext,
                                     String securityGroup,
                                     boolean isFreeView) {
    NSaveFileFromTempFile.Arg arg = new NSaveFileFromTempFile.Arg();
    arg.setEa(ea);
    arg.setFileExt(ext);
    arg.setFileSecurityGroup(securityGroup);
    arg.setSourceUser(sourceUser);
    arg.setTempFileName(tempFileName);
    arg.setIsFreeFile(isFreeView);
    return storageService.nSaveFileFromTempFile(arg, ea).getFinalNPath();
  }

  public String tempFileUpload(String ea, String sourceUser, byte[] data, String business, boolean needThumbnail,String[] water, String ext,boolean needCdn) {
    NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
    arg.setBusiness(business);
    arg.setNeedThumbnail(needThumbnail);
    arg.setSourceUser(sourceUser);
    arg.setEa(ea);
    arg.setExtensionName(ext);
    arg.setData(data);
    arg.setWords(water);
    arg.setNeedCdn(needCdn);
    return storageService.nTempFileUpload(arg, ea).getTempFileName();
  }


  public String uploadDirect(String ea, String sourceUser, String npath,byte[] data, String business,String extension, boolean needCdn) {
    NUploadFileDirect.Arg arg = new NUploadFileDirect.Arg();
    arg.setBusiness(business);
    arg.setData(data);
    arg.setEa(ea);
    arg.setFileExt(extension);
    arg.setNamedFilePath(npath);
    arg.setSourceUser(sourceUser);
    arg.setNeedCdn(needCdn);
    return storageService.nUploadFileDirect(arg, ea).getFinalNPath();
  }

  public String getThumbnail(String ea,
                             String sourceUser,
                             String nPath,
                             String securityGroup,
                             List<String> fileAccessPermissions,
                             int toHeight,
                             int toWidth) {
    NGetThumbnail.Arg arg = new NGetThumbnail.Arg();
    arg.setEa(ea);
    arg.setSourceUser(sourceUser);
    arg.setFileAccessPermissions(fileAccessPermissions);
    arg.setnPath(nPath);
    arg.setToHeight(toHeight);
    arg.setToWidth(toWidth);
    arg.setFileSecurityGroup(securityGroup);
    return storageService.nGetThumbnail(arg, ea).getnPath();
  }

  public String addThumbnail(String ea,
                             String sourceUser,
                             String nPath,
                             String securityGroup,
                             List<String> fileAccessPermissions,
                             int toHeight,
                             int toWidth) {
    NGetThumbnail.Arg arg = new NGetThumbnail.Arg();
    arg.setEa(ea);
    arg.setSourceUser(sourceUser);
    arg.setFileAccessPermissions(fileAccessPermissions);
    arg.setnPath(nPath);
    arg.setToHeight(toHeight);
    arg.setToWidth(toWidth);
    arg.setFileSecurityGroup(securityGroup);
    return storageService.nAddChildFile(arg, ea).getnPath();
  }

  public NFindFileWithSameCode.Result findFileWithSameCode(String ea,
                                                           String sourceUser,
                                                           String code,
                                                           long size,
                                                           String business) {
    NFindFileWithSameCode.Arg arg = new NFindFileWithSameCode.Arg();
    arg.setEa(ea);
    arg.setSourceUser(sourceUser);
    arg.setBusiness(business);
    arg.setCode(code);
    arg.setSize(size);
    return storageService.nFindFileWithSameCode(arg, ea);
  }

  public NBatchDownloadFileAsyn.Result asyncBatchDownload(String ea,
                                                          String downloadUser,
                                                          String downlaodSecurityGroup,
                                                          String downloadXml) {
    NBatchDownloadFileAsyn.Arg arg = new NBatchDownloadFileAsyn.Arg();
    arg.setEa(ea);
    arg.setDownloadUser(downloadUser);
    arg.setDocuments(downloadXml);
    arg.setDownloadSecurityGroup(downlaodSecurityGroup);
    String nativeIpPort = FSCUtils.getNativeIpPort();
    arg.setTag(nativeIpPort);
    return storageService.nBatchDownloadFileAsyn(arg, ea);
  }


  /**
   * 拆分ChunkFileUploadResult为具体的方法
   */
  public ChunkFileUploadCompleteResult chunkFileUploadComplete(String ea,
                                                               String sourceUser,
                                                               String nPath,
                                                               String business) {

    NTempUploadByChunkComplete.Arg nTempUploadByChunkCompleteArg = new NTempUploadByChunkComplete.Arg();
    nTempUploadByChunkCompleteArg.setEa(ea);
    nTempUploadByChunkCompleteArg.setPath(nPath);
    nTempUploadByChunkCompleteArg.setSourceUser(sourceUser);
    NTempUploadByChunkComplete.Result nTempUploadByChunkCompleteResult = storageService.nTempUploadByChunkComplete(nTempUploadByChunkCompleteArg, ea);

    return new ChunkFileUploadCompleteResult(nTempUploadByChunkCompleteResult.isSuccess(), nTempUploadByChunkCompleteResult
      .getChunks());
  }

  public ChunkFileUploadStartResult chunkFileUploadStart(String ea,
                                                         String sourceUser,
                                                         String code,
                                                         String extension,
                                                         int chunkCount,
                                                         int chunkSize,
                                                         int lastChunkSize,
                                                         String business,
                                                         boolean needThumbnail,
                                                         String hashRule) {
    NTempUploadByChunkStart.Arg nTempUploadByChunkStartArg = new NTempUploadByChunkStart.Arg();
    nTempUploadByChunkStartArg.setEa(ea);
    nTempUploadByChunkStartArg.setBusiness(Strings.isNullOrEmpty(business) ? "FSC" : business);
    nTempUploadByChunkStartArg.setChunkCount(chunkCount);
    nTempUploadByChunkStartArg.setChunkSize(chunkSize);
    nTempUploadByChunkStartArg.setLastChunkSize(lastChunkSize);
    nTempUploadByChunkStartArg.setCode(code);
    nTempUploadByChunkStartArg.setNeedThumbnail(needThumbnail);
    nTempUploadByChunkStartArg.setExt(extension);
    nTempUploadByChunkStartArg.setSourceUser(sourceUser);
    nTempUploadByChunkStartArg.setHashRule(hashRule);
    NTempUploadByChunkStart.Result result = storageService.nTempUploadByChunkStart(nTempUploadByChunkStartArg, ea);

    return new ChunkFileUploadStartResult(result.isExit(), result.getPath(), result.getChunkedList(), result.isChunkFile(), result
      .getChunkSize());
  }

  /**
   * 索引从1开始
   */
  public ChunkFileUploadDataResult chunkFileUploadData(String ea, String nPath, int chunkIndex, byte[] data) {
    NTempUploadByChunkPushData.Arg nTempUploadByChunkPushDataArg = new NTempUploadByChunkPushData.Arg();
    nTempUploadByChunkPushDataArg.setEa(ea);
    nTempUploadByChunkPushDataArg.setPath(nPath);
    nTempUploadByChunkPushDataArg.setChunkIndex(chunkIndex);
    nTempUploadByChunkPushDataArg.setData(data);
    NTempUploadByChunkPushData.Result chunkPushDataResult = storageService.nTempUploadByChunkPushData(nTempUploadByChunkPushDataArg, ea);

    return new ChunkFileUploadDataResult(chunkPushDataResult.isSuccess(), chunkPushDataResult.getMessage());
  }


  public void downloadChunkFileByStream(String ea,
                                        String sourceUser,
                                        String nPath,
                                        String securityGroup,
                                        Integer[] chunkIndexArr) {

    HttpServletResponse response = ResteasyProviderFactory.getContextData(HttpServletResponse.class);
    try (ServletOutputStream outputStream = response.getOutputStream()) {
      NChunkFilePullData.Arg nChunkFilePullDataArg = new NChunkFilePullData.Arg();
      NChunkFilePullData.Result nChunkFilePullDataResult;
      for (Integer chunkIndex : chunkIndexArr) {
        nChunkFilePullDataArg.setChunkIndex(chunkIndex);
        nChunkFilePullDataArg.setEa(ea);
        nChunkFilePullDataArg.setPath(nPath);
        nChunkFilePullDataArg.setSourceUser(sourceUser);
        nChunkFilePullDataResult = storageService.nChunkFilePullData(nChunkFilePullDataArg, ea);
        byte[] from = nChunkFilePullDataResult.getData();
        downloadSpeedLimiter.copyWithSpeedLimit(from, outputStream, ea, sourceUser, nPath);
      }
    } catch (Exception ex) {
      log.error("文件流式下载异常:{},{},{},{}", ea, sourceUser, nPath, securityGroup, ex);
      response.setStatus(HttpServletResponse.SC_NOT_FOUND);
    }
  }

  public byte[] downloadFirstChunkFile(String ea, String sourceUser, String nPath) {
    NChunkFilePullData.Arg nChunkFilePullDataArg = new NChunkFilePullData.Arg();
    NChunkFilePullData.Result nChunkFilePullDataResult;
    nChunkFilePullDataArg.setChunkIndex(1);
    nChunkFilePullDataArg.setEa(ea);
    nChunkFilePullDataArg.setPath(nPath);
    nChunkFilePullDataArg.setSourceUser(sourceUser);
    nChunkFilePullDataResult = storageService.nChunkFilePullData(nChunkFilePullDataArg, ea);
    return nChunkFilePullDataResult.getData();
  }


  public CompatibleChunkDownloader chunkFilePullStart(String ea,
                                                      String sourceUser,
                                                      String nPath,
                                                      String securityGroup,
                                                      String filetype,
                                                      String ua) {
    NChunkFilePullStart.Arg pullStartArg = new NChunkFilePullStart.Arg();
    pullStartArg.setSourceUser(sourceUser);
    pullStartArg.setBusiness("FSC");
    pullStartArg.setPath(nPath);
    pullStartArg.setEa(ea);
    pullStartArg.setUa(ua);
    pullStartArg.setSecurityGroup(securityGroup);
    pullStartArg.setFiletype(filetype);
    NChunkFilePullStart.Result nChunkFilePullStartResult = storageService.nChunkFilePullStart(pullStartArg, ea);
    return new CompatibleChunkDownloader(nChunkFilePullStartResult.isChunk(), nChunkFilePullStartResult.isComplete(), nChunkFilePullStartResult
      .getChunksInfo(), nChunkFilePullStartResult.getData());
  }

  public String uploadAvatarFileDirect(String ea, String sourceUser, String namedFilePath, byte[] data, String ext) {
    NUploadFileDirect.Arg arg = new NUploadFileDirect.Arg();
    arg.setSourceUser(sourceUser);
    arg.setEa(ea);
    arg.setData(data);
    arg.setFileExt(ext);
    arg.setBusiness("FSC");
    arg.setNamedFilePath(namedFilePath);
    return storageService.nUploadFileDirect(arg, ea).getFinalNPath();
  }

  public boolean checkNeedRealTimeThumbnail(String ea, String nPath) {
    NCheckNeedRealTimeThumbnail.Arg arg = new NCheckNeedRealTimeThumbnail.Arg();
    arg.setEa(ea);
    arg.setPath(nPath);
    return storageService.nCheckNeddRealTimeThumbnail(arg, ea).isNeed();
  }

  public byte[] getThumbnailData(String ea, String sourceUser, String nPath) {
    try {
      NGetThumbnailData.Arg arg = new NGetThumbnailData.Arg();
      arg.setEa(ea);
      arg.setnPath(nPath);
      arg.setSourceUser(sourceUser);
      return storageService.nGetThumbnailData(arg, ea).getData();
    } catch (Exception e) {
      return null;
    }
  }

  public long getFileSize(String ea, String downUser, String securityGroup, String fileName) {
    NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg();
    arg.setEa(ea);
    arg.setDownloadSecurityGroup(securityGroup);
    arg.setDownUser(downUser);
    arg.setFileName(fileName);
    NGetFileMetaData.Result result = storageService.nGetFileMetaData(arg, ea);
    return result.getSize();
  }

  public String getExtensionName(String ea, String downUser, String securityGroup, String fileName){
    NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg();
    arg.setEa(ea);
    arg.setDownloadSecurityGroup(securityGroup);
    arg.setDownUser(downUser);
    arg.setFileName(fileName);
    NGetFileMetaData.Result result = storageService.nGetFileMetaData(arg, ea);
    return result.getExtensionName();
  }

  public byte[] convertFileToMp3(byte[] sourceData, String sourceType, String ea) {
    if (Strings.isNullOrEmpty(ea)) {
      ea = "fs";
    }
    NConvertFileToMp3.Arg arg = new Arg();
    arg.setData(sourceData);
    arg.setEa(ea);
    arg.setSourceType(sourceType);
    return storageService.convertFileToMp3(arg, ea).getData();
  }

  public byte[] convertHeicToJpg(String ea, byte[] data) {
    ConvertHeicToJpg.Arg arg = new ConvertHeicToJpg.Arg();
    arg.setData(data);
    return storageService.convertHeicToJpg(arg, ea).getData();
  }


}
