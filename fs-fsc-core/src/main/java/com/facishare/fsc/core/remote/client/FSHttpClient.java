package com.facishare.fsc.core.remote.client;

import com.facishare.fsc.core.exceptions.ExceptionConstant;
import com.facishare.fsc.core.exceptions.FSCRemoteException;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.config.SocketConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.function.UnaryOperator;

/**
 * Created by Aaron on 16/5/9.
 */
@Slf4j
public class FSHttpClient {

    protected static int DEFAULT_MAX_CONNECTION = 512;
    protected static int DEFAULT_MAX_PERROUTE_CONNECTION = 50;

    protected static int DEFAULT_SOCKET_TIMEOUT = 5000;
    protected static int DEFAULT_CONNECTION_TIMEOUT = 2000;

    private CloseableHttpClient httpClient;

    static {
        ConfigFactory.getInstance().getConfig("fs-fsc-httpclient-config", config -> {
            log.info("======================fs-fsc-httpclient-config load====================");
            DEFAULT_CONNECTION_TIMEOUT = config.getInt("DEFAULT_CONNECTION_TIMEOUT", 2000);
            log.info("DEFAULT_CONNECTION_TIMEOUT={}", DEFAULT_CONNECTION_TIMEOUT);
            DEFAULT_SOCKET_TIMEOUT = config.getInt("DEFAULT_SOCKET_TIMEOUT", 5000);
            log.info("DEFAULT_SOCKET_TIMEOUT={}", DEFAULT_SOCKET_TIMEOUT);
            DEFAULT_MAX_CONNECTION = config.getInt("DEFAULT_MAX_CONNECTION", 512);
            log.info("DEFAULT_MAX_CONNECTION={}",DEFAULT_MAX_CONNECTION);
            DEFAULT_MAX_PERROUTE_CONNECTION=config.getInt("DEFAULT_MAX_PERROUTE_CONNECTION",50);
            log.info("DEFAULT_MAX_PERROUTE_CONNECTION={}",DEFAULT_MAX_PERROUTE_CONNECTION);

            log.info("======================fs-fsc-httpclient-config end====================");
        });
    }
    public FSHttpClient() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(DEFAULT_MAX_CONNECTION);
        connectionManager.setDefaultMaxPerRoute(DEFAULT_MAX_PERROUTE_CONNECTION);

        SocketConfig.Builder sb = SocketConfig.custom();
        sb.setSoKeepAlive(true);
        sb.setTcpNoDelay(true);
        connectionManager.setDefaultSocketConfig(sb.build());

        HttpClientBuilder hb = HttpClientBuilder.create();
        hb.setConnectionManager(connectionManager);

        RequestConfig.Builder rb = RequestConfig.custom();
        rb.setSocketTimeout(DEFAULT_SOCKET_TIMEOUT);
        rb.setConnectTimeout(DEFAULT_CONNECTION_TIMEOUT);

        hb.setDefaultRequestConfig(rb.build());

        httpClient = hb.build();
    }

    public byte[] post(String uri, Map<String, String> headers, HttpEntity entity)
            throws IOException {
        RequestBuilder requestBuilder = RequestBuilder.post();
        requestBuilder.setUri(uri);

        for (Map.Entry<String, String> e : headers.entrySet()) {
            requestBuilder.addHeader(e.getKey(), e.getValue());
        }

       requestBuilder.setEntity(entity);
        try (CloseableHttpResponse response = httpClient.execute(requestBuilder.build())) {
            if (response.getStatusLine().getStatusCode() == 200) {
                return IOUtils.toByteArray(response.getEntity().getContent());
            } else {
                throw new FSCRemoteException("Error in calling remote service:url:" + uri,response.getStatusLine().getStatusCode());
            }
        }
    }

    public void get(String uri, Response.ResponseBuilder builder, Map<String, String> headers, HttpEntity entity, UnaryOperator<InputStream> wrapper)
            throws IOException {
        RequestBuilder requestBuilder = RequestBuilder.get();
        requestBuilder.setUri(uri);
        if (headers != null) {
            for (Map.Entry<String, String> e : headers.entrySet()) {
                builder.header(e.getKey(), e.getValue());
            }
        }
        if (entity != null) {
            requestBuilder.setEntity(entity);
        }
        try {
            // 异步读取信息，不要用try-close-resources语法，否则会出错
            CloseableHttpResponse response = httpClient.execute(requestBuilder.build());
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                InputStream in = wrapper.apply(response.getEntity().getContent());
                builder.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM).entity(in).build();
            } else {
                builder.status(response.getStatusLine().getStatusCode());
                log.error("Error in calling remote service:url:" + uri, response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            throw new FSCRemoteException("Error in calling remote service:url:" + uri, e, ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE);
        }
    }

    public String getString(String uri, Response.ResponseBuilder builder, Map<String, String> headers, HttpEntity entity) {
        RequestBuilder requestBuilder = RequestBuilder.get();
        requestBuilder.setUri(uri);
        if (headers != null) {
            for (Map.Entry<String, String> e : headers.entrySet()) {
                builder.header(e.getKey(), e.getValue());
            }
        }
        if (entity != null) {
            requestBuilder.setEntity(entity);
        }
        try (CloseableHttpResponse response = httpClient.execute(requestBuilder.build())){
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                InputStream in = response.getEntity().getContent();
                return IOUtils.toString(in, StandardCharsets.UTF_8);
            } else {
                builder.status(response.getStatusLine().getStatusCode());
                log.error("Error in calling remote service:url:" + uri, response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            throw new FSCRemoteException("Error in calling remote service:url:" + uri, e, ExceptionConstant.FSC_HTTP_REMOTE_REQUEST_CODE);
        }
        return null;
    }
}
