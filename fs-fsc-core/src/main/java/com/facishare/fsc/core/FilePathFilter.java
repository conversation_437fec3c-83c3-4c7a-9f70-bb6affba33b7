package com.facishare.fsc.core;

import com.fxiaoke.metrics.CounterService;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Pattern;

/**
 * Created by zhaifj on 16/11/22.
 */
@Component
@Slf4j
public class FilePathFilter {
    @Autowired
    private CounterService counterService;
    private Pattern pattern = Pattern.compile("\\.jpg\\d?\\.jpg");




    private List<String> validPathList;
    {
        ConfigFactory.getInstance().getConfig("fs-fsc-valid-path-prefix",config->{
            //getLines已经处理了空行
            validPathList=config.getLines();
        });
    }

    private boolean defaultValidPath(String clientPath){
        if (clientPath.startsWith("N_") ||clientPath.startsWith("TN_")|| clientPath.startsWith("TG_")
                || clientPath.startsWith("A_")|| clientPath.startsWith("TA_")
                || clientPath.startsWith("G_")||clientPath.startsWith("F_") ||clientPath.startsWith("S_")||clientPath.startsWith("TC_")||clientPath.startsWith("C_")) {
            return true;
        }
        return Splitter.on('_').splitToList(clientPath).size()==3;
    }



    public  boolean isValid(String path){
        if(Strings.isNullOrEmpty(path)){return false;}
        if(defaultValidPath(path)){return true;}
        if(validPathList!=null){
            for (String prefix:validPathList) {
                if(path.startsWith(prefix)){
                    return true;
                }
            }
        }
        counterService.inc("InvalidPath");
        return false;
    }

    public String format(String path){
        if(!Strings.isNullOrEmpty(path)){
            return pattern.matcher(path).replaceAll(".jpg");
        }
        return path;
    }

    public boolean isShareFile(String path){
        if(Strings.isNullOrEmpty(path)){return false;}
        return path.startsWith("S_");
    }
    public boolean isSambaFile(String path){
        return path!=null&&Splitter.on('_').splitToList(path).size()==3;
    }

    public boolean isNCFile(String path) {
        if (Strings.isNullOrEmpty(path)) {
            return false;
        }
        return path.startsWith("N_") || path.startsWith("C_") || path.startsWith("TN_") || path.startsWith("TC");
    }
}
