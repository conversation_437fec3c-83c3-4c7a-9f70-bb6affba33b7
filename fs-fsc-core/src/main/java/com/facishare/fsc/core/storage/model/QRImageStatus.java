package com.facishare.fsc.core.storage.model;

import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

import java.util.Date;

/**
 * Created by <PERSON> on 16/5/17.
 */
@Entity(value = "QRImageStatus",noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field(QRImageStatusFields.expireDate)},
                options = @IndexOptions(name = "expire",expireAfterSeconds=1))}
)
public class QRImageStatus {
    @Id
    private ObjectId _id;
    @Property(QRImageStatusFields.imageId)
    private String imageId;
    @Property(QRImageStatusFields.qRtoken)
    private String qRtoken;
    @Property(QRImageStatusFields.status)
    private String status;
    @Property(QRImageStatusFields.expireDate)
    private Date expireDate;
    @Property(QRImageStatusFields.ea)
    private String ea;
    @Property(QRImageStatusFields.employeeId)
    private String employeeId;
    @Property(QRImageStatusFields.targetEnterpriseAccount)
    private String targetEnterpriseAccount;
    @Property(QRImageStatusFields.targetPhoneNumber)
    private String targetPhoneNumber;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public String getqRtoken() {
        return qRtoken;
    }

    public void setqRtoken(String qRtoken) {
        this.qRtoken = qRtoken;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public String getEa() {
        return ea;
    }

    public void setEa(String ea) {
        this.ea = ea;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getTargetEnterpriseAccount() {
        return targetEnterpriseAccount;
    }

    public void setTargetEnterpriseAccount(String targetEnterpriseAccount) {
        this.targetEnterpriseAccount = targetEnterpriseAccount;
    }

    public String getTargetPhoneNumber() {
        return targetPhoneNumber;
    }

    public void setTargetPhoneNumber(String targetPhoneNumber) {
        this.targetPhoneNumber = targetPhoneNumber;
    }

    @Override
    public String toString() {
        return "QRImageStatus{" +
                "imageId='" + imageId + '\'' +
                ", qRtoken='" + qRtoken + '\'' +
                ", status='" + status + '\'' +
                ", expireDate=" + expireDate +
                ", ea='" + ea + '\'' +
                ", employeeId='" + employeeId + '\'' +
                ", targetEnterpriseAccount='" + targetEnterpriseAccount + '\'' +
                ", targetPhoneNumber='" + targetPhoneNumber + '\'' +
                '}';
    }
}
