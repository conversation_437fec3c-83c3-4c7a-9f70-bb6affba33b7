package com.facishare.fsc.core.storage.impl;

import com.facishare.fsc.core.storage.EnterpriseRichTextDao;
import com.facishare.fsc.core.storage.model.EnterpriseRichText;
import com.facishare.fsc.core.storage.model.RichTextFields;
import com.google.common.base.Strings;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON> on 16/5/16.
 */
@Service("enterpriseRichTextDao")
public class EnterpriseRichTextDaoImpl extends BaseDao implements EnterpriseRichTextDao {
    @Override
    public EnterpriseRichText find(String ea,String sourceId) {
        Query<EnterpriseRichText> query=createQuery(shareContentDB,EnterpriseRichText.class);
        query.field(RichTextFields.sourceId).equal(sourceId);
        if(!Strings.isNullOrEmpty(ea)){
            query.field(RichTextFields.EA).equal(ea);
        }
        return query.get();
    }

    @Override
    public String saveEnterpriseRichText(EnterpriseRichText enterpriseRichText) {
        return save(shareContentDB,enterpriseRichText).getSourceId();
    }
}
