package com.facishare.fsc.core.model;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/10/26.
 */
public class UploadAvatarResult {
    private String Path;
    private String FileExtension;
    private boolean Success;

    public UploadAvatarResult() {
    }

    public UploadAvatarResult(String path, String fileExtension, boolean success) {
        Path = path;
        FileExtension = fileExtension;
        Success = success;
    }

    public String getPath() {
        return Path;
    }

    public void setPath(String path) {
        Path = path;
    }

    public String getFileExtension() {
        return FileExtension;
    }

    public void setFileExtension(String fileExtension) {
        FileExtension = fileExtension;
    }

    public boolean isSuccess() {
        return Success;
    }

    public void setSuccess(boolean success) {
        Success = success;
    }
}
