package com.facishare.fsc.core.storage.model;

import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

/**
 * Created by <PERSON> on 16/5/9.
 */
@Entity(value = "DownloadFileTokens",noClassnameStored = true)
public class DownloadFileToken extends FileToken {
    @Property(FileTokenFields.downloadUser)
    private String downloadUser;
    @Property(FileTokenFields.downloadSecurityGroup)
    private String downloadSecurityGroup;
    @Property(FileTokenFields.zippedFilesStructure)
    private String zippedFilesStructure;

    public String getDownloadUser() {
        return downloadUser;
    }

    public void setDownloadUser(String downloadUser) {
        this.downloadUser = downloadUser;
    }

    public String getDownloadSecurityGroup() {
        return downloadSecurityGroup;
    }

    public void setDownloadSecurityGroup(String downloadSecurityGroup) {
        this.downloadSecurityGroup = downloadSecurityGroup;
    }

    public String getZippedFilesStructure() {
        return zippedFilesStructure;
    }

    public void setZippedFilesStructure(String zippedFilesStructure) {
        this.zippedFilesStructure = zippedFilesStructure;
    }

}
