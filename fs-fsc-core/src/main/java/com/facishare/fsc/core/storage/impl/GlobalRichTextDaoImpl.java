package com.facishare.fsc.core.storage.impl;

import com.facishare.fsc.core.storage.GlobalRichTextDao;
import com.facishare.fsc.core.storage.model.GlobalRichText;
import com.facishare.fsc.core.storage.model.RichTextFields;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON> on 16/5/18.
 */
@Service("globalRichTextDao")
public class GlobalRichTextDaoImpl extends BaseDao implements GlobalRichTextDao{
    @Override
    public GlobalRichText find(String sourceId) {
        Query<GlobalRichText> query=createQuery(shareContentDB,GlobalRichText.class);
        query.field(RichTextFields.sourceId).equal(sourceId);
        return query.get();
    }
}
