package com.facishare.fsc.core.processor.model;

import com.facishare.fsc.common.utils.JsonUtils;
import com.google.common.base.MoreObjects;

/**
 * Created by zhai<PERSON>j on 16/5/16.
 */
public class CompressFileDownloadEvent {
    private Integer code;
    private String message;
    private String key;
    private String downloadURL;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDownloadURL() {
        return downloadURL;
    }

    public void setDownloadURL(String downloadURL) {
        this.downloadURL = downloadURL;
    }
    public String toJson() {
        return JsonUtils.toJson(this);
    }

    public static CompressFileDownloadEvent fromJson(String json) {
        return JsonUtils.fromJson(json, CompressFileDownloadEvent.class);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("code", code).add("message", message)
                .add("key", key).add("downloadURL", downloadURL).toString();
    }

}
