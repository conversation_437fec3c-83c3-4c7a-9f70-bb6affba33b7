package com.facishare.fsc.core.remote;

import com.facishare.common.rocketmq.AutoConfRocketMQSender;
import com.facishare.fsc.core.exceptions.ExceptionConstant;
import com.facishare.fsc.core.exceptions.FSCRemoteException;
import com.facishare.fsc.core.model.ChunkFileUploadCompleteResult;
import com.facishare.fsc.core.model.ChunkFileUploadDataResult;
import com.facishare.fsc.core.model.ChunkFileUploadStartResult;
import com.facishare.fsc.core.model.CompatibleChunkDownloader;
import com.facishare.fsc.core.model.GetDocumentPageData;
import com.facishare.fsc.core.model.GetPreviewInfo;
import com.facishare.fsc.core.model.RangeDownloadResult;
import com.facishare.fsc.core.model.SameCodeResult;
import com.facishare.fsi.proxy.exception.FsiBusinessException;
import com.facishare.fsi.proxy.exception.FsiClientException;
import com.facishare.fsi.proxy.model.warehouse.micro.UploadFileDirect;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NBatchDownloadFileAsyn;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NFindFileWithSameCode;
import com.facishare.fsi.proxy.util.IgnoreErrorUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * Created by Aaron on 16/5/12.
 */
@Service("fileStorageProxy")
@Slf4j
public class FileStorageProxy {
  @Autowired
  private GFileStorageProxy gFileStorageProxy;
  @Autowired
  private NFileStorageProxy nFileStorageProxy;
  @Autowired
  private AFileStorageProxy aFileStorageProxy;
  @Autowired
  private AvatarFileStorageProxy avatarFileStorageProxy;
  @Autowired
  private EnterpriseService enterpriseService;

  @Autowired
  private MDSProxy mdsProxy;

  @Autowired
  private DocPreviewProxy previewProxy;
  private List<String> ignorePathList;


  private static final String KEY_NAME_SERVER = "NAMESERVER";
  private static final String KEY_GROUP = "GROUP";
  private static final String KEY_TOPICS = "TOPICS";

  private AutoConfRocketMQSender sender;

  @Autowired
  private RedisService redisService;


  @PostConstruct
  public void init() {
    sender = new AutoConfRocketMQSender("fs-fsc-trans-avatar-mq", KEY_NAME_SERVER, KEY_GROUP, KEY_TOPICS);
    sender.init();
  }


  {
    ConfigFactory.getInstance().getConfig("fs-fsc-ignore-download-path", config -> {
      ignorePathList = config.getLines();
    });
  }

  public long getFileSize(String ea, String sourceUser, String path, String securityGroup) {
    log.info( "getFileSize:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup);
    try {
      if (path.startsWith("G_")) {
        return -1;
      } else if (path.startsWith("A_") || path.startsWith("TA_")) {
        return aFileStorageProxy.getFileSize(ea, Integer.parseInt(sourceUser.substring(
          sourceUser.indexOf(".") + 1)), path, null, securityGroup);
      } else {
        return nFileStorageProxy.getFileSize(ea, sourceUser, securityGroup, path);
      }
    } catch (Exception e) {
      if(e instanceof FsiClientException){
        FsiClientException fsiException = (FsiClientException) e;
        if (fsiException.getMessage().contains("10007")) {
          log.warn( "getFileSize:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup, e);
        } else {
          log.error( "getFileSize:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup, e);
        }
      }else {
        log.error( "getFileSize:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup, e);
      }
      return -2;
    }
  }

  public byte[] downloadFile(String ea,
                             String sourceUser,
                             String path,
                             String securityGroup,
                             String filetype,
                             String ua) {
    if (ignorePathList.contains(path)) {
      log.info( "ignore downloadFile:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup);
      return null;
    }
    log.info( "downloadFile:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup);
    try {
      if (path.startsWith("G_")) {
        return gFileStorageProxy.downloadFile(sourceUser, path, securityGroup);
      } else if (path.startsWith("A_") || path.startsWith("TA_")) {
        return aFileStorageProxy.downloadFile(ea, Integer.parseInt(sourceUser.substring(
          sourceUser.indexOf(".") + 1)), path, null, securityGroup);
      } else {
        return nFileStorageProxy.downloadFile(ea, sourceUser, path, securityGroup, filetype, ua);
      }
    } catch (Exception e) {
      log.info(e.getClass().getName());
      if (e instanceof FsiClientException) {
        FsiClientException fsiException = (FsiClientException) e;
        if (IgnoreErrorUtil.ignoreError(fsiException.getMessage())) {
          log.warn( "downloadFile:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup, e);
        } else {
          log.error( "downloadFile:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup, e);
        }
      } else {
        log.error( "downloadFile:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup, e);
      }
      return null;
    }
  }

  public byte[] downloadMp3(String ea, String sourceUser, String path, String securityGroup) {
    log.info( "downloadMp3:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup);
    try {
      if (path.startsWith("G_")) {
        String sourceType = "amr";
        String tempPath = path.replace("mp3", "amr");
        byte[] sourceData;
        try {
          sourceData = gFileStorageProxy.downloadFile(sourceUser, tempPath, securityGroup);
        } catch (Exception e) {
          tempPath = path.replace("mp3", "opus");
          sourceData = gFileStorageProxy.downloadFile(sourceUser, tempPath, securityGroup);
          sourceType = "opus";
        }
        return nFileStorageProxy.convertFileToMp3(sourceData, sourceType, ea);

      } else if (path.startsWith("A_") || path.startsWith("TA_")) {
        return aFileStorageProxy.downloadMp3(ea, Integer.parseInt(sourceUser.substring(
          sourceUser.indexOf(".") + 1)), path, null, securityGroup);
      } else {
        return nFileStorageProxy.downloadMp3(ea, sourceUser, path, securityGroup);
      }
    } catch (Exception e) {
      if (e instanceof FsiBusinessException) {
        FsiBusinessException fsiException = (FsiBusinessException) e;
        if (fsiException.getMessage().contains("10007")) {
          log.warn( "downloadMp3:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup, e);
        } else {
          log.error( "downloadMp3:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup, e);
        }
      } else {
        log.error( "downloadMp3:ea:{},sourceUser:{},path:{},securityGroup:{}", ea, sourceUser, path, securityGroup, e);
      }
      return null;
    }
  }


  public byte[] downloadTempFile(String ea, String sourceUser, String tempFileName, String filetype) {
    log.info( "downloadTempFile:ea:{},sourceUser:{},path:{}", ea, sourceUser, tempFileName);
    try {
      if (tempFileName.startsWith("TG_")) {
        return gFileStorageProxy.downloadTempFile(sourceUser, tempFileName);
      } else if (tempFileName.startsWith("TA_")) {
        return aFileStorageProxy.downloadTempFile(ea, Integer.parseInt(sourceUser.substring(
          sourceUser.indexOf(".") + 1)), tempFileName, null, null);
      } else {
        return nFileStorageProxy.downloadTempFile(ea, sourceUser, tempFileName, filetype);
      }
    } catch (Exception e) {
      log.error( "downloadTempFile:ea:{},sourceUser:{},tempFileName:{}", ea, sourceUser, tempFileName, e);
      return null;
    }
  }

  public String createThumbnail(String ea,
                                String sourceUser,
                                String nPath,
                                String securityGroup,
                                List<String> fileAccessPermissions,
                                int toHeight,
                                int toWidth) {
    log.info( "createThumbnail:ea:{},sourceUser:{},path:{},securityGroup:{},List<String>:{},toHeight:{},toWidth:{}", ea, sourceUser, nPath, securityGroup, fileAccessPermissions, toHeight, toWidth);
    try {
      return nFileStorageProxy.getThumbnail(ea, sourceUser, nPath, securityGroup, fileAccessPermissions, toHeight, toWidth);
    } catch (Exception e) {
      log.error( "createThumbnail:path:{},securityGroup:{},fileAccessPermissions:{},toHeight:{},toWidth:{}", nPath, securityGroup, fileAccessPermissions, toHeight, toWidth, e);
      return null;
    }
  }

  public String addThumbnail(String ea,
                             String sourceUser,
                             String nPath,
                             String securityGroup,
                             List<String> fileAccessPermissions,
                             int toHeight,
                             int toWidth) {
    log.info( "addThumbnail:ea:{},sourceUser:{},path:{},securityGroup:{},List<String>:{},toHeight:{},toWidth:{}", ea, sourceUser, nPath, securityGroup, fileAccessPermissions, toHeight, toWidth);
    try {
      return nFileStorageProxy.addThumbnail(ea, sourceUser, nPath, securityGroup, fileAccessPermissions, toHeight, toWidth);
    } catch (Exception e) {
      log.error( "addThumbnail:path:{},securityGroup:{},fileAccessPermissions:{},toHeight:{},toWidth:{}", nPath, securityGroup, fileAccessPermissions, toHeight, toWidth, e);
      return null;
    }
  }

  public byte[] batchDownloadFile(String ea, String sourceUser, String documents, String securityGroup) {
    log.info( "batchDownloadFile:ea:{},sourceUser:{},documents:{}", ea, sourceUser, documents);
    try {
      return nFileStorageProxy.batchDownloadFile(ea, sourceUser, documents, securityGroup);
    } catch (Exception e) {
      log.error( "batchDownloadFile:ea:{},sourceUser:{},docuemnts:{}", ea, sourceUser, documents, e);
      return null;
    }
  }

  public SameCodeResult findFileWithSameCode(String ea, String sourceUser, String code, long size, String business) {
    log.info( "findFileWithSameCode:ea:{},sourceUser:{},code:{},size:{},business:{}", ea, sourceUser, code, size, business);
    try {
      NFindFileWithSameCode.Result result = nFileStorageProxy.findFileWithSameCode(ea, sourceUser, code, size, business);
      return new SameCodeResult(result.isExists(), result.getSize(), result.getTempPath());
    } catch (Exception e) {
      log.error( "findFileWithSameCode:ea:{},sourceUser:{},code:{},size:{},business:{}", ea, sourceUser, code, size, business);
      return new SameCodeResult(false, 0, null);
    }
  }

  public GetPreviewInfo.ResultData getPreviewInfo(String ea, String sourceUser, String docPath, String securityGroup) {
    log.info( "getPreviewInfo:ea:{},sourceUser:{},docPath:{},securityGroup:{}", ea, sourceUser, docPath, securityGroup);
    try {
      return previewProxy.getPreviewInfo(ea, sourceUser, docPath, securityGroup);
    } catch (Exception e) {
      log.error( "getPreviewInfo:ea:{},sourceUser:{},docPath:{},securityGroup:{}", ea, sourceUser, docPath, securityGroup);
      return null;
    }
  }

  public GetDocumentPageData.ResultData getDocumentPage(String ea,
                                                        String employeeAccount,
                                                        String docPath,
                                                        String securityGroup,
                                                        int pageIndex,
                                                        int expectedWidth,
                                                        int maxContentLength) {
    log.info( "getDocumentPage:ea:{},sourceUser:{},docPath:{},securityGroup:{},pageIndex:{},expectedWidth:{},maxContentLength:{}", ea, employeeAccount, docPath, securityGroup, pageIndex, expectedWidth, maxContentLength);
    try {
      return previewProxy.getDocumentPage(ea, employeeAccount, docPath, securityGroup, pageIndex, expectedWidth, maxContentLength);
    } catch (Exception e) {
      log.error( "getDocumentPage:ea:{},sourceUser:{},docPath:{},securityGroup:{},pageIndex:{},expectedWidth:{},maxContentLength:{}", ea, employeeAccount, docPath, securityGroup, pageIndex, expectedWidth, maxContentLength, e);
      return null;
    }
  }

  public String nTempFileUpload(String ea, String sourceUser, byte[] data, String business, boolean needThumbnail,String[] water, String ext,boolean needCdn) {
    log.info( "nTempFileUpload:ea:{},sourceUser:{},dataSize:{}", ea, sourceUser, data.length);
    try {
      return nFileStorageProxy.tempFileUpload(ea, sourceUser, data, business, needThumbnail, water, ext,needCdn);
    } catch (Exception e) {
      if(e.getMessage().contains("s312070021")){
        log.warn( "nTempFileUpload,invalid ext:{}", ea, sourceUser, e);
        throw new FSCRemoteException(e, ExceptionConstant.INVALID_EXTENSION);
      }
      log.error( "nTempFileUpload,ea:{},sourceUser:{}", ea, sourceUser, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  public String gTempFileUpload(String sourceUser, byte[] data) {
    log.info( "gTempFileUpload:sourceUser:{},dataSize:{}", sourceUser, data.length);
    try {
      return gFileStorageProxy.uploadTempFile(sourceUser, data);
    } catch (Exception e) {
      log.error( "gTempFileUpload,sourceUser:{}", sourceUser, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  public String nUploadDirect(String ea, String sourceUser, String npath,byte[] data, String business,String ext, boolean needCdn) {
    try {
      return nFileStorageProxy.uploadDirect(ea,sourceUser,npath, data,business,ext,needCdn);
    } catch (Exception e) {
      log.error( "nUploadDirect,ea:{},sourceUser:{},npath:{},business:{},ext:{},needCdn:{}", ea, sourceUser, npath, business, ext, needCdn, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }


  public String saveFileFromTempFile(String ea,
                                     String sourceUser,
                                     String path,
                                     String ext,
                                     List<String> permissions,
                                     String securityGroup,
                                     boolean isFree) {
    log.info( "saveFileFromTempFile:ea:{},sourceUser:{},path:{},ext:{},permission:{},securityGroup:{}," +
      "isFree:{}", ea, sourceUser, path, ext, permissions, securityGroup, isFree);
    try {
      if (path.startsWith("TG_")) {
        return gFileStorageProxy.saveFileFromTempFile(sourceUser, path, permissions, securityGroup);
      } else {
        return nFileStorageProxy.saveFileFromTempFile(ea, sourceUser, path, ext, securityGroup, isFree);
      }
    } catch (Exception e) {
      log.error(
        "saveFileFromTempFile:ea:{},sourceUser:{},path:{},ext:{},permissions:{},securityGroup:{},isFree:{}" +
          "", ea, sourceUser, path, ext, permissions, securityGroup, isFree, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  public NBatchDownloadFileAsyn.Result asyncBatchDownload(String ea,
                                                          String sourceUser,
                                                          String downloadSecurityGroup,
                                                          String downloadXml) {
    log.info( "asyncBatchDownload:ea:{},sourceUser:{},downloadSecurityGroup:{},downloadXml:{}", ea, sourceUser, downloadSecurityGroup, downloadXml);
    try {
      return nFileStorageProxy.asyncBatchDownload(ea, sourceUser, downloadSecurityGroup, downloadXml);
    } catch (Exception e) {
      log.error( "asyncBatchDownload:ea:{},sourceUser:{},downloadSecurityGroup:{},downloadXml:{}", ea, sourceUser, downloadSecurityGroup, downloadXml, e, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  public String aTempFileUpload(String ea, int employId, String business, byte[] data) {
    log.info( "aTempFileUpload:ea:{},employId:{},business:{},dataSize:{}", ea, employId, business, data.length);
    try {
      return aFileStorageProxy.tempFileUpload(ea, employId, business, data);
    } catch (Exception e) {
      log.error( "aTempFileUpload,ea:{},sourceUser:{}", e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  public ChunkFileUploadStartResult chunkFileUploadStart(String ea,
                                                         String sourceUser,
                                                         String code,
                                                         String extension,
                                                         int chunkCount,
                                                         int chunkSize,
                                                         int lastChunkSize,
                                                         String business,
                                                         boolean needThumbnail,
                                                         String hashRule) {
    try {
      return nFileStorageProxy.chunkFileUploadStart(ea, sourceUser, code, extension, chunkCount, chunkSize, lastChunkSize, business, needThumbnail, hashRule);
    } catch (Exception e) {
      log.error( "chunkFileUploadStart:ea:{},sourceUser:{},code:{},extension:{},chunkCount:{},chunkSize:{},lastChunkSize:{},business:{}", ea, sourceUser, code, extension, chunkCount, chunkSize, lastChunkSize, business, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  public ChunkFileUploadDataResult chunkFileUploadData(String ea, String path, int chunkIndex, byte[] data) {
    try {
      return nFileStorageProxy.chunkFileUploadData(ea, path, chunkIndex, data);
    } catch (Exception e) {
      log.error( "chunkFileUploadData:ea:{},path:{},chunkIndex:{},data:{}", ea, path, chunkIndex, data.length, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  public ChunkFileUploadCompleteResult chunkFileUploadComplete(String ea,
                                                               String sourceUser,
                                                               String path,
                                                               String business) {
    try {
      return nFileStorageProxy.chunkFileUploadComplete(ea, sourceUser, path, business);
    } catch (Exception e) {
      log.error( "chunkFileUploadComplete:ea:{},sourceUser:{},path:{},business:{}", ea, sourceUser, path, business, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }


  public CompatibleChunkDownloader downloadFileCompatibleChunk(String ea,
                                                               String sourceUser,
                                                               String path,
                                                               String securityGroup,
                                                               String filetype,
                                                               String ua) {
    boolean isCrossFile = isCrossPath(path);
    try {
      if (!isCrossFile && Strings.isNullOrEmpty(filetype)) {
        return nFileStorageProxy.chunkFilePullStart(ea, sourceUser, path, securityGroup, filetype, ua);
      } else {
        return new CompatibleChunkDownloader(false, downloadFile(ea, sourceUser, path, securityGroup, filetype, ua));
      }
    } catch (FsiClientException clientException) {
      // sandbox 类型企业，如果是400类型异常+沙盒企业+是企业文件+没有filetype
      boolean ignoreError = IgnoreErrorUtil.ignoreError(clientException.getMessage());
      boolean isSandBoxEnterprise = enterpriseService.isSandBoxEnterprise(ea);
      if (ignoreError && isSandBoxEnterprise && !isCrossFile && Strings.isNullOrEmpty(filetype)) {
        Optional<String> parentEAOption = enterpriseService.getParentEnterprise(ea);
        if (parentEAOption.isPresent()) {
          log.warn("sandbox:{}.{} from parent:{} try download {} file,securityGroup:{},fileType:{},UA:{}",ea,sourceUser,parentEAOption.get(), path, securityGroup, filetype, ua);
          try {
            return nFileStorageProxy.chunkFilePullStart(parentEAOption.get(), sourceUser, path, securityGroup, filetype, ua);
          }catch (FsiClientException e){
            ignoreError = IgnoreErrorUtil.ignoreError(e.getMessage());
          }
        }
      }

      // 如果是忽略的错误，不打印error日志
      if (ignoreError) {
        log.warn("downloadFileCompatibleChunk client error ea:{},sourceUser:{},path:{},securityGroup:{},fileType:{},ua:{},message:{}", ea, sourceUser, path, securityGroup,filetype,ua,clientException.getMessage());
      }else {
        log.error( "downloadFileCompatibleChunk server error,ea:{},sourceUser:{},path:{},securityGroup:{},fileType:{},ua:{}", ea, sourceUser, path, securityGroup,filetype,ua,clientException);
      }

    }catch (Exception e){
      log.error( "downloadFileCompatibleChunk server error,ea:{},sourceUser:{},path:{},securityGroup:{},fileType:{},ua:{}", ea, sourceUser, path, securityGroup,filetype,ua,e);
    }
    return new CompatibleChunkDownloader();
  }

  // 判断path是否是共享类型文件
  private boolean isCrossPath(String path) {
    return path.startsWith("G_") || path.startsWith("A_") || path.startsWith("TA_");
  }

  public void downloadChunkFileByStream(String ea,
                                        String sourceUser,
                                        String path,
                                        String securityGroup,
                                        Integer[] chunkIndexArr) {
    try {
      nFileStorageProxy.downloadChunkFileByStream(ea, sourceUser, path, securityGroup, chunkIndexArr);
    } catch (Exception e) {
      log.error( "downloadChunkFileByStream:ea:{},sourceUser:{},path:{},securityGroup:{},chunkIndexArr:{}", ea, sourceUser, path, securityGroup, chunkIndexArr, e);
    }

  }


  public CompatibleChunkDownloader downloadTempFileCompatibleChunk(String ea,
                                                                   String sourceUser,
                                                                   String path,
                                                                   String filetype,
                                                                   String ua) {
    CompatibleChunkDownloader compatibleChunkDownloader;
    try {
      if (!path.startsWith("G_") && !path.startsWith("A_") && !path.startsWith("TA_")) {
        compatibleChunkDownloader = nFileStorageProxy.chunkFilePullStart(ea, sourceUser, path, null, filetype, ua);
      } else {
        compatibleChunkDownloader = new CompatibleChunkDownloader(false, downloadTempFile(ea, sourceUser, path, filetype));
      }
    } catch (Exception e) {
      log.error( "downloadTempFileCompatibleChunk:ea:{},sourceUser:{},path:{}", ea, sourceUser, path, e);
      compatibleChunkDownloader = new CompatibleChunkDownloader();
    }
    return compatibleChunkDownloader;
  }


  public String avatarFileUpload(String ea,
                                 String sourceUser,
                                 byte[] data,
                                 String ext,
                                 String business,
                                 boolean rpcMDS) {
    log.info( "avatarFileUpload:ea:{},sourceUser:{},dataSize:{},ext:{},business:{},rpcMDS:{}", ea, sourceUser, data.length, ext, business, rpcMDS);
    try {
      List<String> userSplitList = Splitter.on('.').splitToList(sourceUser);
      int employeeID = Integer.parseInt(userSplitList.get(userSplitList.size() - 1));

      UploadFileDirect.Result result = avatarFileStorageProxy.uploadFileDirect(ea, employeeID, data, ext, business);

      String path = Splitter.on('.').splitToList(result.getPath()).get(0);
      redisService.set(path, "1");

      if (rpcMDS) {
        mdsProxy.uploadProfileAvatar(ea, employeeID, path);
      }
      return path;
    } catch (Exception e) {
      log.error( "avatarFileUpload:ea:{},sourceUser:{},dataSize:{},ext:{},business:{},rpcMDS:{}", ea, sourceUser, data.length, ext, business, rpcMDS, e);
      throw new FSCRemoteException(e, ExceptionConstant.FSC_FILE_CODE);
    }
  }

  public boolean checkNeeRealTimeThumbnail(String ea, String nPath) {
    return nFileStorageProxy.checkNeedRealTimeThumbnail(ea, nPath);
  }

  public byte[] getThumbnailData(String ea, String sourceUser, String nPath) {
    return nFileStorageProxy.getThumbnailData(ea, sourceUser, nPath);
  }

  public RangeDownloadResult downloadFileByRange(String ea,
                                                 String path,
                                                 String sourceUser,
                                                 String securityGroup,
                                                 int byteStartIndex,
                                                 int byteEndIndex) {
    if (path.startsWith("G_")||path.startsWith("TG_")){
      // G文件在当前并不真实存在,实际上是需要通过A文件下载,即G文件是A文件的别名
      String gPath = convertPath(path);
      return aFileStorageProxy.downloadFileByRange(ea,gPath, sourceUser, securityGroup, byteStartIndex, byteEndIndex);
    }
    if (path.startsWith("A_") || path.startsWith("TA_")) {
      return aFileStorageProxy.downloadFileByRange(ea, path, sourceUser, securityGroup, byteStartIndex, byteEndIndex);
    } else {
      return nFileStorageProxy.downloadFileByRange(ea, path, sourceUser, securityGroup, byteStartIndex, byteEndIndex);
    }
  }

  private String convertPath(String path) {
    if (Strings.isNullOrEmpty(path)) {
      return null;
    }
    if (path.indexOf('G') != -1) {
      return path.replace('G', 'A');
    }
    return path;
  }

  public byte[] downloadFirstChunkFile(String ea, String sourceUser, String nPath) {
    return nFileStorageProxy.downloadFirstChunkFile(ea, sourceUser, nPath);
  }


  public byte[] convertHeicToJpg(String ea, byte[] data) {
    return nFileStorageProxy.convertHeicToJpg(ea, data);
  }
}