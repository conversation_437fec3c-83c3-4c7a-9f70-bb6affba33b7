package com.facishare.fsc.core.model.vo;

import lombok.Builder;
import lombok.Data;

/**
 * Created by zhaifj on 2017/4/28.
 */
@Data
@Builder
public class FileDownloadQueryVo {

  private String ea;
  private String sourceUser;
  private String path;
  private String securityGroup;
  private String filetype;
  private String ua;
  private int byteStartIndex;
  private int byteEndIndex;
  private boolean preview;
  private boolean parse;
  public String getFiletype() {
    return "webp".equalsIgnoreCase(filetype) ? "webp" : null;
  }
}
