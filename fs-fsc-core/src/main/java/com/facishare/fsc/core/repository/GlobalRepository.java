package com.facishare.fsc.core.repository;

/**
 * Created by <PERSON> on 16/5/16.
 */
public interface GlobalRepository {
    String createImageCode(String userTag, String clientId, String epxId);

    void prSignUp(String mobile, String name, String company, String post);

    void partnerRegister(String company, String name, String post, String mobile, String product, String intention);

    boolean addIndustryContact(String contactName, String contactPhone);
}
