package com.facishare.fsc.core.model;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 16/9/6.
 */
public class CompatibleChunkDownloader {

  private List<Chunk> ChunksInfo;
  private boolean IsChunk;
  private byte[] Data;
  private boolean IsComplete;
  private boolean isOriginImage;
  private long fileSize;
  private Integer[] chunkIndexs;

  public long getFileSize() {
    return fileSize;
  }

  public void setFileSize(long fileSize) {
    this.fileSize = fileSize;
  }

  public Integer[] getChunkIndexs() {
    return chunkIndexs;
  }

  public void setChunkIndexs(Integer[] chunkIndexs) {
    this.chunkIndexs = chunkIndexs;
  }

  public boolean isOriginImage() {
    return isOriginImage;
  }

  public void setOriginImage(boolean originImage) {
    isOriginImage = originImage;
  }

  public CompatibleChunkDownloader() {
  }

  public CompatibleChunkDownloader(boolean isChunk, boolean isComplete,
      List<com.facishare.fsi.proxy.model.warehouse.n.fileupload.Chunk> chunkedList, byte[] data) {
    if (chunkedList != null && chunkedList.size() > 0) {
      ChunksInfo = Lists.newArrayList();
      chunkIndexs = new Integer[chunkedList.size()];
      for (int i = 0; i < chunkedList.size(); i++) {
        com.facishare.fsi.proxy.model.warehouse.n.fileupload.Chunk completedChunk = chunkedList
            .get(i);
        ChunksInfo.add(new Chunk(completedChunk.getIndex(), completedChunk.getSize()));
        fileSize += completedChunk.getSize();
        chunkIndexs[i] = completedChunk.getIndex();
      }
    } else {
      chunkIndexs = new Integer[0];
      fileSize = data.length;
    }
    IsChunk = isChunk;
    Data = data;
    IsComplete = isComplete;
  }


  public CompatibleChunkDownloader(boolean isChunk, byte[] data) {
    this.IsChunk = isChunk;
    this.Data = data;
  }


  public boolean isChunk() {
    return IsChunk;
  }


  public byte[] getData() {
    return Data;
  }

  public void setData(byte[] data) {
    Data = data;
  }

  public boolean isComplete() {
    return IsComplete;
  }

}
