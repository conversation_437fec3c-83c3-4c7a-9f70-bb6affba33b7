package com.facishare.fsc.core.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.common.http.HttpClient;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.github.autoconf.ConfigFactory;
import java.util.Optional;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2022/10/12  16:11
 */
@Component
public class EnterpriseService {
  @Autowired
  private EIEAConverter eieaConverter;
  private String sandboxSuffix = "_sandbox";
  private String url;

  @PostConstruct
  private void init() {
    ConfigFactory.getConfig("fs-stone-file-config", config -> {
      url = config.get("getSandboxConfigUrl");
    });
  }

  public Optional<String> getParentEnterprise(String ea) {
    int ei = eieaConverter.enterpriseAccountToId(ea);
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("enterpriseId", String.valueOf(ei));
    Request request = new Request.Builder().url(url).post(RequestBody.create(MediaType.parse("application/json"), jsonObject.toJSONString())).build();
    String parentEA = (String) HttpClient.defaultClient().getClient().syncExecute(request, new SyncCallback() {
      @Override
      public String response(Response response) throws Exception {
        if (response.isSuccessful()&&response.body()!=null){
          String body = response.body().string();
          JSONObject jsonObject = JSON.parseObject(body);
          Integer parentEi = jsonObject.getJSONObject("config").getInteger("enterpriseId");
          return eieaConverter.enterpriseIdToAccount(parentEi);
        }
        return null;
      }
    });
    return Optional.ofNullable(parentEA);
  }

  public boolean isSandBoxEnterprise(String ea){
    return ea.endsWith(sandboxSuffix);
  }
}
