package com.facishare.fsc.core.model;

import org.bson.BasicBSONDecoder;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON> on 16/5/9.
 */
public interface GetDocumentPageData {
    class Arg {
        public String FSP;
        public String EnterpriseAccount;
        public String EnterpriseCreateYearMonth;
        public String DocPath;
        public String DownloadSecurityGroup;
        public String DownloadUser;
        public long LengthLimit;
        public int PageIndex;
        public int ExpectedWith;

        public Arg(String enterpriseAccount, String FSP, String enterpriseCreateYearMonth, String docPath,
                   String downloadSecurityGroup, String downloadUser, long lengthLimit, int pageIndex,
                   int expectedWith) {
            this.FSP = FSP;
            EnterpriseAccount = enterpriseAccount;
            EnterpriseCreateYearMonth = enterpriseCreateYearMonth;
            DocPath = docPath;
            DownloadSecurityGroup = downloadSecurityGroup;
            DownloadUser = downloadUser;
            LengthLimit = lengthLimit;
            PageIndex = pageIndex;
            ExpectedWith = expectedWith;
        }
    }

    class Result extends JsonResponse {
        public ResultData Data;
        public String Description;

        public static Result getInstance(byte[] data) {
            Map<String, Object> map = new BasicBSONDecoder().readObject((byte[]) data).toMap();
            Result result = new Result();
            result.Code = (String) map.get("Code");
            result.Status = map.get("Status") + "";
            result.Description = (String) map.get("Description");
            if (map.get("Data") != null) {
                Map<String, Object> child = (HashMap) map.get("Data");
                ResultData resultData = new ResultData();
                resultData.buffer = (byte[]) child.get("Buffer");
                resultData.ContentType = (String) child.get("ContentType");
                result.Data = resultData;
            }
            return result;

        }
    }

    class ResultData {
        public byte[] buffer;
        public String ContentType;
    }
}
