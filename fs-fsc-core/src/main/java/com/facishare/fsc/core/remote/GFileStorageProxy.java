package com.facishare.fsc.core.remote;

import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload;
import com.facishare.fsi.proxy.model.warehouse.g.GFileUpload;
import com.facishare.fsi.proxy.model.warehouse.g.GSaveFileFromTempFile;
import com.facishare.fsi.proxy.model.warehouse.g.GTempFileDownload;
import com.facishare.fsi.proxy.model.warehouse.g.GTempFileUpload;
import com.facishare.fsi.proxy.service.GFileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON> on 16/5/4.
 */
@Service("gFileStorageProxy")
public class GFileStorageProxy {
    @Autowired
    private GFileStorageService gFileStorageService;

    public byte[] downloadFile(String sourceUser,String gPath, String securityGroup) {
        GFileDownload.Arg arg = new GFileDownload.Arg();
        arg.downloadUser = sourceUser;
        arg.downloadSecurityGroup = securityGroup;
        arg.gPath = gPath;
        return gFileStorageService.downloadFile(arg).data;
    }

    public String saveFileFromTempFile(String sourceUser,String tGPath,List<String> permissions, String securityGroup) {
        GSaveFileFromTempFile.Arg arg = new GSaveFileFromTempFile.Arg();
        arg.sourceUser = sourceUser;
        arg.fileAccessPermissions = permissions;
        arg.fileSecurityGroup = securityGroup;
        arg.tGPath = tGPath;
        return gFileStorageService.saveFileFromTempFile(arg).gPath;
    }

    public String uploadTempFile(String sourceUser,byte[] data) {
        GTempFileUpload.Arg arg = new GTempFileUpload.Arg();
        arg.data = data;
        arg.sourceUser =sourceUser;
        return gFileStorageService.uploadTempFile(arg).tGPath;
    }

    public String uploadFileDirect(String sourceUser,byte[] buffer, String ext) {
        GFileUpload.Arg arg = new GFileUpload.Arg();
        arg.fileExt = ext;
        arg.data = buffer;
        arg.sourceUser = sourceUser;
        return gFileStorageService.uploadFile(arg).gPath;
    }

    public byte[] downloadMp3(String sourceUser,String gPath, String securityGroup) {
        GFileDownload.Arg arg = new GFileDownload.Arg();
        arg.gPath = gPath;
        arg.downloadSecurityGroup = securityGroup;
        arg.downloadUser = sourceUser;
        return gFileStorageService.downloadMp3(arg).data;
    }

    public byte[] downloadTempFile(String sourceUser,String tGFile) {
        GTempFileDownload.Arg arg = new GTempFileDownload.Arg();
        arg.sourceUser = sourceUser;
        arg.tGPath = tGFile;
        return gFileStorageService.tempFileDownload(arg).data;
    }
}
