package com.facishare.fsc.core.fascade.impl;

import com.facishare.contract.core.facade.ContractAction;
import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.fascade.ContractFacade;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.core.Response;
import java.util.Map;

/**
 * Created by <PERSON> on 16/5/24.
 */
@Service
@Slf4j
public class ContractFacadeImpl implements ContractFacade {
    @Autowired
    private ContractAction contractAction;
    @Override
    public Response.ResponseBuilder uploadFile(String mobile,String deviceId,int employeeId,int enterpriseId
            ,String employeeAccount, String enterpriseAccount,byte[] data) {
        log.info( "mobile:{},deviceId:{},employeeId:{},enterpriseId:{},employeeAccount:{},enterpriseAccount;{}",mobile,deviceId,employeeId,enterpriseId,
                employeeAccount,enterpriseAccount);
        Map<String,Object> result= Maps.newHashMap();
        result.put("M1",0);
        result.put("M2","success");
        try{
            contractAction.saveContract(mobile,deviceId,employeeId,
                enterpriseId,employeeAccount,enterpriseAccount,data);
        }catch (Exception e){
            result.put("M1",1);
            result.put("M2","uploadFile error"+e.getMessage());
            log.error("uploadFile文件上传错误",e);
        }
        return Response.status(Response.Status.OK).entity(JsonUtils.toJson(result));
    }
}
