package com.facishare.fsc.core.remote;

import com.facishare.fsc.core.model.Avatar;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用文件路径到redis去检查，查看是否是头像
 * Created by lirui on 2016-10-26 12:45.
 */
@Service
@Slf4j
public class AvatarCheckService {

  @Autowired
  private RedisService redisService;
  public final Pattern AVATAR_PATH_PATTERN = Pattern.compile("[0-9a-f]{32}([0-9])");

  /**
   * 用于高清图替换
   * @param nPath
   * @param ea
   * @return
   */
  public Avatar detect(String nPath, String ea) {
    //N_201612_19_512d56d352d2410180aae07e26634ba2
    //S_201504_23_8e3fe50c3d4f466ea07c426b44063f91
    //201409_15_477f8b32-cc1b-4caa-857a-2b9f149985901.jpg

    //前两种可采用AVATAR_PATH_PATTERN正则匹配，
    //启用实时缩略后所有图都有32位的图，基于此图可生成各种缩略图


    String redisPathRule=nPath.replaceAll("-","");
    Matcher avatarMatcher = AVATAR_PATH_PATTERN.matcher(redisPathRule);
    if(!avatarMatcher.find()){return null;}

    String index=avatarMatcher.group(1);
    int endPos = nPath.indexOf(".");
    switch (endPos) {
      case -1:
        endPos = nPath.length()-1;
        break;
      default:
        endPos--;
        break;
    }
    String basePath=nPath.substring(0,endPos);

    log.debug("nPath:{},basePath:{}",nPath,basePath);
    if(index.equals("0")){
      return new Avatar(basePath, '0', true);
    }

    String redisKey=redisPathRule.substring(0,avatarMatcher.end()-1);
    log.debug("nPath:{},basePath:{},redisKey:{}",nPath,basePath,redisKey);
    if(index.equals("1") && redisService.exists(redisKey)){
      return new Avatar(basePath, '0', true);
    }

    return null;
  }
}
