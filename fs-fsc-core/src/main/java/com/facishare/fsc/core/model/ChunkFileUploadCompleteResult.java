package com.facishare.fsc.core.model;


import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 16/8/18.
 */
public class ChunkFileUploadCompleteResult {
    private Boolean IsSuccess;
    private List<Chunk> ChunkList;

    public Boolean getSuccess() {
        return IsSuccess;
    }

    public void setSuccess(Boolean success) {
        IsSuccess = success;
    }

    public List<Chunk> getChunkList() {
        return ChunkList;
    }

    public void setChunkList(List<Chunk> chunkList) {
        ChunkList = chunkList;
    }

    public ChunkFileUploadCompleteResult(Boolean isSuccess, List<com.facishare.fsi.proxy.model.warehouse.n.fileupload.Chunk> chunkList) {
        IsSuccess = isSuccess;
        if(chunkList !=null&& chunkList.size()>0){
            ChunkList= Lists.newArrayList();
            for (com.facishare.fsi.proxy.model.warehouse.n.fileupload.Chunk completedChunk: chunkList) {
                ChunkList.add(new Chunk(completedChunk.getIndex(),completedChunk.getSize()));
            }
        }
    }
}


