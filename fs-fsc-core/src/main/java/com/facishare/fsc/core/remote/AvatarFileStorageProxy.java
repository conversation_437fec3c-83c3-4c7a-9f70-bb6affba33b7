package com.facishare.fsc.core.remote;

import com.facishare.fsi.proxy.model.warehouse.micro.DownloadFile;
import com.facishare.fsi.proxy.model.warehouse.micro.UploadFileDirect;
import com.facishare.fsi.proxy.service.AvatarFileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON> on 16/5/4.
 */
@Service("avatarFileStorageProxy")
public class AvatarFileStorageProxy {

    @Autowired
    private AvatarFileStorageService avatarFileStorageService;
    public UploadFileDirect.Result uploadFileDirect(String ea, int employId, byte[] data, String ext,String business) {
        UploadFileDirect.Arg arg=new UploadFileDirect.Arg();
        arg.setData(data);
        arg.setBusiness(business);
        arg.setUser(new com.facishare.fsi.proxy.model.warehouse.micro.User(employId,ea));
        arg.setFileExt(ext);
        arg.setEa(ea);
        arg.setNeedThumbnail(true);
        return  avatarFileStorageService.uploadFileDirect(arg);
    }

    public byte[] downloadFile(String ea, int employId, String path) {
        DownloadFile.Arg arg=new DownloadFile.Arg();
        arg.setBusiness("FSC");
        arg.setUser(new com.facishare.fsi.proxy.model.warehouse.micro.User(employId,ea));
        arg.setPath(path);
        return avatarFileStorageService.downloadFile(arg).getData();
    }
}
