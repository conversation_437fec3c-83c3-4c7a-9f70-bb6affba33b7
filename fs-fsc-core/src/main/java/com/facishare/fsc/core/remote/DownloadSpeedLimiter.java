package com.facishare.fsc.core.remote;

import com.fxiaoke.common.TaskScheduler;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.io.Files;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.UnaryOperator;

/**
 * 针对下载做限速，避免突发流量导致主站业务异常
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@SuppressWarnings("UnstableApiUsage")
public class DownloadSpeedLimiter {
  private int maxBytesByEnterprisePerSecond;
  private int maxBytesByUserPerSecond;
  private int maxBytesByPathPerSecond;
  private int largeFileCacheSize;
  private int smallFileSize;

  private Map<String, Integer> vipEaMaxBytes = Maps.newHashMap();
  private Map<String, RateLimiter> eaLimiters = Maps.newConcurrentMap();
  private Map<String, RateLimiter> userLimiters = Maps.newConcurrentMap();
  private Map<String, RateLimiter> pathLimiters = Maps.newConcurrentMap();

  private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("fsc");

  private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
  private final AtomicLong counter = new AtomicLong();
  private Path cacheRoot;

  private boolean currentCloudRateLimit;

  @PostConstruct
  void init() {
    // 查找环境变量确定fsc-cache目录
    String[] more = new String[] {"CATALINA_HOME", "user.home", "java.io.tmpdir"};
    // 获取环境变量或者系统属性
    UnaryOperator<String> mapper = s -> Objects.toString(System.getenv(s), System.getProperty(s));
    Optional<Path> found = Arrays.stream(more).map(mapper).filter(Objects::nonNull).map(s -> {
      // 兼容windows处理; /C:/xxxx
      if (s.charAt(2) == ':' && s.charAt(0) == '/') {
        return s.substring(1);
      } else {
        return s;
      }
    }).map(Paths::get).map(this::tryCreateDirectory).filter(Objects::nonNull).findFirst();

    cacheRoot = found.orElseGet(() -> tryCreateDirectory(Paths.get("/opt/tomcat")));

    ConfigFactory.getConfig("fs-fsc-cgi-config", config -> {
      currentCloudRateLimit = config.getBool("currentCloudRateLimit", true);
      // 低于1M的文件不限速
      smallFileSize = config.getInt("smallFileSize", 1024 * 1024);
      // 高于5M的文件写入本地缓存，避免长时间占用内存导致OOM
      largeFileCacheSize = config.getInt("largeFileCacheSize", 1024 * 1024 * 5);
      // 每个企业限速20M
      maxBytesByEnterprisePerSecond = config.getInt("maxBytesByEnterprisePerSecond", 1024 * 1024 * 20);
      // 每个人限速5M
      maxBytesByUserPerSecond = config.getInt("maxBytesByUserPerSecond", 1024 * 1024 * 5);
      // 每个文件限速1M
      maxBytesByPathPerSecond = config.getInt("maxBytesByPathPerSecond", 1024 * 1024);
      // 有些vip企业，默认的针对单个企业的限制，对于他们来讲太小，可以指定单独的配额
      String parameters = config.get("vipEnterpriseMaxBytesPerSecond");
      if (parameters != null && !parameters.isEmpty()) {
        Map<String, Integer> settings = Maps.newHashMap();
        Splitter.on('&').withKeyValueSeparator('=').split(parameters).forEach((ea, bytes) -> settings.put(ea, Integer.parseInt(bytes)));
        if (!settings.isEmpty()) {
          vipEaMaxBytes = settings;
        }
      }
    });

    // 每分钟重置限速器，避免长时间占据内存，以及新配置不生效问题
    TaskScheduler.system().scheduleAtFixedRate(() -> {
      // 保存旧的限速器
      Map<String, RateLimiter> limiter01 = eaLimiters;
      Map<String, RateLimiter> limiter02 = userLimiters;
      Map<String, RateLimiter> limiter03 = pathLimiters;
      // 创建新的限速器
      eaLimiters = Maps.newConcurrentMap();
      userLimiters = Maps.newConcurrentMap();
      pathLimiters = Maps.newConcurrentMap();
      // 清空旧的限速器
      Arrays.asList(limiter01, limiter02, limiter03).forEach(Map::clear);
    }, 30, 30, TimeUnit.SECONDS);

    // 每分钟检查一次，清理过期的文件
    scheduler.scheduleAtFixedRate(() -> deleteExpiredFiles(cacheRoot.toFile()), 1, 1, TimeUnit.MINUTES);

    // 注册退出回调功能，避免jvm退出时，部分文件没有被清理
    Runtime.getRuntime().addShutdownHook(new Thread(scheduler::shutdown));
  }


  private boolean noSpeedLimit(String ea){
    return !currentCloudRateLimit || !gray.isAllow("download-speed-limit", ea);
  }

  /**
   * 删除长时间未使用的文件
   *
   * @param file 文件或者目录
   * @return 删除的文件数量
   */
  private int deleteExpiredFiles(File file) {
    Consumer<File> deleteIfExists = f -> {
      try {
        log.info("delete file: {}", f);
        boolean deleted = java.nio.file.Files.deleteIfExists(f.toPath());
        if (!deleted) {
          log.warn("delete file failed, file: {}", f);
        }
      } catch (IOException e) {
        log.error("delete file failed, file: {}, ", f, e);
      }
    };
    if (file.isFile() && file.lastModified() < System.currentTimeMillis() - TimeUnit.DAYS.toMillis(1)) {
      deleteIfExists.accept(file);
      return 1;
    }
    final AtomicInteger count = new AtomicInteger();
    if (file.isDirectory()) {
      File[] files = file.listFiles();
      Optional.ofNullable(files).ifPresent(fs -> {
        int num = Arrays.stream(fs).mapToInt(this::deleteExpiredFiles).sum();
        count.addAndGet(num);
      });
    }
    return count.get();
  }

  private Path tryCreateDirectory(Path basePath) {
    if (basePath != null) {
      try {
        Path path = basePath.resolve("logs/fsc-cache");
        Files.createParentDirs(path.resolve("_blank").toFile());
        log.info("will use {} as local file cache directory, ", path.toFile());
        return path;
      } catch (Exception e) {
        log.error("cannot create local file cache directory in {}, ", basePath, e);
      }
    }
    return null;
  }

  private RateLimiter createEaLimiter(String ea) {
    int maxBytes = vipEaMaxBytes.getOrDefault(ea, maxBytesByEnterprisePerSecond);
    return RateLimiter.create(maxBytes);
  }

  public void copyWithSpeedLimit(byte[] from, OutputStream to, String ea, String user, String path) throws IOException {

    // 不限速
    if (noSpeedLimit(ea)) {
      to.write(from);
      return;
    }

    List<RateLimiter> limiters = new ArrayList<>();
    limiters.add(eaLimiters.computeIfAbsent(ea, this::createEaLimiter));
    if (user != null && !user.isEmpty()) {
      limiters.add(userLimiters.computeIfAbsent(ea + user, k -> RateLimiter.create(maxBytesByUserPerSecond)));
    }
    limiters.add(pathLimiters.computeIfAbsent(path, k -> RateLimiter.create(maxBytesByPathPerSecond)));

    long startTime = System.currentTimeMillis();
    int len = from.length;
    int off = 0;
    while (off < len) {
      // 每次最多复制1024字节，避免一次要求较大的限速额度
      int bytes = Math.min(len - off, 1024);
      to.write(from, off, bytes);
      off += bytes;
      limiters.forEach(permits -> permits.acquire(bytes));
    }
    // 打印限速日志
    DecimalFormat decimalFormat = new DecimalFormat("#,###.00");
    long cost = System.currentTimeMillis() - startTime;
    if (cost > 1000 || len > 1024 * 1024) {
      log.info("thunk send file {}/{}, within {} ms, network speed {}KB/s", ea, path, cost, decimalFormat.format(len * 1D / cost));
    }
  }

  public InputStream wrapWithSpeedLimit(InputStream in, String ea, String user, String path) {
    return wrapWithSpeedLimit(in, ea, user, path, null);
  }

  public InputStream wrapWithSpeedLimit(InputStream in, String ea, String user, String path, Runnable endOfStreamHook) {
    if (noSpeedLimit(ea)) {
      return in;
    }
    List<RateLimiter> limiters = new ArrayList<>();
    limiters.add(eaLimiters.computeIfAbsent(ea, this::createEaLimiter));
    if (user != null && !user.isEmpty()) {
      limiters.add(userLimiters.computeIfAbsent(ea + user, k -> RateLimiter.create(maxBytesByUserPerSecond)));
    }
    limiters.add(pathLimiters.computeIfAbsent(path, k -> RateLimiter.create(maxBytesByPathPerSecond)));
    return new InputStreamLimiter(ea, path, in, limiters, endOfStreamHook);
  }

  public InputStream wrapWithSpeedLimit(byte @NonNull [] buf, String ea, String user, String path) {

    // 不限速
    if (noSpeedLimit(ea)) {
      return new ByteArrayInputStream(buf);
    }

    // 小文件不限流
    if (buf.length < smallFileSize) {
      return new ByteArrayInputStream(buf);
    }
    InputStream in = new ByteArrayInputStream(buf);
    // 针对大文件，先写入本地文件，再读取本地文件，避免内存占用过大
    Runnable endOfStreamHook = null;
    if (buf.length > largeFileCacheSize && gray.isAllow("large-file-with-local-cache", ea)) {
      // 增加计数器，避免文件名重复和多线程写入问题
      String name = counter.incrementAndGet() + "__" + replaceInvalidChars(ea) + "__" + replaceInvalidChars(path);
      File file = cacheRoot.resolve(name).toFile();
      try {
        Files.write(buf, file);
        DecimalFormat decimalFormat = new DecimalFormat("#,###.00");
        log.info("write file {}, {} KB", file, decimalFormat.format(buf.length / 1000D));
        in = Files.asByteSource(file).openBufferedStream();
        // 异步启动删除任务，避免阻塞当前请求
        endOfStreamHook = () -> scheduler.schedule(() -> {
          try {
            boolean deleted = java.nio.file.Files.deleteIfExists(file.toPath());
            log.info("delete file {}, {}", file, deleted ? "success" : "failed");
          } catch (IOException e) {
            log.error("delete file error, file: {}, ", file, e);
          }
        }, 100, TimeUnit.MILLISECONDS);
      } catch (Exception e) {
        log.error("write file error", e);
      }
    }
    return wrapWithSpeedLimit(in, ea, user, path, endOfStreamHook);
  }

  private String replaceInvalidChars(String s) {
    return CharMatcher.anyOf(" \t\n\r\\/#~").replaceFrom(s, '_').toLowerCase();
  }
}
