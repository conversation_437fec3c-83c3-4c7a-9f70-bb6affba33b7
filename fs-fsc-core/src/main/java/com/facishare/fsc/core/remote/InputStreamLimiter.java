package com.facishare.fsc.core.remote;

import com.fxiaoke.common.SizeFormatter;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;

@SuppressWarnings("UnstableApiUsage")
@Slf4j
public class InputStreamLimiter extends InputStream {
  private final String ea;
  private final String path;
  private final InputStream origin;
  private final List<RateLimiter> limiters;
  private final Runnable endOfStreamHook;
  private final long startTime;
  private int bytesCount;

  public InputStreamLimiter(String ea, String path, InputStream inputStream, List<RateLimiter> limiters, Runnable endOfStreamHook) {
    this.ea = ea;
    this.path = path;
    this.origin = inputStream;
    this.limiters = limiters;
    this.endOfStreamHook = endOfStreamHook;
    this.startTime = System.currentTimeMillis();
  }

  @Override
  public int read() throws IOException {
    int chr = origin.read();
    limiters.forEach(RateLimiter::acquire);
    bytesCount++;
    return chr;
  }

  @Override
  public int read(byte @NonNull [] buf, int off, int len) throws IOException {
    int bytesRead = 0;
    while (bytesRead < len) {
      int remainingBytes = len - bytesRead;
      int bytesToRead = Math.min(remainingBytes, 1024);
      int bytesReadThisTime = origin.read(buf, off + bytesRead, bytesToRead);
      if (bytesReadThisTime == -1) {
        return bytesRead > 0 ? bytesRead : -1; // 如果没有读取到任何字节，返回 -1 表示结束
      }
      bytesRead += bytesReadThisTime;
      limiters.forEach(permits -> permits.acquire(bytesReadThisTime));
    }
    bytesCount += bytesRead;
    return bytesRead;
  }

  @Override
  public int read(byte @NonNull [] buf) throws IOException {
    return read(buf, 0, buf.length);
  }

  @Override
  public long skip(long n) throws IOException {
    return origin.skip(n);
  }

  @Override
  public int available() throws IOException {
    return origin.available();
  }

  @Override
  public void close() throws IOException {
    origin.close();
    long cost = System.currentTimeMillis() - startTime;
    if (cost > 1000 || bytesCount > 1024 * 1024) {
      String speed = SizeFormatter.speed(bytesCount, cost);
      log.info("send file {}/{}, within {}, network speed {}KB/s", ea, path, Duration.ofMillis(cost), speed);
    }
    if (endOfStreamHook != null) {
      endOfStreamHook.run();
    }
  }

  @Override
  public synchronized void mark(int readlimit) {
    origin.mark(readlimit);
  }

  @Override
  public synchronized void reset() throws IOException {
    origin.reset();
  }

  @Override
  public boolean markSupported() {
    return origin.markSupported();
  }

  public int getBytesCount() {
    return bytesCount;
  }
}