package com.facishare.fsc.core.repository.model;

import com.facishare.fsc.core.storage.model.FileToken;

/**
 * Created by <PERSON> on 16/5/13.
 */
public class FileMetaData {
    private String fileType;
    private String contentType;
    private byte[] data;
    private String name;
    private FileToken token;
    public FileMetaData(String name,String contentType,String fileType, byte[] data) {
        this.fileType = fileType;
        this.contentType=contentType;
        this.data = data;
        this.name=name;
    }
    public FileMetaData(String name,String fileType, byte[] data) {
        this.fileType = fileType;
        this.data = data;
        this.name=name;
    }
    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public FileToken getToken() {
        return token;
    }

    public void setToken(FileToken token) {
        this.token = token;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "FileMetaData{" +
                "fileType='" + fileType + '\'' +
                "contentType='" + contentType + '\'' +
                ", data=" + data.length +
                '}';
    }
}
