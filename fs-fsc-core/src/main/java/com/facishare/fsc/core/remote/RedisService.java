package com.facishare.fsc.core.remote;

import com.facishare.fsc.core.FSSwitch;
import com.github.jedis.support.JedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by zhaifj on 2017/1/17.
 */
@Service
@Slf4j
public class RedisService {
    @Resource(name = "avatarCache")
    private JedisCmd jedis;
    public String get(String key){
        try {
            return jedis.get(key);
        }catch (Exception ex){
            log.error("get redis failed ,key is {}",key,ex);
        }
        return null;
    }

    public void set(String key,String value){
        try {
            jedis.set(key,value);
        }catch (Exception ex){
            log.error("set redis failed ,key is {},value is {}",key,value,ex);
        }
    }

    public boolean exists(String key){
        try {
            return jedis.exists(key);
        }catch (Exception ex){
            log.error("exists redis failed ,key is {}",key,ex);
        }
        return false;
    }

    public void set(String key,String value,int expireTime){
        try {
            jedis.setex(key, expireTime, value);
        }catch (Exception ex){
            log.error("set redis failed ,key is {},value is {}",key,value,ex);
        }
    }

    public void setex(String key,String value){
        set(key,value, FSSwitch.AVATAR_REDIS_EXPIRETIME);
    }
}
