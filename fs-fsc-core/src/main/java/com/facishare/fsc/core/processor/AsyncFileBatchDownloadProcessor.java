package com.facishare.fsc.core.processor;

import static com.facishare.fsc.core.FSSwitch.COMET_FILE_DOWNLOAD_TIMEOUT;

import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.processor.model.CompressFileDownloadEvent;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.ws.rs.container.AsyncResponse;
import javax.ws.rs.container.CompletionCallback;
import javax.ws.rs.container.TimeoutHandler;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "asyncFileBatchDownloadProcessor")
public class AsyncFileBatchDownloadProcessor implements ApplicationListener<ContextRefreshedEvent> {

  private String configName;
  private String sectionNames;
  private AutoConfMQPushConsumer consumer;

  private static final Map<String, AsyncResponse> resultResponse = new ConcurrentHashMap<>();

  @PostConstruct
  public void init() {
    log.info("AsyncFileBatchDownloadProcessor init start");
    loadConfig();
    initializeConsumer();
    log.info("AsyncFileBatchDownloadProcessor init end");
  }

  private void loadConfig() {
    ConfigFactory.getConfig("fs-file-system-config", config -> {
      configName = config.get("cms.warehouseBatch.compress.mqConfigName");
      sectionNames = config.get("cms.warehouseBatch.compress.sectionNames");
    });
  }

  private void initializeConsumer() {
    consumer = new AutoConfMQPushConsumer(configName, sectionNames,
        (MessageListenerConcurrently) (msgs, context) -> {
          processMessage(msgs);
          return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
  }

  public void listenerBatchDownloadStatus(AsyncResponse response, final String key) {
    resultResponse.put(key, response);
    log.info("listenerBatchDownloadStatus:key:{},Time out is:{}", key, COMET_FILE_DOWNLOAD_TIMEOUT);
    DownloadResponseStatusHandler handler = new DownloadResponseStatusHandler(key);
    response.setTimeoutHandler(handler);
    response.setTimeout(COMET_FILE_DOWNLOAD_TIMEOUT, TimeUnit.SECONDS);
    response.register(handler);
  }

  static class DownloadResponseStatusHandler implements TimeoutHandler, CompletionCallback {
    private final String key;
    public DownloadResponseStatusHandler(String key) {
      this.key = key;
    }

    @Override
    public void onComplete(Throwable throwable) {
      resultResponse.remove(key);
      log.info("listenerBatchDownloadStatus:complete:{}", key);
    }

    @Override
    public void handleTimeout(AsyncResponse asyncResponse) {
      if (!resultResponse.containsKey(key)) {
        return;
      }
      log.info("listenerBatchDownloadStatus:Response timeout key:{}", key);
      Map<String, Object> map = Maps.newHashMap();
      map.put("status", 1);
      asyncResponse.resume(Response.ok().entity(JsonUtils.toJson(map)).encoding("UTF-8")
          .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON).build());
      resultResponse.remove(key);
    }
  }

  private void processMessage(List<MessageExt> messages) {
    for (MessageExt messageExt : messages) {
      CompressFileDownloadEvent task;
      try {
        task = CompressFileDownloadEvent.fromJson(new String(messageExt.getBody()));
        log.info("ProcessBatchDownload begin,{}", task);
        process(task);
        log.info("ProcessBatchDownload enn,{}", task);
      } catch (Exception e) {
        log.error("UnSerialize CompressFileDownloadEvent error!", e);
      }
    }
  }

  public void process(CompressFileDownloadEvent task) {
    if (resultResponse.containsKey(task.getKey())) {
      log.info("async batch package task result:{}", task);
      AsyncResponse response = resultResponse.get(task.getKey());
      Map<String, Object> result = Maps.newHashMap();
      if (task.getCode() == 0) {
        result.put("status", 0);
        result.put("token", Base64.getUrlEncoder().encodeToString(task.getDownloadURL().getBytes()));
        log.info("asyncBatchDownloadByDocument:result:{},task:{}", result, task);
        response.resume(Response.ok().entity(JsonUtils.toJson(result)).encoding("UTF-8")
            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON).build());
      } else {
        result.put("status", 3);
        result.put("message", task.getMessage());
        response.resume(Response.ok().entity(JsonUtils.toJson(result)).encoding("UTF-8")
            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON).build());
        log.error("asyncBatchDownloadByDocument:result:{},task:{}", result, task);
      }
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (consumer != null && event.getApplicationContext().getParent() == null) {
      consumer.start();
    }
  }

  @PreDestroy
  public void close() {
    consumer.close();
  }
}
