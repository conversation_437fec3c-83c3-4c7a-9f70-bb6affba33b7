package com.facishare.fsc.core.storage.model;

import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

/**
 * Created by <PERSON> on 16/5/9.
 */
public class RichText {
    @Id
    private ObjectId _id;
    @Property(RichTextFields.sourceId)
    private String sourceId;
    @Property(RichTextFields.content)
    private String content;
    @Property(RichTextFields.title)
    private String title;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
