package com.facishare.fsc.core.storage.model;

import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.Date;

/**
 * Created by <PERSON> on 16/5/9.
 */
public class FileToken {
    @Id
    private ObjectId _id;
    @Property(FileTokenFields.EA)
    private String EA;
    @Property(FileTokenFields.eaYearMonth)
    private String eaYearMonth;
    @Property(FileTokenFields.fileToken)
    private String fileToken;
    @Property(FileTokenFields.fileType)
    private String fileType;
    @Property(FileTokenFields.fileName)
    private String fileName;
    @Property(FileTokenFields.filePath)
    private String filePath;
    @Property(FileTokenFields.createTime)
    private Date createTime;
    @Property(FileTokenFields.warehouseType)
    private String warehouseType;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getEA() {
        return EA;
    }

    public void setEA(String EA) {
        this.EA = EA;
    }

    public String getEaYearMonth() {
        return eaYearMonth;
    }

    public void setEaYearMonth(String eaYearMonth) {
        this.eaYearMonth = eaYearMonth;
    }

    public String getFileToken() {
        return fileToken;
    }

    public void setFileToken(String fileToken) {
        this.fileToken = fileToken;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(String warehouseType) {
        this.warehouseType = warehouseType;
    }
}
