package com.facishare.fsc.core.remote;

import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.core.exceptions.ExceptionConstant;
import com.facishare.fsc.core.exceptions.FSCDocPreViewException;
import com.facishare.fsc.core.model.GetDocumentPageData;
import com.facishare.fsc.core.model.GetPreviewInfo;
import com.facishare.fsc.core.remote.client.FSHttpClient;
import com.facishare.fsc.core.repository.impl.GlobalRepositoryImpl;
import com.facishare.fsi.proxy.model.global.config.GetEnterpriseConfigByEA;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.InputStreamEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Aaron on 16/5/9.
 */
@Service("docPreviewProxy")
@Slf4j
public class DocPreviewProxy {
    @Value("${fsc_http_proxy_config}")
    private String configName;
    private final static String FSI_ENTERPRISE = "FSI-Enterprise";
    private volatile String docPreviewProxyUrl;
    @Autowired
    private GlobalRepositoryImpl globalRepository;
    @PostConstruct
    public void init() {
        ConfigFactory.getInstance().getConfig(configName, iConfig -> {
            log.info("==================start "+configName+"===============");
            log.info("==================end "+configName+"===============");
            this.docPreviewProxyUrl = iConfig.get("doc.preview.proxy.url");
        });
    }

    private FSHttpClient httpClient = new FSHttpClient();

    public GetPreviewInfo.ResultData getPreviewInfo(String ea,String downloadUser,String docPath, String securityGroup) {
        GetEnterpriseConfigByEA.EnterpriseConfig enterpriseConfig=globalRepository.getEnterpriseConfigByEA(ea);
        GetPreviewInfo.Arg arg = new GetPreviewInfo.Arg(ea,
                docPath, securityGroup, downloadUser,
                enterpriseConfig.CreateYearMonth,
                enterpriseConfig.FspAddress);
        GetPreviewInfo.Result result = GetPreviewInfo.Result.getInstance(post("GetPreviewInfo", arg));
        log.info("getPreviewInfo:result:{}",JsonUtils.toJson(result));
        if (result.Data != null && result.Data.PageCount != 0) return result.Data;
        return null;
    }

    public GetDocumentPageData.ResultData getDocumentPage(String ea,String downloadUser,String docPath, String securityGroup,
                                                      int pageIndex, int expectedWidth,
                                                      int maxContentLength) {
        GetEnterpriseConfigByEA.EnterpriseConfig enterpriseConfig=globalRepository.getEnterpriseConfigByEA(ea);
        GetDocumentPageData.Arg arg = new GetDocumentPageData.Arg(ea,
                enterpriseConfig.FspV5Address,
                enterpriseConfig.CreateYearMonth, docPath,
                securityGroup, downloadUser, maxContentLength, pageIndex, expectedWidth);
        GetDocumentPageData.Result result = GetDocumentPageData.Result.getInstance(post("GetPreviewPage", arg));
        if (result.Data != null && result.Data.buffer != null) {
            return result.Data;
        }
        return null;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getURI(String uri) {
        return docPreviewProxyUrl + "/Doc/" + uri;
    }

    public byte[] post(String uri, Object arg) {
        log.info("uri:{},arg:{}",getURI(uri),JsonUtils.toJson(arg));
        Map<String, String> headers = new HashMap<>();
        try {
            String requestStr = JsonUtils.toJson(arg);
            InputStreamEntity inputStreamEntity = new InputStreamEntity(new ByteArrayInputStream(requestStr.getBytes(StandardCharsets.UTF_8)));
            inputStreamEntity.setContentEncoding("UTF-8");
            inputStreamEntity.setContentType(ContentType.APPLICATION_JSON.getMimeType());
            byte[] data=httpClient.post(getURI(uri), headers, inputStreamEntity);
            return data;
        } catch (IOException e) {
            throw new FSCDocPreViewException("fail to preview :"+getURI(uri)+",arg:"+JsonUtils.toJson(arg), e, ExceptionConstant.FSC_DOC_PRE_VIEW);
        }
    }

}
