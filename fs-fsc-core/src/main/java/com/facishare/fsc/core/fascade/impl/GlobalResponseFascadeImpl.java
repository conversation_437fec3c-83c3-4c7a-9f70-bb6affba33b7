package com.facishare.fsc.core.fascade.impl;

import com.facishare.fsc.common.utils.JsonUtils;
import com.facishare.fsc.common.utils.ValidateCodeUtils;
import com.facishare.fsc.core.fascade.GlobalResponseFascade;
import com.facishare.fsc.core.repository.impl.GlobalRepositoryImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by <PERSON> on 16/5/16.
 */
@Service("glabolResponseFascade")
@Slf4j
public class GlobalResponseFascadeImpl implements GlobalResponseFascade {
    @Autowired
    private GlobalRepositoryImpl globalRepository;

    @Override
    public Response.ResponseBuilder createImageCode(String userTag, String clientId, String epxId) {
        log.info("createImageCode:userTag:{},clientId:{},epxId:{}",userTag,clientId,epxId);
        String code = globalRepository.createImageCode(userTag, clientId, epxId);
        byte[] data = ValidateCodeUtils.createCodeBytes(28, 25, 1, 1, code);
        return Response.status(Response.Status.OK).header(HttpHeaders.CONTENT_TYPE, "image/jpeg").entity(data);
    }

    @Override
    public Response.ResponseBuilder prSignUp(String mobile, String name, String company, String post) {
        log.info("prSignUp:mobile:{},name:{},company:{},post:{}",mobile,name,company,post);
        if(Strings.isNullOrEmpty(mobile)||Strings.isNullOrEmpty(name)||Strings.isNullOrEmpty(company)||Strings.isNullOrEmpty(post)){
            return Response.status(Response.Status.BAD_REQUEST);
        }
        Map<String,String> results=Maps.newHashMap();
        if(mobile.length()>50||name.length()>50||company.length()>100||post.length()>50){
            results.put("Status","false");
            results.put("ErrorInfo","The length of the submitted data exceeds the limit");
            log.error("prSignUp:result:{},mobile:{},name:{},company:{},post:{}",results,mobile,name,company,post);
        }else{
            globalRepository.prSignUp(mobile,name,company,post);
            results.put("Status","true");
        }
        return Response.status(Response.Status.OK).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON).entity(JsonUtils.toJson(results));
    }

    @Override
    public Response.ResponseBuilder partnerRegister(String company, String name, String post, String mobile,
                                                    String product, String intention) {
        log.info("partnerRegister:mobile:{},name:{},company:{},post:{},product:{},intention:{}",mobile,name,company,post,product,intention);
        if(Strings.isNullOrEmpty(mobile)||Strings.isNullOrEmpty(name)||Strings.isNullOrEmpty(company)
                ||Strings.isNullOrEmpty(post)||Strings.isNullOrEmpty(intention)||Strings.isNullOrEmpty(product)){
            return Response.status(Response.Status.BAD_REQUEST);
        }
        Map<String,String> results=Maps.newHashMap();
        if(mobile.length()>50||name.length()>50||company.length()>100||post.length()>50||intention.length()>500||product.length()>500){
            results.put("Status","false");
            results.put("ErrorInfo","The length of the submitted data exceeds the limit");
            log.error("partnerRegister:result:{},mobile:{},name:{},company:{},post:{},product:{},intention:{}",results,mobile,name,company,post,product,intention);
        }else {
            globalRepository.partnerRegister(company,name,post,mobile,product,intention);
            results.put("Status","true");
        }

        return Response.status(Response.Status.OK).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON).entity(JsonUtils.toJson(results));
    }

    @Override
    public Response.ResponseBuilder addIndustryContact(String contactName, String contactPhone) {
        log.info("addIndustryContact:contactName:{},contactPhone:{}",contactName,contactPhone);
        Response.ResponseBuilder builder=Response.status(Response.Status.OK).header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
        Pattern pattern = Pattern.compile("^((\\+)?86 |((\\+)?86)?)0?1[34578]\\d{9}$");
        // 现在创建 matcher 对象
        Matcher m = pattern.matcher(contactPhone);
        Map<String,String> maps=Maps.newHashMap();
        if(!m.find()){
            builder.status(Response.Status.BAD_REQUEST);
            maps.put("Status","false");
            maps.put("ErrorInfo","invalid phone number");
            log.error("addIndustryContact:result:{},contactName:{},contactPhone:{}",maps,contactName,contactPhone);
        }else if(contactName.length()>20){
            builder.status(Response.Status.BAD_REQUEST);
            maps.put("Status","false");
            maps.put("ErrorInfo","Username length is up to 20 characters");
            log.error("addIndustryContact:result:{},contactName:{},contactPhone:{}",maps,contactName,contactPhone);
        }else{
            maps.put("Status",globalRepository.addIndustryContact(contactName,contactPhone)+"");
        }
        return builder.entity(JsonUtils.toJson(maps));
    }
}
