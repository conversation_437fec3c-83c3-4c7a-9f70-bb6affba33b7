package com.facishare.fsc.core;

import com.facishare.fsc.common.authenticate.AuthInfo;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletRequest;
import javax.ws.rs.container.ContainerRequestContext;
import java.util.Optional;

/**
 * Created by <PERSON> on 16/5/16.
 */
@Slf4j
public class FSCContext {
    private ContainerRequestContext requestContext;
    private ServletRequest servletRequest;
    private AuthInfo authInfo;
    private FSCVersion fscVersion;
    private static final ThreadLocal<FSCContext> LOCAL = new ThreadLocal<>();

    private FSCContext() {
    }

    public FSCVersion getFscVersion() {
        return fscVersion;
    }

    public void setFscVersion(FSCVersion fscVersion) {
        this.fscVersion = fscVersion;
    }

    public ContainerRequestContext getRequestContext() {
        return requestContext;
    }

    public void setRequestContext(ContainerRequestContext requestContext) {
        this.requestContext = requestContext;
    }

    public static void removeLocal() {
        LOCAL.remove();
    }

    public static FSCContext getLocal() {
        if (LOCAL.get() == null) {
            LOCAL.set(new FSCContext());
        }
        return LOCAL.get();
    }

    public static ContainerRequestContext getCurrentRequestContext() {
        return Optional.ofNullable(LOCAL.get()).map(FSCContext::getRequestContext).orElse(null);
    }

    public static ServletRequest getRequest() {
        return LOCAL.get().servletRequest;
    }

    public ServletRequest getServletRequest() {
        return servletRequest;
    }

    public AuthInfo getAuthInfo() {
        return authInfo;
    }

    public void setAuthInfo(AuthInfo authInfo) {
        this.authInfo = authInfo;
    }

    public void setServletRequest(ServletRequest servletRequest) {
        this.servletRequest = servletRequest;
    }
}
