<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>fs-fsc</artifactId>
    <groupId>com.facishare</groupId>
    <version>2.2.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>fs-fsc-provider</artifactId>
  <packaging>war</packaging>

  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <java.version>21</java.version>
    <jdk.version>21</jdk.version>
    <spring.version>4.3.21.RELEASE</spring.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsc-api</artifactId>
      <version>${project.parent.version}</version>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mongo-spring-support</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsc-common</artifactId>
      <version>${project.parent.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>javassist-3.14.0-GA</artifactId>
          <groupId>org.ow2.util.bundles</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
      <exclusions>
        <exclusion>
          <groupId>io.netty</groupId>
          <artifactId>netty</artifactId>
        </exclusion>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-log4j12</artifactId>
        </exclusion>
        <exclusion>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>spring</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>javassist</artifactId>
          <groupId>org.javassist</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty</artifactId>
          <groupId>org.jboss.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>httpcore</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>resteasy-jaxrs</artifactId>
          <groupId>org.jboss.resteasy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>resteasy-client</artifactId>
          <groupId>org.jboss.resteasy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>resteasy-jaxb-provider</artifactId>
          <groupId>org.jboss.resteasy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>resteasy-jdk-http</artifactId>
          <groupId>org.jboss.resteasy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jaxrs-api</artifactId>
          <groupId>org.jboss.resteasy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fastjson</artifactId>
          <groupId>com.alibaba</groupId>
        </exclusion>
        <exclusion>
          <artifactId>java-utils</artifactId>
          <groupId>com.fxiaoke.common</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-codec</artifactId>
          <groupId>commons-codec</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-io</artifactId>
          <groupId>commons-io</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-aop</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-beans</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-context</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-core</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-expression</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-web</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-eye-j4log</artifactId>
          <groupId>com.fxiaoke</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-api</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>activation</artifactId>
          <groupId>javax.activation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jcl-over-slf4j</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-kafka-support</artifactId>
          <groupId>com.fxiaoke.common</groupId>
        </exclusion>
        <exclusion>
          <artifactId>config-core</artifactId>
          <groupId>com.github.colin-lee</groupId>
        </exclusion>
        <exclusion>
          <artifactId>lombok</artifactId>
          <groupId>org.projectlombok</groupId>
        </exclusion>
        <exclusion>
          <artifactId>curator-client</artifactId>
          <groupId>org.apache.curator</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>jedis-spring-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.jboss.netty</groupId>
      <artifactId>netty</artifactId>
    </dependency>
    <!--dubbo改造rest-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-dubbo-rest-plugin</artifactId>
      <version>1.1.2-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-uc-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>
  </dependencies>
  <build>
    <finalName>fs-fsc-provider-final</finalName>
  </build>
</project>