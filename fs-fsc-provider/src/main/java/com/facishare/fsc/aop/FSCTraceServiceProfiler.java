package com.facishare.fsc.aop;

import com.github.autoconf.ConfigFactory;
import com.github.trace.aop.ServiceProfiler;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;

/**
 * zhiafj
 * 出现配置中的异常，不上报蜂眼
 */
@Slf4j
public class FSCTraceServiceProfiler extends ServiceProfiler {

  static Set<Class> classSet = new HashSet<>();

  public FSCTraceServiceProfiler() {
  }

  public void initIgnoreExceptionMap() {
    ConfigFactory.getInstance().getConfig("fs-fsc-provider-ignore-exception", config -> {
      Set<Class> tempClassSet = new HashSet<>();
      config.getLines().forEach(className -> {
            try {
              tempClassSet.add(Class.forName(className));
              log.info("ignore exception item:{}", className);
            } catch (Exception e) {
              log.error("{} class {} not found!", "fs-fsc-provider-ignore-exception", className);
            }
          }
      );
      classSet = tempClassSet;
    });
  }

  @Override
  protected boolean isFail(Throwable e) {
    if (classSet.contains(e.getClass()) || isCauseClassInIgnoreSet(e)) {
      log.info("ignore {} ", e.getClass().getName());
      return false;
    }
    return super.isFail(e);
  }

  private boolean isCauseClassInIgnoreSet(Throwable e) {
    return e.getCause() != null && classSet.contains(e.getCause().getClass());
  }
}
