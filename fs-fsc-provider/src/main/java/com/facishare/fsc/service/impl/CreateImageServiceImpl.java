package com.facishare.fsc.service.impl;

import com.facishare.fsc.api.model.CreateValidateCode;
import com.facishare.fsc.api.service.CreateImageService;
import com.facishare.fsc.common.utils.CheckCodeUtils;
import com.facishare.fsc.exception.ExceptionConstant;
import com.facishare.fsc.exception.FSCArgException;
import com.facishare.fsc.exception.FSCException;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;

/**
 * Created by <PERSON> on 16/6/6.
 */
@Service("createImageService")
public class CreateImageServiceImpl implements CreateImageService {

  @ReloadableProperty("clientVcWidth")
  private int clientVcWidth=131;

  @ReloadableProperty("clientVcHeight")
  private int clientVcHeight=56;

  @ReloadableProperty("clientFontSize")
  private int clientFontSize=25;


  @Override
  public CreateValidateCode.Result createValidateCode(CreateValidateCode.Arg arg) {
    if (Strings.isNullOrEmpty(arg.code)) {
      throw new FSCArgException(ExceptionConstant.DUBBO_ARG_ERROR);
    }

//    CheckCodeUtils checkCodeUtils = new CheckCodeUtils(arg.code, clientVcWidth, clientVcHeight);
//    checkCodeUtils.setFontSize(clientVcFontSize);
//    //byte[] data = ValidateCodeUtils.createCodeBytes(28, 25, 1, 1, arg.code);
    try {
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      CheckCodeUtils.outputImage(clientVcWidth,clientVcHeight,clientFontSize,out,arg.code);
      byte[] data = out.toByteArray();
      CreateValidateCode.Result result = new CreateValidateCode.Result();
      result.data = data;
      return result;
    } catch (Exception e) {
      throw new FSCException(e, ExceptionConstant.CREATE_VCODE_EXCEPTION);
    }
  }
}
