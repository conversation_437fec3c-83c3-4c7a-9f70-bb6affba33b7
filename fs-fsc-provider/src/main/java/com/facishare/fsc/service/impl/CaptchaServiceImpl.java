package com.facishare.fsc.service.impl;

import com.facishare.fsc.api.model.CreateCaptcha;
import com.facishare.fsc.api.model.ValidateCaptcha;
import com.facishare.fsc.api.service.CaptchaService;
import com.facishare.fsc.common.utils.CheckCodeUtils;
import com.facishare.fsc.dao.impl.CaptchaDaoImpl;
import com.facishare.fsc.exception.ExceptionConstant;
import com.facishare.fsc.exception.FSCArgException;
import com.facishare.uc.api.model.CreateValidateCodeByArg;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.api.IConfigFactory;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;

@Service("captchaServiceImpl")
@Component
@Slf4j(topic = "CaptchaService")
public class CaptchaServiceImpl implements CaptchaService {

  @Autowired
  private CaptchaDaoImpl captchaDao;
  @Autowired
  private com.facishare.uc.api.service.CaptchaService captchaServiceUc;


  private int webVcWidth = 131;
  private int webVcHeight = 56;
  private int codeLength = 4;
  private int vcType = 1;//1表示中文
  private int fontSize = 30;


  @PostConstruct
  private void init() {
    IConfigFactory factory = ConfigFactory.getInstance();
    factory.getConfig("fs-fsc-provider", (IConfig config) -> {
      webVcWidth = config.getInt("webVcWidth", webVcWidth);
      webVcHeight = config.getInt("webVcHeight", webVcHeight);
      codeLength = config.getInt("codeLength", codeLength);
      vcType = config.getInt("vcType", vcType);
      fontSize = config.getInt("fontSize", fontSize);
    });
  }


  @Override
  public CreateCaptcha.Result create(CreateCaptcha.Arg arg) {
    if (Strings.isNullOrEmpty(arg.cId) || Strings.isNullOrEmpty(arg.epxId)) {
      throw new FSCArgException(ExceptionConstant.DUBBO_ARG_ERROR);
    }
    CreateCaptcha.Result result = new CreateCaptcha.Result();
    //如果调用方传了lan参数为en，则验证码为数字和英文字符
    if (!Strings.isNullOrEmpty(arg.lan) && "en".equalsIgnoreCase(arg.lan)) {
      vcType = 0;
    } else {
      vcType = 1;
    }
    try {
      String code = CheckCodeUtils.generateVerifyCode(codeLength);
      CreateValidateCodeByArg.Arg  verification= new CreateValidateCodeByArg.Arg();
      verification.setExpireSeconds(100);
      verification.setId(arg.epxId);
      verification.setCode(code.toUpperCase());
      log.info("调用UC生成验证码:{}",verification);
      CreateValidateCodeByArg.Result validateCodeByArg = captchaServiceUc.createValidateCodeByArg(verification);
      String finalCode = validateCodeByArg.getCode();
      log.info("从UC获取验证码成功:{}",finalCode);
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      CheckCodeUtils.outputImage(webVcWidth, webVcHeight, fontSize, out, finalCode);
      byte[] data = out.toByteArray();
      result.data = data;
      log.info("生成验证码成功:{}",code);
    } catch (Exception e) {
      log.error("create vc happend exception!", e);
    }
    return result;
  }

  @Override
  public ValidateCaptcha.Result validate(ValidateCaptcha.Arg arg) {
    if (Strings.isNullOrEmpty(arg.cid) || Strings.isNullOrEmpty(arg.epxId) || Strings.isNullOrEmpty(arg.code)) {
      throw new FSCArgException(ExceptionConstant.DUBBO_ARG_ERROR);
    }
    ValidateCaptcha.Result result = new ValidateCaptcha.Result();
    result.success =  captchaServiceUc.verify(arg.epxId,arg.code);
    //    result.success = captcha != null;
    return result;
  }

  public CaptchaDaoImpl getCaptchaDao() {
    return captchaDao;
  }

  public void setCaptchaDao(CaptchaDaoImpl captchaDao) {
    this.captchaDao = captchaDao;
  }
}
