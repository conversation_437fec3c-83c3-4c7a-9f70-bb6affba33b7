package com.facishare.fsc.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.fsc.api.model.BatchGenerateFilePreviewShareToken;
import com.facishare.fsc.api.model.CreateAccessAvatarToken;
import com.facishare.fsc.api.model.CreateAccessFileToken;
import com.facishare.fsc.api.model.CreateFileShareId;
import com.facishare.fsc.api.model.CreateFileShareIds;
import com.facishare.fsc.api.model.CreateFileShareIds.Arg;
import com.facishare.fsc.api.model.CreateFileShareIds.Result;
import com.facishare.fsc.api.model.CreateFileShareToken;
import com.facishare.fsc.api.model.CreatePreviewShareTokens;
import com.facishare.fsc.api.service.SharedFileService;
import com.facishare.fsc.common.utils.AES256Utils;
import com.facishare.fsc.exception.ExceptionConstant;
import com.facishare.fsc.exception.FSCArgException;
import com.facishare.fsc.remote.FileShareIdService;
import com.google.common.base.Joiner;
import com.google.common.base.Stopwatch;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by Aaron on 16/7/4.
 */
@Service("sharedFileService")
@Slf4j
public class SharedFileServiceImpl implements SharedFileService {

  @Autowired
  private FileShareIdService fileShareIdService;

  @Override
  public CreateAccessAvatarToken.Result createAccessAvatarToken(CreateAccessAvatarToken.Arg arg) {
    Stopwatch stopwatch = Stopwatch.createStarted();
    log.info("createAccessAvatarToken args:{}", arg.toString());
    if (Strings.isNullOrEmpty(arg.EA) || Strings.isNullOrEmpty(arg.path)) {
      throw new FSCArgException("createAccessAvatarToken.Arg Error,arg:" + arg, ExceptionConstant.DUBBO_ARG_ERROR);
    }
    String avatarToken = AES256Utils.encode(arg.EA + ":" + arg.employeeId + ":" + arg.path);
    stopwatch.stop();
    log.info("createAccessAvatarToken end avatarToken:{},cost:{} ms", avatarToken,stopwatch.elapsed().toMillis());
    return new CreateAccessAvatarToken.Result(avatarToken);
  }

  @Override
  public CreateAccessFileToken.Result createAccessFileToken(CreateAccessFileToken.Arg arg) {
    Stopwatch stopwatch = Stopwatch.createStarted();
    log.info("createAccessFileToken args:{}", arg.toString());
    if (Strings.isNullOrEmpty(arg.EA) || Strings.isNullOrEmpty(arg.path)) {
      throw new FSCArgException("createAccessFileToken.Arg Error,arg:" + arg.toString(), ExceptionConstant.DUBBO_ARG_ERROR);
    }
    String fileToken = AES256Utils.encode(Joiner.on(':')
        .join(arg.EA, arg.employeeId, arg.path, Objects.toString(arg.securityGroup, "-"),
            System.currentTimeMillis(), arg.expireMills));
    stopwatch.stop();
    log.info("createAccessFileToken end fileToken:{},cost:{} ms", fileToken,stopwatch.elapsed().toMillis());
    return new CreateAccessFileToken.Result(fileToken);
  }

  @Override
  public CreateFileShareId.Result createFileShareId(CreateFileShareId.Arg arg) throws Exception {
    Stopwatch stopwatch = Stopwatch.createStarted();
    log.info("createFileShareId args:{}", arg.toString());
    String fileId;
    String ea = arg.ea;
    String path = arg.path;
    String sg = arg.securityGroup;
    int employeeId = arg.employeeId;
    if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(path) || employeeId <= 0) {
      throw new FSCArgException("createFileShareId.Arg Error,arg:" + arg.toString(), ExceptionConstant.DUBBO_ARG_ERROR);
    }
    fileId = Strings.isNullOrEmpty(sg) ? fileShareIdService.findOrCreate(ea, employeeId, path) : fileShareIdService.findOrCreate(ea, employeeId, path, sg);
    stopwatch.stop();
    log.info("createFileShareId end fileId:{},cost:{} ms", fileId,stopwatch.elapsed().toMillis());
    return new CreateFileShareId.Result(fileId);
  }

  @Override
  public Result createFileShareIds(Arg arg) {
    Stopwatch stopwatch = Stopwatch.createStarted();
    log.info("createFileShareIds args:{}", arg.toString());
    Map<String, String> resultMap = Maps.newHashMap();
    String ea = arg.ea;
    int employeeId = arg.employeeId;
    List<String> pathList = arg.pathList;
    String sg = arg.securityGroup;
    if (Strings.isNullOrEmpty(ea) || pathList == null || pathList.isEmpty() || employeeId <= 0) {
      throw new FSCArgException("createFileShareIds.Arg Error,arg:" + arg.toString(), ExceptionConstant.DUBBO_ARG_ERROR);
    }
    pathList.forEach(item -> {
      String fileId = "";
      try {
        fileId = Strings.isNullOrEmpty(sg) ? fileShareIdService.findOrCreate(ea, employeeId, item) : fileShareIdService.findOrCreate(ea, employeeId, item, sg);
      } catch (Exception e) {
        log.error("createFileShareIds Exception {},{}", ea, item);
      }
      resultMap.put(item, fileId);
    });
    stopwatch.stop();
    log.info("createFileShareIds end resultMap.size:{},cost:{} ms", resultMap.size(),stopwatch.elapsed().toMillis());
    return new CreateFileShareIds.Result(resultMap);
  }

  @Override
  public CreatePreviewShareTokens.Result createPreviewShareTokens(CreatePreviewShareTokens.Arg arg) throws Exception {
    Stopwatch stopwatch = Stopwatch.createStarted();
    log.info("createPreviewShareTokens args:{}", arg.toString());
    Map<String, String> resultMap = Maps.newHashMap();
    String ea = arg.ea;
    int employeeId = arg.employeeId;
    List<String> pathList = arg.pathList;
    String sg = Strings.isNullOrEmpty(arg.securityGroup) ? "" : arg.securityGroup;
    if (Strings.isNullOrEmpty(ea) || pathList == null || pathList.isEmpty() || employeeId <= 0) {
      throw new FSCArgException("createPreviewShareTokens.Arg Error,arg:" + arg, ExceptionConstant.DUBBO_ARG_ERROR);
    }
    pathList.forEach(item -> {
      String fileId = "";
      try {
        fileId = AES256Utils.encode(Joiner.on(':').join(ea, employeeId, item, sg, System.currentTimeMillis()));
      } catch (Exception e) {
        log.error("createPreviewShareTokens Exception {},{}", ea, item, e);
      }
      resultMap.put(item, fileId);
    });
    stopwatch.stop();
    log.info("createPreviewShareTokens end resultMap.size:{},cost:{} ms", resultMap.size(),stopwatch.elapsed().toMillis());
    return new CreatePreviewShareTokens.Result(resultMap);
  }

  @Override
  public BatchGenerateFilePreviewShareToken.Result batchGenerateFilePreviewShareToken(BatchGenerateFilePreviewShareToken.Arg arg) {
    Stopwatch stopwatch = Stopwatch.createStarted();
    log.info("start batchGenerateFilePreviewShareToken args:{}", arg.toString());
    Map<String, String> shareTokens = Maps.newHashMap();
    arg.getFileInfoList().forEach(fileInfo -> {
      long expireTime = getExpiresTimestamp(fileInfo.getExpireTime());
      String ciphertext = Joiner.on(':')
          .join(fileInfo.getEnterpriseAccount(), fileInfo.getEmployeeId(), fileInfo.getPath(),
              fileInfo.getSecurityGroup(),expireTime);
      String shareToken = AES256Utils.encode(ciphertext);
      shareTokens.put(fileInfo.getPath(), shareToken);
    });
    stopwatch.stop();
    log.info("end batchGenerateFilePreviewShareToken shareTokens:{},cost:{} ms", shareTokens,stopwatch.elapsed().toMillis());
    return BatchGenerateFilePreviewShareToken.Result.of(shareTokens);
  }

  public static long getExpiresTimestamp(long expires) {
    if (expires < 3600) {
      expires = 3600;
    }
    if (expires > 604800) {
      expires = 604800;
    }
    Instant currentInstant = Instant.now();
    Instant futureInstant = currentInstant.plusSeconds(3600-expires);
    return futureInstant.toEpochMilli();
  }

  @Override
  public CreateFileShareToken.Result createFileShareTokens(CreateFileShareToken.Arg arg) throws Exception {
    Stopwatch stopwatch = Stopwatch.createStarted();
    log.info("createFileShareTokens args:{}", arg.toString());
    Map<String, String> resultMap = Maps.newHashMap();
    String ea = arg.ea;
    int employeeId = arg.employeeId;
    List<String> pathList = arg.pathList;
    //默认7天
    int invalidDay = arg.expireDay <= 0 ? 7 : arg.expireDay;
    if (Strings.isNullOrEmpty(ea) || pathList == null || pathList.isEmpty() || employeeId <= 0) {
      throw new FSCArgException("createFileShareTokens.Arg Error,arg:" + arg.toString(), ExceptionConstant.DUBBO_ARG_ERROR);
    }
    pathList.forEach(item -> {
      String token = "";
      try {
        token = fileShareIdService.createTokenByPath(ea, employeeId, item, invalidDay);
      } catch (Exception e) {
        log.error("createFileShareIds Exception {},{}", ea, item);
      }
      resultMap.put(item, token);
    });
    stopwatch.stop();
    log.info("createFileShareTokens end resultMap.size:{},cost:{} ms", resultMap.size(),stopwatch.elapsed().toMillis());
    return new CreateFileShareToken.Result(resultMap);
  }

  @Override
  public CreateFileShareToken.Result createFileShareTokensV2(CreateFileShareToken.Arg arg) throws Exception {
    Stopwatch stopwatch = Stopwatch.createStarted();
    log.info("createFileShareTokensV2 request:{}", JSON.toJSONString(arg));
    String ea = arg.ea;
    int employeeId = arg.employeeId;
    String business = arg.business;
    if((arg.expireMinute > 30 || arg.expireMinute < 0) && !("stone-fileserver".equals(arg.business))){
      throw new FSCArgException("The validity period parameter is illegal (should be greater than 0 and less than or equal to 30)",400);
    }
    int expireMinute = arg.expireMinute;
    if (arg.pathList == null || arg.pathList.isEmpty()) {
      return null;
    }
    Map<String, String> tokenMap = Maps.newHashMap();
    arg.pathList.forEach(path -> {
      String token = "";
      try {
        token = fileShareIdService.createTokenByPath(ea, employeeId, path, business, expireMinute);
      } catch (Exception e) {
        log.error("createFileShareIds Exception {},{}", ea, path);
      }
      tokenMap.put(path, token);
    });
    stopwatch.stop();
    log.info("createFileShareTokensV2 end resultMap.size:{},cost:{} ms", tokenMap.size(),stopwatch.elapsed().toMillis());
    return new CreateFileShareToken.Result(tokenMap);
  }
}
