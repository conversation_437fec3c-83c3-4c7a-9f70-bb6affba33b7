package com.facishare.fsc.exception;

import com.github.autoconf.ConfigFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by <PERSON> on 16/5/9.
 */
public class ExceptionConstant {

  public static final int DUBBO_ARG_ERROR = 20006;
  public static final int  CREATE_VCODE_EXCEPTION=30001;
  public static final int CREATE_SHAREIDS_ERROR = 20009;
  private static final Map<String, String> exceptionsMessageMap = new ConcurrentHashMap<String, String>();

  static {
    ConfigFactory.getInstance().getConfig("fs-fsc-constant-dict", config -> {
      exceptionsMessageMap.clear();
      exceptionsMessageMap.putAll(config.getAll());
    });
  }

  public static String getExceptionTip(int code) {
    return exceptionsMessageMap.getOrDefault(code + "", "General exception information");
  }
}
