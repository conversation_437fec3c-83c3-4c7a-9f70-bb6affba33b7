package com.facishare.fsc.exception;


/**
 * Created by <PERSON> on 16/6/6.
 */
public class FSCArgException extends FSCException {

  public FSCArgException(int code) {
    super(code);
  }

  public FSCArgException(String message, int code) {
    super(message, code);
  }

  public FSCArgException(String message, Throwable cause, int code) {
    super(message, cause, code);
  }

  public FSCArgException(Throwable cause, int code) {
    super(cause, code);
  }
}
