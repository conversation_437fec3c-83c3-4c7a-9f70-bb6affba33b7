package com.facishare.fsc.remote;

import com.fxiaoke.common.Guard;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.api.IConfigFactory;
import com.github.jedis.support.JedisCmd;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * Created by liuq on 2017/1/17.
 */
@Service
public class FileShareIdService {
  @Autowired
  private JedisCmd redisCache;
  private String fileShareSkey = "";

  @PostConstruct
  private void init() {
    IConfigFactory factory = ConfigFactory.getInstance();
    factory.getConfig("fs-fsc-fileshare", (IConfig config) -> {
      fileShareSkey = config.get("skey");
    });
  }

  public String findOrCreate(String ea, int employeeId, String path) throws Exception {
    //检测redis里面是否有
    String redisKey = "_fileshare_" + ea + "$" + path;
    String rawString = ea + "$" + employeeId + "$" + path;
    return getFileShareSkey(redisKey, rawString);
  }

  public String createTokenByPath(String ea, int employeeId, String path, int expireTime) throws Exception {
    Guard guard = new Guard(fileShareSkey);
    String rawString = ea + "$" + employeeId + "$" + path + "$" + System.currentTimeMillis() + "$" + expireTime;
    return guard.encode2(rawString);
  }

  public String createTokenByPath(String ea, int employeeId, String path, String business, long expireMinute) {
    Guard guard = new Guard(fileShareSkey);
    String rawString = ea + "$" + employeeId + "$" + path + "$" + business + "$" + (System.currentTimeMillis() + expireMinute * 60 * 1000) + "$" + "V2";
    return guard.encode2(rawString);
  }

  private String getFileShareSkey(String redisKey, String rawString) throws Exception {
    String fileId;
    Guard guard = new Guard(fileShareSkey);
    fileId = guard.encode2(rawString);
    return fileId;
  }

  public String findOrCreate(String ea, int employeeId, String path, String sg) throws Exception {

    String redisKey = "_fileshare_" + ea + "$" + path + "$" + sg;
    String rawString = ea + "$" + employeeId + "$" + path + "$" + sg;
    return getFileShareSkey(redisKey, rawString);

  }

  public static void main(String[] args) throws Exception {
    Guard guard = new Guard("1111111111111111");
    String ea = "71554";
    String employeeId = "1000";
    String path = "N_202101_06_c2893a9ecea54bbf94b8af80c18c930b1";
    String rawString = ea + "$" + employeeId + "$" + path + "$" + System.currentTimeMillis() + "$" + 1;
    String token = guard.encode2(rawString);
    System.out.println(token);
  }


}
