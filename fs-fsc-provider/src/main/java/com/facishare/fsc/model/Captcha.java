package com.facishare.fsc.model;

import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.Date;

/**
 * Created by <PERSON> on 16/5/9.
 */
@Entity(value = "Captcha",noClassnameStored = true)
public class Captcha {
    @Id
    private ObjectId _id;
    @Property(CaptchaFields.EPXId)
    private String EPXId;
    @Property(CaptchaFields.clientId)
    private String clientId;
    @Property(CaptchaFields.userTag)
    private String userTag;
    @Property(CaptchaFields.expireAt)
    private Date expireAt;
    @Property(CaptchaFields.validateCode)
    private String validateCode;

    public ObjectId get_id() {
        return _id;
    }

    public String getValidateCode() {
        return validateCode;
    }

    public void setValidateCode(String validateCode) {
        this.validateCode = validateCode;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getEPXId() {
        return EPXId;
    }

    public void setEPXId(String EPXId) {
        this.EPXId = EPXId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getUserTag() {
        return userTag;
    }

    public void setUserTag(String userTag) {
        this.userTag = userTag;
    }

    public Date getExpireAt() {
        return expireAt;
    }

    public void setExpireAt(Date expireAt) {
        this.expireAt = expireAt;
    }

    @Override
    public String toString() {
        return "Captcha{" +
                "_id=" + _id +
                ", EPXId='" + EPXId + '\'' +
                ", clientId='" + clientId + '\'' +
                ", userTag='" + userTag + '\'' +
                ", expireAt=" + expireAt +
                ", validateCode='" + validateCode + '\'' +
                '}';
    }
}
