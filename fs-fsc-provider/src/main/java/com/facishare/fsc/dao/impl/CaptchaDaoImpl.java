package com.facishare.fsc.dao.impl;

import com.facishare.fsc.dao.CaptchaDao;
import com.facishare.fsc.model.Captcha;
import com.facishare.fsc.model.CaptchaFields;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON> on 16/5/9.
 */
@Component
public class CaptchaDaoImpl extends BaseDao implements CaptchaDao {

  @Override
  public Captcha findCaptcha(String cId, String epxId, String code) {
    Query<Captcha> query = createQuery(shareContentDB, Captcha.class);
    query.field(CaptchaFields.clientId).equal(cId).field(CaptchaFields.EPXId).equal(epxId)
        .field(CaptchaFields.validateCode).equal(code);
    return query.get();
  }

  @Override
  public Captcha saveCaptcha(Captcha captcha) {
    return save(shareContentDB, captcha);
  }
}
