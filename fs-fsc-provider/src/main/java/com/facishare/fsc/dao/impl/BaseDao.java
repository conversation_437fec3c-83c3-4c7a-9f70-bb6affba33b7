package com.facishare.fsc.dao.impl;

import com.github.mongo.support.DatastoreExt;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by <PERSON> on 16/5/9.
 */
public class BaseDao {

  public final String shareContentDB = "ShareContent";
  public final String qrCodedDB = "QRLogin";
  @Autowired
  private DatastoreExt datastoreExt;

  public void setDatastoreExt(DatastoreExt datastoreExt) {
    this.datastoreExt = datastoreExt;
  }

  public <T> Query<T> createQuery(String db, Class<T> collection) {
    datastoreExt = datastoreExt.use(db);
    return datastoreExt.createQuery(collection);
  }

  public <T> UpdateOperations<T> createUpdateOperations(String db, Class<T> clazz) {
    datastoreExt = datastoreExt.use(db);
    return datastoreExt.createUpdateOperations(clazz);
  }

  public <T> T save(String db, T entity) {
    datastoreExt = datastoreExt.use(db);
    datastoreExt.save(entity);
    return entity;
  }
}
