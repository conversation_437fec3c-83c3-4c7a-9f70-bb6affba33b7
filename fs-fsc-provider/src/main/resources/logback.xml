<configuration scan="false" scanPeriod="1 seconds" debug="false">
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder 默认配置为PatternLayoutEncoder -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <file>${catalina.home}/logs/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/fs-app-%d{yyyyMMdd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                }%n
            </pattern>
        </encoder>
    </appender>

    <!-- 异步输出日志避免阻塞服务 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="RollingFile"/>
    </appender>

    <!-- 配置基础组件为WARN级别，避免打印过多影响服务自己日志 -->
    <logger name="druid.sql" level="ERROR"/>
    <logger name="org.hibernate" level="ERROR"/>
    <logger name="org.springframework" level="ERROR"/>
    <logger name="org.apache" level="ERROR"/>
    <logger name="com.github.trace" level="ERROR"/>
    <logger name="com.facishare.fsi.proxy" level="ERROR"/>
    <logger name="com.fxiaoke.j4log.J4LogFactory" level="ERROR"/>
    <logger name="com.fs.fmcg.sdk.ai" level="INFO"/>
    <logger name="RocketmqClient" level="WARN"/>
    <logger name="RocketmqCommon" level="WARN"/>
    <logger name="RocketmqRemoting" level="WARN"/>

    <root level="INFO">
        <appender-ref ref="ASYNC"/>
    </root>
</configuration>
