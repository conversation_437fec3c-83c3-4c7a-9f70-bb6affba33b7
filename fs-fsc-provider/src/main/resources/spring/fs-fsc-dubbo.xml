<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
    <dubbo:application name="${dubbo.application.name}"></dubbo:application>

    <dubbo:registry address="${dubbo.registry.address}"></dubbo:registry>

    <dubbo:protocol id="dubbo" name="dubbo" port="${dubbo.server.port}" threads="500"/>
    <dubbo:provider filter="tracerpc"/>

    <dubbo:service interface="com.facishare.fsc.api.service.CaptchaService" ref="captchaServiceImpl" protocol="dubbo"/>
    <dubbo:service interface="com.facishare.fsc.api.service.CreateImageService" ref="createImageService" protocol="dubbo"/>
    <dubbo:service interface="com.facishare.fsc.api.service.SharedFileService" ref="sharedFileService" protocol="dubbo"/>

</beans>