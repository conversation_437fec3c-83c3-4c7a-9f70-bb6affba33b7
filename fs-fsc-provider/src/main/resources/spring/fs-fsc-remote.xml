<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


  <import resource="classpath:fs-uc-rest.xml"/>

  <bean id="userCenterApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
    <property name="configName" value="fs-uc-api-rest-client"/>
  </bean>
</beans>
