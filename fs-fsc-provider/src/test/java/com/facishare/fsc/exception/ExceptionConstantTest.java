package com.facishare.fsc.exception;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for ExceptionConstant
 */
public class ExceptionConstantTest {

    @Test
    public void testExceptionCodeConstants() {
        assertEquals(20006, ExceptionConstant.DUBBO_ARG_ERROR);
        assertEquals(30001, ExceptionConstant.CREATE_VCODE_EXCEPTION);
        assertEquals(20009, ExceptionConstant.CREATE_SHAREIDS_ERROR);
    }

    @Test
    public void testExceptionCodeUniqueness() {
        int[] codes = {
            ExceptionConstant.DUBBO_ARG_ERROR,
            ExceptionConstant.CREATE_VCODE_EXCEPTION,
            ExceptionConstant.CREATE_SHAREIDS_ERROR
        };

        // Check that all codes are unique
        for (int i = 0; i < codes.length; i++) {
            for (int j = i + 1; j < codes.length; j++) {
                assertNotEquals("Exception codes should be unique", codes[i], codes[j]);
            }
        }
    }

    @Test
    public void testExceptionCodeRange() {
        // DUBBO_ARG_ERROR and CREATE_SHAREIDS_ERROR should be in the 20000 range
        assertTrue(ExceptionConstant.DUBBO_ARG_ERROR >= 20000 && ExceptionConstant.DUBBO_ARG_ERROR < 30000);
        assertTrue(ExceptionConstant.CREATE_SHAREIDS_ERROR >= 20000 && ExceptionConstant.CREATE_SHAREIDS_ERROR < 30000);
        
        // CREATE_VCODE_EXCEPTION should be in the 30000 range
        assertTrue(ExceptionConstant.CREATE_VCODE_EXCEPTION >= 30000 && ExceptionConstant.CREATE_VCODE_EXCEPTION < 40000);
    }

    @Test
    public void testGetExceptionTipWithValidCode() {
        // Test with known exception codes
        String tip1 = ExceptionConstant.getExceptionTip(ExceptionConstant.DUBBO_ARG_ERROR);
        assertNotNull(tip1);
        assertFalse(tip1.isEmpty());

        String tip2 = ExceptionConstant.getExceptionTip(ExceptionConstant.CREATE_VCODE_EXCEPTION);
        assertNotNull(tip2);
        assertFalse(tip2.isEmpty());

        String tip3 = ExceptionConstant.getExceptionTip(ExceptionConstant.CREATE_SHAREIDS_ERROR);
        assertNotNull(tip3);
        assertFalse(tip3.isEmpty());
    }

    @Test
    public void testGetExceptionTipWithInvalidCode() {
        // Test with unknown exception code
        String tip = ExceptionConstant.getExceptionTip(99999);
        assertEquals("General exception information", tip);
    }

    @Test
    public void testGetExceptionTipWithZeroCode() {
        String tip = ExceptionConstant.getExceptionTip(0);
        assertEquals("General exception information", tip);
    }

    @Test
    public void testGetExceptionTipWithNegativeCode() {
        String tip = ExceptionConstant.getExceptionTip(-1);
        assertEquals("General exception information", tip);
    }

    @Test
    public void testGetExceptionTipConsistency() {
        // Test that calling the same method multiple times returns the same result
        int code = ExceptionConstant.DUBBO_ARG_ERROR;
        String tip1 = ExceptionConstant.getExceptionTip(code);
        String tip2 = ExceptionConstant.getExceptionTip(code);
        assertEquals(tip1, tip2);
    }

    @Test
    public void testGetExceptionTipNotNull() {
        // Test that getExceptionTip never returns null
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.DUBBO_ARG_ERROR));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.CREATE_VCODE_EXCEPTION));
        assertNotNull(ExceptionConstant.getExceptionTip(ExceptionConstant.CREATE_SHAREIDS_ERROR));
    }

    @Test
    public void testDefaultExceptionMessage() {
        // Test the default message for unknown codes
        String defaultMessage = "General exception information";
        assertEquals(defaultMessage, ExceptionConstant.getExceptionTip(Integer.MAX_VALUE));
        assertEquals(defaultMessage, ExceptionConstant.getExceptionTip(Integer.MIN_VALUE));
        assertEquals(defaultMessage, ExceptionConstant.getExceptionTip(-999));
        assertEquals(defaultMessage, ExceptionConstant.getExceptionTip(999999));
    }

    @Test
    public void testSpecificExceptionCodes() {
        // Test specific code values
        assertEquals(20006, ExceptionConstant.DUBBO_ARG_ERROR);
        assertEquals(30001, ExceptionConstant.CREATE_VCODE_EXCEPTION);
        assertEquals(20009, ExceptionConstant.CREATE_SHAREIDS_ERROR);
    }

    @Test
    public void testExceptionCodeTypes() {
        // Test that all codes are positive integers
        assertTrue(ExceptionConstant.DUBBO_ARG_ERROR > 0);
        assertTrue(ExceptionConstant.CREATE_VCODE_EXCEPTION > 0);
        assertTrue(ExceptionConstant.CREATE_SHAREIDS_ERROR > 0);
    }

    @Test
    public void testExceptionCodeBoundaries() {
        // Test boundary values
        assertTrue(ExceptionConstant.DUBBO_ARG_ERROR < Integer.MAX_VALUE);
        assertTrue(ExceptionConstant.CREATE_VCODE_EXCEPTION < Integer.MAX_VALUE);
        assertTrue(ExceptionConstant.CREATE_SHAREIDS_ERROR < Integer.MAX_VALUE);
        
        assertTrue(ExceptionConstant.DUBBO_ARG_ERROR > Integer.MIN_VALUE);
        assertTrue(ExceptionConstant.CREATE_VCODE_EXCEPTION > Integer.MIN_VALUE);
        assertTrue(ExceptionConstant.CREATE_SHAREIDS_ERROR > Integer.MIN_VALUE);
    }
}
