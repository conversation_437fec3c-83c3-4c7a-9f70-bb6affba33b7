package com.facishare.fsc.api.model;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for FileInfo model
 */
public class FileInfoTest {

    @Test
    public void testDefaultConstructor() {
        FileInfo fileInfo = new FileInfo();
        assertNotNull(fileInfo);
    }

    @Test
    public void testAllArgsConstructor() {
        String enterpriseAccount = "test_ea";
        int employeeId = 123;
        String path = "/test/path";
        String securityGroup = "test_sg";
        long expireTime = System.currentTimeMillis();

        FileInfo fileInfo = new FileInfo(enterpriseAccount, employeeId, path, securityGroup, expireTime);

        assertEquals(enterpriseAccount, fileInfo.getEnterpriseAccount());
        assertEquals(employeeId, fileInfo.getEmployeeId());
        assertEquals(path, fileInfo.getPath());
        assertEquals(securityGroup, fileInfo.getSecurityGroup());
        assertEquals(expireTime, fileInfo.getExpireTime());
    }

    @Test
    public void testSettersAndGetters() {
        FileInfo fileInfo = new FileInfo();
        
        String enterpriseAccount = "test_ea";
        int employeeId = 456;
        String path = "/another/path";
        String securityGroup = "another_sg";
        long expireTime = System.currentTimeMillis() + 3600000;

        fileInfo.setEnterpriseAccount(enterpriseAccount);
        fileInfo.setEmployeeId(employeeId);
        fileInfo.setPath(path);
        fileInfo.setSecurityGroup(securityGroup);
        fileInfo.setExpireTime(expireTime);

        assertEquals(enterpriseAccount, fileInfo.getEnterpriseAccount());
        assertEquals(employeeId, fileInfo.getEmployeeId());
        assertEquals(path, fileInfo.getPath());
        assertEquals(securityGroup, fileInfo.getSecurityGroup());
        assertEquals(expireTime, fileInfo.getExpireTime());
    }

    @Test
    public void testEquals() {
        FileInfo fileInfo1 = new FileInfo("ea1", 123, "/path1", "sg1", 1000L);
        FileInfo fileInfo2 = new FileInfo("ea1", 123, "/path1", "sg1", 1000L);
        FileInfo fileInfo3 = new FileInfo("ea2", 456, "/path2", "sg2", 2000L);

        assertEquals(fileInfo1, fileInfo2);
        assertNotEquals(fileInfo1, fileInfo3);
        assertNotEquals(fileInfo1, null);
        assertNotEquals(fileInfo1, "not a FileInfo");
    }

    @Test
    public void testHashCode() {
        FileInfo fileInfo1 = new FileInfo("ea1", 123, "/path1", "sg1", 1000L);
        FileInfo fileInfo2 = new FileInfo("ea1", 123, "/path1", "sg1", 1000L);

        assertEquals(fileInfo1.hashCode(), fileInfo2.hashCode());
    }

    @Test
    public void testToString() {
        FileInfo fileInfo = new FileInfo("ea1", 123, "/path1", "sg1", 1000L);
        String toString = fileInfo.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("ea1"));
        assertTrue(toString.contains("123"));
        assertTrue(toString.contains("/path1"));
        assertTrue(toString.contains("sg1"));
        assertTrue(toString.contains("1000"));
    }

    @Test
    public void testNullValues() {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setEnterpriseAccount(null);
        fileInfo.setPath(null);
        fileInfo.setSecurityGroup(null);

        assertNull(fileInfo.getEnterpriseAccount());
        assertNull(fileInfo.getPath());
        assertNull(fileInfo.getSecurityGroup());
    }

    @Test
    public void testEmptyValues() {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setEnterpriseAccount("");
        fileInfo.setPath("");
        fileInfo.setSecurityGroup("");

        assertEquals("", fileInfo.getEnterpriseAccount());
        assertEquals("", fileInfo.getPath());
        assertEquals("", fileInfo.getSecurityGroup());
    }

    @Test
    public void testNegativeEmployeeId() {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setEmployeeId(-1);
        assertEquals(-1, fileInfo.getEmployeeId());
    }

    @Test
    public void testZeroEmployeeId() {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setEmployeeId(0);
        assertEquals(0, fileInfo.getEmployeeId());
    }

    @Test
    public void testNegativeExpireTime() {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setExpireTime(-1L);
        assertEquals(-1L, fileInfo.getExpireTime());
    }

    @Test
    public void testZeroExpireTime() {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setExpireTime(0L);
        assertEquals(0L, fileInfo.getExpireTime());
    }
}
