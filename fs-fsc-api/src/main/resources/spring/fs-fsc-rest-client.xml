<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <bean name="dubboRestHttpClient" id="dubboRestHttpClient" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"/>

  <bean id="FSCApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
    <property name="configName" value= "fs-fsc-api-rest-client"/>
  </bean>

  <bean id="sharedFileDubboService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean">
    <property name="objectType" value="com.facishare.fsc.api.service.SharedFileService"/>
    <property name="serverHostProfile" ref="FSCApiHostProfile"/>
  </bean>

  <bean id="createImageDubboService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean">
    <property name="objectType" value="com.facishare.fsc.api.service.CreateImageService"/>
    <property name="serverHostProfile" ref="FSCApiHostProfile"/>
  </bean>

  <bean id="captchaDubboService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean">
    <property name="objectType" value="com.facishare.fsc.api.service.CaptchaService"/>
    <property name="serverHostProfile" ref="FSCApiHostProfile"/>
  </bean>

</beans>