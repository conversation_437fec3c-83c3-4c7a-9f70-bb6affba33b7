package com.facishare.fsc.api.model;

import io.protostuff.Tag;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface CreateFileShareToken {

  class Arg implements Serializable {
    @Tag(1)
    public String ea;
    @Tag(2)
    public int employeeId;
    @Tag(3)
    public List<String> pathList;
    @Tag(4)
    public int expireDay;//有效期
    @Tag(5)
    public String business;
    @Tag(6)
    public int expireMinute;

    @Override
    public String toString() {
      return "Arg{" + "ea='" + ea + '\'' + ", employeeId=" + employeeId + ", path='" + pathList + '\'' + ", expireDay='" + expireDay + '\'' + '}';
    }
  }


  class Result implements Serializable {
    @Tag(1)
    public Map<String, String> tokenMap;

    public Result() {
    }

    public Result(Map<String, String> resultMap) {
      this.tokenMap = resultMap;
    }

    @Override
    public String toString() {
      return "Result{" + "fileIdMap='" + tokenMap + '\'' + '}';
    }
  }
}
