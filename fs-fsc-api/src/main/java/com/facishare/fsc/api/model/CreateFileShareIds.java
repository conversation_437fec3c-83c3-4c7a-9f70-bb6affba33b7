package com.facishare.fsc.api.model;

import io.protostuff.Tag;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by liuq on 2017/1/16.
 */
public interface CreateFileShareIds {
  class Arg implements Serializable {
    @Tag(1)
    public String ea;
    @Tag(2)
    public int employeeId;
    @Tag(3)
    public List<String> pathList;
    @Tag(4)
    public String securityGroup;

    @Override
    public String toString() {
      return "Arg{" + "ea='" + ea + '\'' + ", employeeId=" + employeeId + ", path='" + pathList + '\'' + ", sg='" +
        securityGroup + '\'' + '}';
    }
  }


  class Result implements Serializable {
    @Tag(1)
    public Map<String, String> fileIdMap;

    public Result() {
    }

    public Result(Map<String, String> resultMap) {
      this.fileIdMap = resultMap;
    }

    @Override
    public String toString() {
      return "Result{" + "fileIdMap='" + fileIdMap + '\'' + '}';
    }
  }
}
