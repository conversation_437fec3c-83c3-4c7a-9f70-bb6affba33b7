package com.facishare.fsc.api.model;

import io.protostuff.Tag;

import java.io.Serializable;

/**
 * Created by <PERSON> on 16/6/6.
 */
public interface ValidateCaptcha {
    class Result implements Serializable {
        @Tag(1)
        public boolean success;

        @Override
        public String toString() {
            return "Result{" +
                    "success=" + success +
                    '}';
        }
    }
    class Arg implements  Serializable{
        @Tag(1)
        public String cid;
        @Tag(2)
        public String epxId;
        @Tag(3)
        public String code;

        @Override
        public String toString() {
            return "Arg{" +
                    "cid='" + cid + '\'' +
                    ", epxId='" + epxId + '\'' +
                    ", code='" + code + '\'' +
                    '}';
        }
    }
}
