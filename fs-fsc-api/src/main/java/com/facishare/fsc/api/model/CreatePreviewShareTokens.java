package com.facishare.fsc.api.model;

import io.protostuff.Tag;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface CreatePreviewShareTokens {

  class Arg implements Serializable {
    //企业账号
    @Tag(1)
    public String ea;
    //员工编号
    @Tag(2)
    public int employeeId;
    //path集合
    @Tag(3)
    public List<String> pathList;
    //安全组,网盘业务传 XiaoKeNetDisk
    @Tag(4)
    public String securityGroup;

    @Override
    public String toString() {
      return "Arg{" +
        "ea='" + ea + '\'' +
        ", employeeId=" + employeeId +
        ", path='" + pathList + '\'' +
        '}';
    }
  }

  class Result implements Serializable {
    @Tag(1)
    public Map<String,String> fileIdMap;

    public Result() {
    }

    public Result(Map<String, String> resultMap) {
      this.fileIdMap=resultMap;
    }

    @Override
    public String toString() {
      return "Result{" +
        "fileIdMap='" + fileIdMap + '\'' +
        '}';
    }
  }
}
