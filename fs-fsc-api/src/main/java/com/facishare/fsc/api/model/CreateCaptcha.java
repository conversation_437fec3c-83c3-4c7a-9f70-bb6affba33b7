package com.facishare.fsc.api.model;

import io.protostuff.Tag;

import java.io.Serializable;

/**
 * Created by <PERSON> on 16/6/6.
 */
public interface CreateCaptcha {
  class Result implements Serializable {
    @Tag(1)
    public byte[] data;
  }


  class Arg implements Serializable {
    @Tag(1)
    public String cId;
    @Tag(2)
    public String epxId;
    @Tag(3)
    public String lan;

    @Override
    public String toString() {
      return "Arg{cId='" + cId + "',expId='" + epxId + "',lan='" + lan + "'}";
    }

  }
}
