package com.facishare.fsc.api.model;

import io.protostuff.Tag;

import java.io.Serializable;

/**
 * Created by liuq on 2017/1/16.
 */
public interface CreateFileShareId {
    class Arg implements Serializable {
        @Tag(1)
        public String ea;
        @Tag(2)
        public int employeeId;
        @Tag(3)
        public String path;
        @Tag(4)
        public  String securityGroup;

        @Override
        public String toString() {
            return "Arg{" +
                    "ea='" + ea + '\'' +
                    ", employeeId=" + employeeId +
                    ", path='" + path + '\'' +
                    ", sg='" + securityGroup + '\''+
                    '}';
        }
    }

    class Result implements Serializable {
        @Tag(1)
        public String fileId;

        public Result(String token) {
            this.fileId = token;
        }

        public Result() {
        }

        @Override
        public String toString() {
            return "Result{" +
                    "fileId='" + fileId + '\'' +
                    '}';
        }
    }
}
