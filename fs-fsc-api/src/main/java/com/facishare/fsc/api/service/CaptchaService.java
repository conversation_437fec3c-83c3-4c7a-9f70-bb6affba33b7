package com.facishare.fsc.api.service;


import com.facishare.fsc.api.model.CreateCaptcha;
import com.facishare.fsc.api.model.ValidateCaptcha;

/**
 * Created by <PERSON> on 16/6/6.
 */
public interface CaptchaService {
    /**
     * 生成验证码图片
     * @param arg
     * @return
     */
    CreateCaptcha.Result create(CreateCaptcha.Arg arg);

    /**
     * 验证验证码
     * @param arg
     * @return
     */
    ValidateCaptcha.Result validate(ValidateCaptcha.Arg arg);
}
