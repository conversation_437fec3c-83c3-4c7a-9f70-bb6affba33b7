package com.facishare.fsc.api.model;

import io.protostuff.Tag;

import java.io.Serializable;

/**
 * Created by <PERSON> on 16/7/20.
 */
public interface CreateAccessFileToken {
    class Arg implements Serializable {
        @Tag(1)
        public String EA;
        @Tag(2)
        public int employeeId;
        @Tag(3)
        public String path;
        @Tag(4)
        public String securityGroup;
        @Tag(5)
        public long expireMills;

      @Override
      public String toString() {
        final StringBuffer sb = new StringBuffer("Arg{");
        sb.append("EA='").append(EA).append('\'');
        sb.append(", employeeId=").append(employeeId);
        sb.append(", path='").append(path).append('\'');
        sb.append(", securityGroup='").append(securityGroup).append('\'');
        sb.append(", expireMills=").append(expireMills);
        sb.append('}');
        return sb.toString();
      }
    }

    class Result implements Serializable{
        @Tag(1)
        public String token;

        public Result(String token) {
            this.token = token;
        }

        public Result() {
        }

        @Override
        public String toString() {
            return "Result{" +
                    "token='" + token + '\'' +
                    '}';
        }
    }
}
