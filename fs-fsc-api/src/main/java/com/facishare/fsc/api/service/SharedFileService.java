package com.facishare.fsc.api.service;

import com.facishare.fsc.api.model.BatchGenerateFilePreviewShareToken;
import com.facishare.fsc.api.model.CreateAccessAvatarToken;
import com.facishare.fsc.api.model.CreateAccessFileToken;
import com.facishare.fsc.api.model.CreateFileShareId;
import com.facishare.fsc.api.model.CreateFileShareIds;
import com.facishare.fsc.api.model.CreateFileShareToken;
import com.facishare.fsc.api.model.CreatePreviewShareTokens;

/**
 * Created by <PERSON> on 16/7/4.
 */
public interface SharedFileService {
    CreateAccessAvatarToken.Result createAccessAvatarToken(CreateAccessAvatarToken.Arg arg);
    CreateAccessFileToken.Result createAccessFileToken(CreateAccessFileToken.Arg arg);
    CreateFileShareId.Result createFileShareId(CreateFileShareId.Arg arg) throws Exception;
    CreateFileShareIds.Result createFileShareIds(CreateFileShareIds.Arg arg) throws Exception;
    //批量生成预览token
    CreatePreviewShareTokens.Result createPreviewShareTokens(CreatePreviewShareTokens.Arg arg) throws Exception;

    /**
     * 批量生成文件预览token,支持自定义token有效期
     * @param arg 请求参数 {@link BatchGenerateFilePreviewShareToken.Arg}
     *            business 业务标识
     *            fileInfoList 文件信息列表
     *              .enterpriseAccount 企业账号
     *              .employeeId 员工id
     *              .path 文件路径
     *              .expireTime token有效期 单位秒 取值范围[3600,604800] 默认值3600,小于3600按3600处理,大于604800按604800处理
     *              .securityGroup 安全组(可选 网盘文件必填)
     * @return {@link BatchGenerateFilePreviewShareToken.Result}
     *           shareTokens token列表 <path,token> 文件路径和token的映射
     * @throws Exception 异常
     */
    BatchGenerateFilePreviewShareToken.Result batchGenerateFilePreviewShareToken(BatchGenerateFilePreviewShareToken.Arg arg) throws Exception;
    @Deprecated
    CreateFileShareToken.Result createFileShareTokens(CreateFileShareToken.Arg arg) throws Exception;
    CreateFileShareToken.Result createFileShareTokensV2(CreateFileShareToken.Arg arg) throws Exception;

}
