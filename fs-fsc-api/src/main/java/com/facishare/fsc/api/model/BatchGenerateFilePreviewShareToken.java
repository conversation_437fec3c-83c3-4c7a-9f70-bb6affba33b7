package com.facishare.fsc.api.model;

import io.protostuff.Tag;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface BatchGenerateFilePreviewShareToken {
  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Arg implements Serializable {

    @Tag(1)
    private String business;

    @Tag(2)
    private List<FileInfo> fileInfoList;
  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result implements Serializable{
    @Tag(1)
    public Map<String,String> shareTokens;

    public static Result of(Map<String,String> shareTokens){
      return new Result(shareTokens);
    }
  }
}
