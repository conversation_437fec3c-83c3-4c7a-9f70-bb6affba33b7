package com.facishare.fsc.api.model;

import io.protostuff.Tag;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class FileInfo implements Serializable {

  @Tag(1)
  private String enterpriseAccount;
  @Tag(2)
  private int employeeId;

  @Tag(3)
  private String path;
  @Tag(4)
  private String securityGroup;
  @Tag(5)
  private long expireTime;
}
