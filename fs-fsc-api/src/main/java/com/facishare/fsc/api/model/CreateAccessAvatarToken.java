package com.facishare.fsc.api.model;

import io.protostuff.Tag;

import java.io.Serializable;

/**
 * Created by <PERSON> on 16/7/4.
 */
public interface CreateAccessAvatarToken {
    class Arg implements Serializable{
        @Tag(1)
        public String EA;
        @Tag(2)
        public int employeeId;
        @Tag(3)
        public String path;

        @Override
        public String toString() {
            return "Arg{" +
                    "EA='" + EA + '\'' +
                    ", employeeId=" + employeeId +
                    ", path='" + path + '\'' +
                    '}';
        }
    }

    class Result implements Serializable{
        @Tag(1)
        public String token;

        public Result(String token) {
            this.token = token;
        }

        public Result() {
        }

        @Override
        public String toString() {
            return "Result{" +
                    "token='" + token + '\'' +
                    '}';
        }
    }
}
