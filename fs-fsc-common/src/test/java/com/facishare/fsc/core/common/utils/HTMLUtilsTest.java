package com.facishare.fsc.core.common.utils;

import com.facishare.fsc.common.utils.HTMLUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.Set;

/**
 * Created by <PERSON> on 16/5/21.
 */
public class HTMLUtilsTest {
    @Test
    public  void testGetRichTextImages() {
        String content="<p><img type=\\\"l\\\" src=\\\"/FSC/EM/File/ViewTempImg?TempFileName=TN_f7e781f614194b3589d74964c1c0632c&TempFileExt=jpg\\\" style=\\\"max-width:450px;width: 100%;\\\"/></p><p><img type=\\\"l\\\" src=\\\"/FSC/EM/File/ViewTempImg?TempFileName=TN_e99fbf2adcf941acbb1660dd913b0028&TempFileExt=jpg\\\" style=\\\"max-width:450px;width: 100%;\\\"/>公司立志实现三大宗旨：提高农业从业者的劳动价值；为城市人提供真正有能量的原生态农产品；减少污染，保护环境，珍爱地球。<img type=\\\"l\\\" src=\\\"/FSC/EM/File/ViewTempImg?TempFileName=TN_29b6ad8a668a4f7785edeba81d598788&TempFileExt=png\\\" style=\\\"max-width:450px;width: 100%;\\\"/></p><p>经常食用有机食品，可以减少这些原因造成的疾病，现在西方流行的食物疗方法，应用的就是食用有机食品，逐渐减少人体内的>毒素积累，起到治疗的效果。<br/><br/>有机农业生产活动有利于生产者的健康，这是因为常规的农业生产中大量使用了高毒甚至剧毒的化学农药，生产者在使用化学农药的时候，由于不了解正确使用的方法，而经常发生农药中毒现象，严重者甚至付出生命。有机农业生产者则避免了这些不利影响。<img type=\\\"l\\\" src=\\\"/FSC/EM/File/ViewTempImg?TempFileName=TN_e379d1ef5d564515ba12214ac624009f&TempFileExt=jpg\\\" style=\\\"max-width:450px;width: 100%;\\\"/>、私家菜园配送具有时间短，无仓储过程的特点，最大限度的保证蔬菜的新鲜度和安全性。&nbsp;2、按照农场—公司—服务站—户主进行统一编码\n" +
                "\" +\n" +
                "                \"，凌晨4:00-6:00采摘蔬菜，7点从农场基地出发，8:30前送达中转地，基地除了打印配送框里的配送单外，还需打印一式三份的服务站配送单，即以服务站为单位，打印一份汇总配送单，用于中转和服务站签收；&nbsp;3、中转地核对签收服务站配送单后，配送车辆按线路、按服务站装车，同一服务站同一车>，同一线路同一车，近的放外面，远的放里面，9:00前出发；&nbsp;4、11点之前配送至服务站处，服务站签收服务站配送单后，应在客户要求的时间前将对应采摘的蔬菜配送至客户家里，客户要求自提的服务站负责保管至客户取货。同时要求客户收到蔬菜后核对配送单并进行签收，签收单在业务回访或者下>次送货时回收到合心农公司；&nbsp;5、服务站需负责并保证配送筐的回收，在每次送货时需将上次的配送筐从客户手中收回，并在下次送货车到时如数返回给合心农公司，并做好交接工作，具体见公司《服务站管理规定》，合心农公司需及时将配送框返回给农场。&nbsp;<img type=\\\\\\\"l\\\\\\\" src=\\\\\\\"/FSC/EM/File/ViewTempImg?TempFileName=TN_bbceaad8325e44859fb51a4ab17f0e91&TempFileExt=jpg\\\\\\\" style=\\\\\\\"max-width:450px;width: 100%;\\\\\\\"/></p><p>近日,合心农私家菜园又迎来新的企业合作伙伴。继广州日报、广东电视台公共频道等诸多企业在合心农拥有私家菜园之后，以饮食养生为主题的高端餐厅锅里壮，>秉承速度、激情、安全的宝马和凯迪拉克汽车也正式成为合心农私家菜园的战略合作伙伴。</p><p>&nbsp; &nbsp; 在食品安全问题日益突出的今天，人们对健康有机食品的需求越来越大，在这个大环境下，合心农私家菜园应运而生。“传承中国传统农业，让消费者吃上绿色健康农产品，让农业从业者尊严而幸\\n\" +\n" +
                "                \"福的生活”是合心农的企业使命，“源本自然”是合心农的运营理念。也正是这样的企业使命和运营理念吸引了大批企业以及私人客户成为我们的合作伙伴。</p><p><br/></p>";
       Set<String> urls= HTMLUtils.getRichTextImages(content);
        System.out.println(urls);
        content = HTMLUtils.filterHtml(content);
        Set<String> images = HTMLUtils.getRichTextImages(content);
        String newSrc = null;
        for (String src : images) {
            String tempSrc = src + "&";
            String tempFileName = HTMLUtils.getContent(tempSrc, "TempFileName=", "&");
            String tempFileExt = HTMLUtils.getContent(tempSrc, "TempFileExt=", "&");
            tempFileExt=tempFileExt.replace("\\","");
            newSrc=tempFileName+"."+tempFileExt;
            content = content.replaceAll(src, newSrc);
        }
        System.out.println(content);
    }
    @Test
    public void testHtmlFilter(){
        //过滤 script
        Assert.assertEquals(HTMLUtils.filterHtml("<html><script/></html>"),"<html></html>");
        Assert.assertEquals(HTMLUtils.filterHtml("<html><script></script></html>"),"<html></html>");
        //过滤 <iframe> 标签
        Assert.assertEquals(HTMLUtils.filterHtml("<html><iframe/></html>"),"<html></html>");
        Assert.assertEquals(HTMLUtils.filterHtml("<html><iframe></iframe></html>"),"<html></html>");
        //过滤 on: 的事件
        //过滤on 带单引号的 过滤on  带双引号的 过滤on 不带有引号的
        Assert.assertEquals(HTMLUtils.filterHtml("<html onClick='xx();'><iframe/></html>").replace(" ",""),"<html></html>".trim());
        Assert.assertEquals(HTMLUtils.filterHtml("<html onClick=\"xx();\"><iframe/></html>").replace(" ",""),"<html></html>".trim());
        Assert.assertEquals(HTMLUtils.filterHtml("<html onClick=xx();><iframe/></html>").replace(" ",""),"<html></html>".trim());

        //过滤 javascript: 的事件
        Assert.assertEquals(HTMLUtils.filterHtml("<html onClick='javascript;;'><iframe/></html>").replace(" ",""),"<html></html>".trim());
    }

}
