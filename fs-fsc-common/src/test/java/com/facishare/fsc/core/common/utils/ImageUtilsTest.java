package com.facishare.fsc.core.common.utils;

import com.facishare.fsc.common.utils.ImageUtils;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.util.Base64Utils;
import java.io.FileInputStream;

/**
 * Created by <PERSON> on 16/5/14.
 */
public class ImageUtilsTest {
    @Test
    public void testImageUtils()throws Exception{
        byte[] data= ImageUtils.createTextImage("读取文档预览失败",200,500);
        Assert.assertNotNull(data);
    }
    @Test
    public void testFromBase64()throws Exception{
        String content=Base64Utils.encodeToString(IOUtils.toByteArray(new FileInputStream(ImageUtilsTest.class.getResource("/IMG_0200.jpg").getFile())));

        byte[] data=ImageUtils.fromBase64ToBytes("data:image/jpg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0a\\n\\n↵HBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIy\\n\\n↵MjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAABAAEDASIA\\n\\n↵AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA\\n\\n↵AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3\\n\\n↵ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm\\n\\n↵p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA\\n\\n↵AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx\\n\\n↵BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK\\n\\n↵U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3\\n\\n↵uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD3+iii\\n\\n↵gD//2Q==");
        Assert.assertNotNull(data);
    }
}
