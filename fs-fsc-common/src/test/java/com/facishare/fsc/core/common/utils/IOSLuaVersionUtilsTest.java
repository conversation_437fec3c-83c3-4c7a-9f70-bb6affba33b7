package com.facishare.fsc.core.common.utils;

import com.facishare.fsc.common.utils.FileHelper;
import com.facishare.fsc.common.utils.IOSLuaVersionUtils;
import org.apache.commons.codec.Resources;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;

/**
 * Created by <PERSON> on 16/5/17.
 */
public class IOSLuaVersionUtilsTest {
    @Test
    public void testNeedLuaFileUpdate(){
        String iOSVersionPath=IOSLuaVersionUtils.class.getResource("/").getPath(), appVersion="100103",  luaVersion="1";
        String path=IOSLuaVersionUtils.needLuaFileUpdate(iOSVersionPath,appVersion,luaVersion);
        Assert.assertNotNull(path);
        System.out.println(path);
    }
    @Test
    public void createLuaMd5HashCode() throws Exception {
        byte[] data=IOUtils.toByteArray(Resources.getInputStream("Version.xml"));
        System.out.println(FileHelper.getMD5(data).toUpperCase());
    }

}
