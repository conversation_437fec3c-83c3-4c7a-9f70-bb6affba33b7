package com.facishare.fsc.core.common.utils;

import com.facishare.fsc.common.utils.ValidateCodeUtils;
import org.junit.Assert;
import org.junit.Test;
import java.io.File;


/**
 * Created by <PERSON> on 16/5/3.
 */
public class ValidateCodeUtilsTest{
    @Test
    public void testValidateBytes()throws Exception{
        byte[] datas= ValidateCodeUtils.createCodeBytes(28,25,1,1,"15LN");
        String path = ValidateCodeUtilsTest.class.getResource("/").getFile()+ File.separator+"Code.jpg";
        Assert.assertTrue(path.contains("Code.jpg"));
    }

    @Test
    public void testCreateValidate(){
        Assert.assertEquals(ValidateCodeUtils.createCode(10).length(),10);
    }
}
