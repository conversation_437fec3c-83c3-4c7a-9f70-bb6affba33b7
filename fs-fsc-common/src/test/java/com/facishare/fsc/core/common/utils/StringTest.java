package com.facishare.fsc.core.common.utils;

import org.junit.Test;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by <PERSON> on 16/5/17.
 */
public class StringTest {
    @Test
    public void test(){
        int i="sldkf.sdkf".split("\\.").length;
        System.out.println(i);
    }
    @Test
    public void testPhone(){
        Pattern pattern = Pattern.compile("^((\\+)?86 |((\\+)?86)?)0?1[34578]\\d{9}$");
        // 现在创建 matcher 对象
        Matcher m = pattern.matcher("15110130731");
        System.out.println(m.find());
    }
    @Test
    public void testURLCode()throws Exception{
        //        aHR0cHM6Ly93d3cuZnhpYW9rZS5jb20vbW9iL3F4L3FyY29kZS5odG1sP3Rva2VuPTkyYjBlZmUwMmJkYTRjYmNhZWUzZTEyODY5ZDdiMjY3
        String qrtUrl = URLDecoder.decode(new String(
          Base64.getDecoder().decode("aHR0cHM6Ly93d3cuZnhpYW9rZS5jb20vbW9iL3F4L3FyY29kZS5odG1sP3Rva2VuPTkyYjBlZmUwMmJkYTRjYmNhZWUzZTEyODY5ZDdiMjY3")), "UTF-8");
        String qrt = qrtUrl.split("\\?")[1].split("=")[1];
        System.out.println(qrt);
    }
    @Test
    public void testHeaderIcon(){
        String ts = "2vffZGnBZ88F5PCc54U4lYCZcbdwHc5aSmEEG6wtBHNRwCn8voRWPlCKhGQV268EUNXZcDzM8fIspGCC6LrXcIBAgFA/DKS6v0xJmzZdn6k=1.jpg";
        int idx = ts.lastIndexOf('=');
        String encodedString = ts.substring(0, idx + 1);
        System.out.println("encodedString:" + encodedString);
        String decryResult = aesDecrypt(encodedString);
        System.out.println("decryResult:" + decryResult);
        String[] sts = decryResult.split(":");

        String ea = sts[0];
        String path = sts[1] + ts.substring(ts.lastIndexOf('.'));
        System.out.println(ea + "---" + path);
    }
    private String aesDecrypt(String param) {
        String openAESKey="jIEAR7B6dYm69dix";
        String openAESIV="QOmjFKz3My0XieYF";
        try {
            byte[] bKey = openAESKey.getBytes(StandardCharsets.UTF_8);
            byte[] bIV = openAESIV.getBytes(StandardCharsets.UTF_8);
            String encodedString = param.replace(" ", "+");
            byte[] byteArray = Base64.getDecoder().decode(encodedString);
            SecretKeySpec secretKey = new SecretKeySpec(bKey, "AES");
            Cipher c = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec iv = new IvParameterSpec(bIV);
            c.init(Cipher.DECRYPT_MODE, secretKey, iv);
            byte[] resultByte = c.doFinal(byteArray);
            String result = new String(resultByte, StandardCharsets.UTF_8);
            return result.substring(10, result.length() - 1);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
}
