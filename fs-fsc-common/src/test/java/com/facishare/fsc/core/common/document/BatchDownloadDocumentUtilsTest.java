package com.facishare.fsc.core.common.document;

import com.facishare.fsc.common.document.BatchDownloadDocumentUtils;
import com.facishare.fsc.common.document.BatchFileDocument;
import com.facishare.fsc.common.document.BatchFileEntity;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;

/**
 * Created by <PERSON> on 16/5/20.
 */
public class BatchDownloadDocumentUtilsTest {
    @Test
    public void testGetSingleFileDocumentStr(){
        List<BatchFileEntity> batchFileEntityList= Lists.newArrayList();
        for (int i = 0; i <2 ; i++) {
            batchFileEntityList.add(new BatchFileEntity("fileName"+i,"N_FilePath"+i));
        }
        String documentStr= BatchDownloadDocumentUtils.getSingleFileDocumentStr(batchFileEntityList);
        System.out.println(documentStr);
    }
    @Test
    public void testGetDirectoryStr(){

        BatchFileDocument document=new BatchFileDocument();
        BatchFileDocument batchFileDocument=new BatchFileDocument();
        document.setChilds(Lists.newArrayList(batchFileDocument));
        batchFileDocument.setDirectoryName("目录一");
        List<BatchFileEntity> batchFileEntityList= Lists.newArrayList();
        for (int i = 0; i <10 ; i++) {
            batchFileEntityList.add(new BatchFileEntity("fileName"+i,"N_FilePath"+i));
        }
        batchFileDocument.setFiles(batchFileEntityList);
        document.setFiles(batchFileEntityList);
        System.out.println(BatchDownloadDocumentUtils.getFileDocumentStr(document));
//        System.out.println(new GsonBuilder().setPrettyPrinting().create().toJson(document));
    }
}
