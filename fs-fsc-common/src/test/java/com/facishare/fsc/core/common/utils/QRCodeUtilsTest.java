package com.facishare.fsc.core.common.utils;

import com.facishare.fsc.common.utils.QRCodeUtils;
import com.google.common.collect.Maps;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.net.URLDecoder;
import java.util.Base64;
import java.util.Map;

/**
 * Created by <PERSON> on 16/5/14.
 */
public class QRCodeUtilsTest {
    @Test
    public void createQRCode() throws Exception{
        Map<EncodeHintType, Integer> hints = Maps.newHashMap();
        hints.put(EncodeHintType.MARGIN,0);
        byte[] data = QRCodeUtils.encodeToQRCode("abcdeffff", BarcodeFormat.QR_CODE, 220,220,hints);
        Assert.assertNotNull(data);
    }
    @Test
    public void decodeQRCodeUrl()throws Exception{
        String qrString="aHR0cHM6Ly93d3cuZnhpYW9rZS5jb20vbW9iL3F4L3FyY29kZS5odG1sP3Rva2VuPWZlNmI0OTI0NjZlMjQxZDE4OGVhZmEzYTgyNTY1YjQy";
        System.out.println(URLDecoder.decode(new String(Base64.getDecoder().decode(qrString)), "UTF-8"));
    }
}
