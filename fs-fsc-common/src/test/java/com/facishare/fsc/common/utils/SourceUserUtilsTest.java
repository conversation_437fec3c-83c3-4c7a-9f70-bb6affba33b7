package com.facishare.fsc.common.utils;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for SourceUserUtils
 */
public class SourceUserUtilsTest {

    @Test
    public void testGetEmployeeIdWithValidFormat() {
        assertEquals(Integer.valueOf(123), SourceUserUtils.getEmployeeId("company.123"));
        assertEquals(Integer.valueOf(456), SourceUserUtils.getEmployeeId("test_company.456"));
        assertEquals(Integer.valueOf(789), SourceUserUtils.getEmployeeId("enterprise.789"));
    }

    @Test
    public void testGetEmployeeIdWithMultipleDots() {
        assertEquals(Integer.valueOf(123), SourceUserUtils.getEmployeeId("company.sub.123"));
        assertEquals(Integer.valueOf(456), SourceUserUtils.getEmployeeId("a.b.c.456"));
        assertEquals(Integer.valueOf(789), SourceUserUtils.getEmployeeId("test.company.name.789"));
    }

    @Test
    public void testGetEmployeeIdWithNoDot() {
        assertEquals(Integer.valueOf(123), SourceUserUtils.getEmployeeId("123"));
        assertEquals(Integer.valueOf(456), SourceUserUtils.getEmployeeId("456"));
    }

    @Test
    public void testGetEmployeeIdWithNullInput() {
        assertNull(SourceUserUtils.getEmployeeId(null));
    }

    @Test
    public void testGetEmployeeIdWithEmptyString() {
        assertNull(SourceUserUtils.getEmployeeId(""));
    }

    @Test
    public void testGetEmployeeIdWithWhitespaceString() {
        assertNull(SourceUserUtils.getEmployeeId("   "));
    }

    @Test
    public void testGetEmployeeIdWithInvalidEmployeeId() {
        assertNull(SourceUserUtils.getEmployeeId("company.abc"));
        assertEquals(Integer.valueOf(34), SourceUserUtils.getEmployeeId("company.12.34")); // lastIndexOf('.') gets "34"
        assertNull(SourceUserUtils.getEmployeeId("company."));
        assertNull(SourceUserUtils.getEmployeeId("company.12a"));
    }

    @Test
    public void testGetEmployeeIdWithNegativeNumber() {
        assertEquals(Integer.valueOf(-123), SourceUserUtils.getEmployeeId("company.-123"));
        assertEquals(Integer.valueOf(-456), SourceUserUtils.getEmployeeId("test.-456"));
    }

    @Test
    public void testGetEmployeeIdWithPositiveSign() {
        assertEquals(Integer.valueOf(123), SourceUserUtils.getEmployeeId("company.+123"));
        assertEquals(Integer.valueOf(456), SourceUserUtils.getEmployeeId("test.+456"));
    }

    @Test
    public void testGetEmployeeIdWithZero() {
        assertEquals(Integer.valueOf(0), SourceUserUtils.getEmployeeId("company.0"));
        assertEquals(Integer.valueOf(0), SourceUserUtils.getEmployeeId("test.+0"));
        assertEquals(Integer.valueOf(0), SourceUserUtils.getEmployeeId("test.-0"));
    }

    @Test
    public void testGetEmployeeIdWithLargeNumber() {
        assertEquals(Integer.valueOf(*********), SourceUserUtils.getEmployeeId("company.*********"));
        assertEquals(Integer.valueOf(Integer.MAX_VALUE), SourceUserUtils.getEmployeeId("company." + Integer.MAX_VALUE));
    }

    @Test
    public void testGetEmployeeIdWithMinValue() {
        assertEquals(Integer.valueOf(Integer.MIN_VALUE), SourceUserUtils.getEmployeeId("company." + Integer.MIN_VALUE));
    }

    @Test
    public void testGetEmployeeIdWithLeadingZeros() {
        assertEquals(Integer.valueOf(123), SourceUserUtils.getEmployeeId("company.00123"));
        assertEquals(Integer.valueOf(456), SourceUserUtils.getEmployeeId("company.000456"));
    }

    @Test
    public void testGetEmployeeIdWithSpecialCharactersInCompany() {
        assertEquals(Integer.valueOf(123), SourceUserUtils.getEmployeeId("company@test.123"));
        assertEquals(Integer.valueOf(456), SourceUserUtils.getEmployeeId("company_test.456"));
        assertEquals(Integer.valueOf(789), SourceUserUtils.getEmployeeId("company-test.789"));
    }

    @Test
    public void testGetEmployeeIdWithOnlyDot() {
        assertNull(SourceUserUtils.getEmployeeId("."));
    }

    @Test
    public void testGetEmployeeIdWithDotAtEnd() {
        assertNull(SourceUserUtils.getEmployeeId("company."));
    }

    @Test
    public void testGetEmployeeIdWithDotAtStart() {
        // When startSub = lastIndexOf('.') = 0, startSub > 0 is false, so it uses the whole string ".123"
        // ".123" doesn't match the integer pattern, so returns null
        assertNull(SourceUserUtils.getEmployeeId(".123"));
    }

    @Test
    public void testGetEmployeeIdWithMultipleConsecutiveDots() {
        assertEquals(Integer.valueOf(123), SourceUserUtils.getEmployeeId("company..123"));
        assertEquals(Integer.valueOf(456), SourceUserUtils.getEmployeeId("company...456"));
    }

    @Test
    public void testGetEmployeeIdWithFloatingPointNumber() {
        assertEquals(Integer.valueOf(34), SourceUserUtils.getEmployeeId("company.12.34")); // lastIndexOf('.') gets "34"
        assertEquals(Integer.valueOf(0), SourceUserUtils.getEmployeeId("company.123.0")); // lastIndexOf('.') gets "0"
    }

    @Test
    public void testGetEmployeeIdWithScientificNotation() {
        assertNull(SourceUserUtils.getEmployeeId("company.1e5"));
        assertNull(SourceUserUtils.getEmployeeId("company.1E10"));
    }

    @Test
    public void testGetEmployeeIdWithHexNumber() {
        assertNull(SourceUserUtils.getEmployeeId("company.0x123"));
        assertNull(SourceUserUtils.getEmployeeId("company.0xFF"));
    }

    @Test
    public void testGetEmployeeIdWithAlphanumeric() {
        assertNull(SourceUserUtils.getEmployeeId("company.123abc"));
        assertNull(SourceUserUtils.getEmployeeId("company.abc123"));
    }

    @Test
    public void testGetEmployeeIdWithSpaces() {
        assertNull(SourceUserUtils.getEmployeeId("company. 123"));
        assertNull(SourceUserUtils.getEmployeeId("company.123 "));
        assertNull(SourceUserUtils.getEmployeeId("company. 123 "));
    }

    @Test
    public void testGetEmployeeIdEdgeCases() {
        // Test with very long company name
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("a");
        }
        String longCompanyName = sb.toString();
        assertEquals(Integer.valueOf(123), SourceUserUtils.getEmployeeId(longCompanyName + ".123"));

        // Test with empty company name - when startSub = 0, it uses whole string which doesn't match integer pattern
        assertNull(SourceUserUtils.getEmployeeId(".123"));
    }
}
