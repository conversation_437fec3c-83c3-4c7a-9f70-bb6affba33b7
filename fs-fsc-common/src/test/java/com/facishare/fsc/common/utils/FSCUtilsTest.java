package com.facishare.fsc.common.utils;

import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.junit.Assert.fail;

import com.github.autoconf.base.ProcessInfo;
import com.github.autoconf.helper.ConfigHelper;
import org.mockito.MockedStatic;

/**
 * Test class for FSCUtils
 */
public class FSCUtilsTest {

    private MockedStatic<ConfigHelper> configHelperMock;

    @Before
    public void setUp() {
        configHelperMock = mockStatic(ConfigHelper.class);
    }

    @After
    public void tearDown() {
        if (configHelperMock != null) {
            configHelperMock.close();
        }
    }

    @Test
    public void testGetNativeIpPortWithValidIpAndPort() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("*************");
        processInfo.setPort("8080");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertEquals("*************:8080", result);
    }

    @Test
    public void testGetNativeIpPortWithNullPort() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("*************");
        processInfo.setPort(null);

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertEquals("*************:80", result);
    }

    @Test
    public void testGetNativeIpPortWithEmptyPort() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("*************");
        processInfo.setPort("");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertEquals("*************:80", result);
    }

    @Test
    public void testGetNativeIpPortWithWhitespacePort() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("*************");
        processInfo.setPort("   ");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        // The actual implementation doesn't trim whitespace, it only checks isNullOrEmpty
        assertEquals("*************:   ", result);
    }

    @Test
    public void testGetNativeIpPortWithLocalhostIp() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("127.0.0.1");
        processInfo.setPort("9090");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertEquals("127.0.0.1:9090", result);
    }

    @Test
    public void testGetNativeIpPortWithNullIp() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp(null);
        processInfo.setPort("8080");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        // This will throw NullPointerException because Joiner.on(":").join() doesn't handle null values
        try {
            FSCUtils.getNativeIpPort();
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // Expected behavior
        }
    }

    @Test
    public void testGetNativeIpPortWithEmptyIp() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("");
        processInfo.setPort("8080");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertEquals(":8080", result);
    }

    @Test
    public void testGetNativeIpPortWithBothNullValues() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp(null);
        processInfo.setPort(null);

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        // This will throw NullPointerException because Joiner.on(":").join() doesn't handle null IP
        try {
            FSCUtils.getNativeIpPort();
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // Expected behavior
        }
    }

    @Test
    public void testGetNativeIpPortWithBothEmptyValues() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("");
        processInfo.setPort("");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertEquals(":80", result);
    }

    @Test
    public void testGetNativeIpPortWithSpecialCharactersInIp() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("::1"); // IPv6 localhost
        processInfo.setPort("8080");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertEquals("::1:8080", result);
    }

    @Test
    public void testGetNativeIpPortWithHighPortNumber() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("*************");
        processInfo.setPort("65535");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertEquals("*************:65535", result);
    }

    @Test
    public void testGetNativeIpPortWithLowPortNumber() {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("*************");
        processInfo.setPort("1");

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertEquals("*************:1", result);
    }

    @Test
    public void testDefaultPortConstant() {
        // Test that the default port is 80
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setIp("*************");
        processInfo.setPort(null);

        configHelperMock.when(ConfigHelper::getProcessInfo).thenReturn(processInfo);

        String result = FSCUtils.getNativeIpPort();
        assertTrue(result.endsWith(":80"));
    }
}
