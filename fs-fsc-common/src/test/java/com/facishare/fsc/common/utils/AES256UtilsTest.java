package com.facishare.fsc.common.utils;

import org.junit.Assert;
import org.junit.Test;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.util.Base64;

/**
 * Created by <PERSON> on 16/7/4.
 */
public class AES256UtilsTest {

    @Test
    public void testEncode() throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(256);
        SecretKey aesKey = kgen.generateKey();
        System.out.println(Base64.getEncoder().encodeToString(aesKey.getEncoded()));
        String path = AES256Utils.encode("fssdetest:1099:201504_20_1418842d-2570-4387-a1fe-52071b624c6e2.jpg");
        System.out.println(path);
        Assert.assertEquals("A2F14E4E0E49AB93C57FDA87D4AB68CDEFA1B727B317EDA77C71F3509A23541351557CFDD2DAF89E5582A12F38002D837C4ECE0E659F7EA76EE8AB2FFCFFC71CD105B0633EB172F363FB957364A0CF50", path);
        String target = AES256Utils.decode("AA20252628D726274A3FEBCF14F13883D2200CA83786A8F311812BFCD946F660CEB0C31E1377486B04C19FD44E80C96092E93A555DA96EE09B1FAD76EC9F895B21080B7F651785E0EA7E8C0A07DF0505");
        System.out.println(target);
        Assert.assertEquals("55334:0:A_201708_22_aba59ab2a7d24eceb6d7ab3e397427cf.docx:-:1503460021601", target);
    }

}