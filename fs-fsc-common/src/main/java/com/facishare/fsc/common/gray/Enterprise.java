package com.facishare.fsc.common.gray;

import java.util.List;

/**
 * Created by <PERSON> on 16/5/27.
 */
public class Enterprise {
    private String ea;
    private List<String> employees;

    public Enterprise() {
    }

    public Enterprise(String ea, List<String> employees) {
        this.ea = ea;
        this.employees = employees;
    }

    public String getEa() {
        return ea;
    }

    public void setEa(String ea) {
        this.ea = ea;
    }

    public List<String> getEmployees() {
        return employees;
    }

    public void setEmployees(List<String> employees) {
        this.employees = employees;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Enterprise that = (Enterprise) o;

        return ea != null ? ea.equals(that.ea) : that.ea == null;

    }

    @Override
    public int hashCode() {
        return ea != null ? ea.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "Enterprise{" +
                "ea='" + ea + '\'' +
                ", employees=" + employees +
                '}';
    }
}
