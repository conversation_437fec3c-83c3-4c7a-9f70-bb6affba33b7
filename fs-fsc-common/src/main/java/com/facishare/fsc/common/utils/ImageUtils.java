package com.facishare.fsc.common.utils;

import org.springframework.util.Base64Utils;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Iterator;

/**
 * Created by <PERSON> on 16/5/14.
 */
public class ImageUtils {

    public static byte[] createTextImage(String text, int width, int height) {
        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = bufferedImage.createGraphics();
        g.setColor(Color.WHITE);
        g.fillRect(0,0,width,height);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        Font font = new Font("微软雅黑", Font.BOLD, 24);
        FontMetrics fm = g.getFontMetrics(font);
        float strwidth = fm.stringWidth(text);
        float strHeight = fm.getHeight();
        float xPosOfWm = (width - strwidth) / 2;
        float yPosOfWm = (height - strHeight) / 2 + strHeight;
        g.setColor(Color.BLACK);
        g.setFont(font);
        g.drawString(text, xPosOfWm, yPosOfWm);
        g.dispose();
        return getBufferedImage(bufferedImage);

    }

    public static byte[] getBufferedImage(BufferedImage bufferedImage) {

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageOutputStream ios = ImageIO.createImageOutputStream(baos);
            Iterator<ImageWriter> iter = ImageIO.getImageWritersByFormatName("JPG");
            ImageWriter writer = iter.next();
            ImageWriteParam iwp = writer.getDefaultWriteParam();
            iwp.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            iwp.setCompressionQuality(1f);
            writer.setOutput(ios);
            writer.write(null, new IIOImage(bufferedImage, null, null), iwp);
            writer.dispose();
            return baos.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    public static BufferedImage getBufferedImage(byte[] bytes) {
        try {
            return ImageIO.read(new ByteArrayInputStream(bytes));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static byte[] fromBase64ToBytes(String content){
        String filterStr = "base64,";
        int filterIndex = content.indexOf(filterStr);
//        桌面剪粘图 用到  ,其base64中有了这两种噪音,在这里进行降噪处理
        String imageContent = content.substring(filterIndex + filterStr.length()).replaceAll("\\\\n","").replaceAll("\\n","").replaceAll("↵","").replace("\\\\r","").replace("\\r","");
        return Base64Utils.decodeFromString(imageContent);
    }
}
