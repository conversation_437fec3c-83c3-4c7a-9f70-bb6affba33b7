package com.facishare.fsc.common.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonIOException;
import com.google.gson.JsonSyntaxException;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * Created by <PERSON> on 16/5/9.
 */
public final class JsonUtils {
    private static final Gson GSON = new GsonBuilder().create();

    private JsonUtils() {
    }

    public static String toJson(Object src) {
        return GSON.toJson(src);
    }

    public static byte[] toJsonByte(Object src) {
        try {
            String json = GSON.toJson(src);
            return json.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String toJson(Object src, Type typeOfSrc) {
        return GSON.toJson(src, typeOfSrc);
    }

    public static <T> T fromJson(String json, Class<T> classOfT) throws JsonSyntaxException, JsonIOException {
        return GSON.fromJson(json, classOfT);
    }

    public static <T> T fromJson(String json, Type typeOfT) throws JsonSyntaxException {
        return GSON.fromJson(json, typeOfT);
    }
}
