package com.facishare.fsc.common.utils;


import java.awt.*;
import java.util.List;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Hashtable;
import java.util.Map;
import java.util.Random;

import javax.imageio.ImageIO;

import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.oned.Code128Writer;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * Created by Aaron on 16/5/3.
 */
public class QRCodeUtils {
  /**
   * 生成QRCode二维码<br>
   * 在编码时需要将com.google.zxing.qrcode.encoder.Encoder.java中的<br>
   * static final String DEFAULT_BYTE_MODE_ENCODING = "ISO8859-1";<br>
   * 修改为UTF-8，否则中文编译后解析不了<br>
   */
  public static byte[] encodeToQRCode(String contents,
                                      BarcodeFormat format,
                                      int width,
                                      int height,
                                      Map<EncodeHintType, ?> hints) {
    try {
      //消除乱码
      contents = new String(contents.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
      BitMatrix bitMatrix = new MultiFormatWriter().encode(contents, format, width, height, hints);
      //bitMatrix = deleteWhite(bitMatrix);
      return transferTobytes(bitMatrix);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 生成Bar code
   * 在编码时需要将com.google.zxing.qrcode.encoder.Encoder.java中的
   * static final String DEFAULT_BYTE_MODE_ENCODING = "ISO8859-1";
   * 修改为UTF-8，否则中文编译后解析不了
   */
  public static byte[] encodeToBarCode(String contents,
                                       BarcodeFormat format,
                                       int width,
                                       int height,
                                       int fontSize,
                                       String number,
                                       Map<EncodeHintType, ?> hints) {
    try {
      //消除乱码
      contents = new String(contents.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
      BitMatrix bitMatrix = new MultiFormatWriter().encode(contents, format, width, height, hints);
      //bitMatrix = deleteWhite(bitMatrix);
      return transferBarCodeToBytes(bitMatrix, fontSize, number);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 按照需要的二维码类型生成二维码
   *
   * @param contents  内容
   * @param codeModel 二维码类型  QRCode: 二维码  BarCode : 条形码
   * @return 码
   */
  public static byte[] encodeByCodeModel(String contents, String size, String codeModel) {

    if ("BarCode".equals(codeModel)) {
      if (!judgeContentSize(contents)) {
        throw new RuntimeException("The length of the barcode content is too long");
      }
      Pair<Integer, Integer> barCodeSize = getBarCodeSize(size); // 解析宽高
      int width = barCodeSize.getLeft();
      int height = barCodeSize.getRight();
      return encodeToCodeWithOutNumber(contents, width, height); // 默认不需要
    } else if ("QRCode".equals(codeModel)) {
      Pair<Integer, Integer> qrCodeSize = getQRCodeSize(size);
      int width = qrCodeSize.getLeft();
      int height = qrCodeSize.getRight();
      return encodeToQRCode(contents, width, height);
    } else {
      throw new RuntimeException("Unsupported code types: " + codeModel);
    }
  }

  /**
   * 限制一维码的内容，限制大小为100个字符
   *
   * @param content 内容
   * @return 是否符合
   */
  private static boolean judgeContentSize(String content) {
    return !content.isEmpty() && content.length() < 100;
  }

  /**
   * 获取条形码大小
   *
   * @param wh 宽高
   * @return 宽高
   */
  public static Pair<Integer, Integer> getBarCodeSize(String wh) {
    Pair<Integer, Integer> sizePari = Pair.of(450, 150);
    if (!Strings.isNullOrEmpty(wh) && wh.contains("*")) {
      List<String> strings = Splitter.on("*").splitToList(wh);
      if (strings.size() == 2) {
        int width = Integer.parseInt(strings.get(0));
        int height = Integer.parseInt(strings.get(1));
        if (width <= 700 && height <= 700 && width > 0 && height > 0) {
          sizePari = Pair.of(width, height);
        } else {
          throw new RuntimeException("The width and height of the barcode are not within the specified range");
        }
      }

    }
    return sizePari;
  }

  /**
   * 获取二维码大小
   *
   * @param wh 宽高
   * @return 宽高
   */
  public static Pair<Integer, Integer> getQRCodeSize(String wh) {
    Pair<Integer, Integer> sizePari = Pair.of(400, 400);
    // 如果没有*，就是原来的输入一个参数的逻辑。那么默认就是400*400
    if (!Strings.isNullOrEmpty(wh) && wh.contains("*")) {
      List<String> strings = Splitter.on("*").splitToList(wh);
      if (strings.size() == 2) {
        int width = Integer.parseInt(strings.get(0));
        int height = Integer.parseInt(strings.get(1));
        if (width <= 700 && height <= 700 && width > 0 && height > 0) {
          sizePari = Pair.of(width, height);
        } else {
          throw new RuntimeException("The width and height of the barcode are not within the specified range");
        }
      }
    } else if (!Strings.isNullOrEmpty(wh))// 不是空，没有*，意味着只输入一位
    {
      int size = Integer.parseInt(wh);
      if (!(size <= 700 && size > 0)) {
        throw new RuntimeException("The width and height of the barcode are not within the specified range");
      }
      sizePari = Pair.of(size, size);
    }
    return sizePari;
  }

  public static byte[] encodeToQRCode(String contents, int width, int height) {
    Map<EncodeHintType, Object> hints = new Hashtable<>();
    hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
    hints.put(EncodeHintType.MARGIN, 0);
    return QRCodeUtils.encodeToQRCode(contents, BarcodeFormat.QR_CODE, width, height, hints);
  }


  /**
   * 生成条形码 带数字下标
   *
   * @param contents 内容
   * @param width    宽度
   * @param height   高度
   * @return 条形码   二进制
   */
  public static byte[] encodeToBarCodeWithNumber(String contents, int width, int height, int fontSize) {
    Map<EncodeHintType, Object> hints = new Hashtable<>();
    hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
    hints.put(EncodeHintType.MARGIN, 0);
    return QRCodeUtils.encodeToBarCode(contents, BarcodeFormat.CODE_128, width, height, fontSize, generateRandomNumber(), hints);
  }

  /**
   * 生成十三位随机数字下标
   *
   * @return 十三位随机数字下标
   */
  private static String generateRandomNumber() {
    Random rand = new Random();
    //生成一个在 [0,8999999999999] 的随机数, 然后加上 1000000000000，确保它总是13位数
    long num = (long) (rand.nextDouble() * 8999999999999L) + 1000000000000L;
    return Long.toString(num);
  }

  /**
   * 生成条形码 不带数字下标
   *
   * @param content 内容
   * @param width   宽度
   * @param height  高度
   * @return 条形码  二进制
   */
  public static byte[] encodeToCodeWithOutNumber(String content, int width, int height) {
    Map<EncodeHintType, Object> hints = new Hashtable<>();
    hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
    hints.put(EncodeHintType.MARGIN, 0);
    return QRCodeUtils.encodeToQRCode(content, BarcodeFormat.CODE_128, width, height, hints);
  }

  private static byte[] transferTobytes(BitMatrix matrix) throws IOException {
    BufferedImage image = toBufferedImage(matrix);
    Graphics2D g = image.createGraphics();
    g.drawImage(image, null, null);
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    ImageIO.write(image, "png", byteArrayOutputStream);
    g.dispose();
    byte[] pngBytes = byteArrayOutputStream.toByteArray();
    byteArrayOutputStream.close();
    return pngBytes;
  }

  /**
   * 将条形码转换成字节数组
   *
   * @param bitMatrix 二进制
   * @param fontSize  字体大小
   * @param number    数字下标
   * @return 字节数组
   * @throws IOException IO异常
   */
  private static byte[] transferBarCodeToBytes(BitMatrix bitMatrix, int fontSize, String number) throws IOException {
    int width = bitMatrix.getWidth();
    int height = bitMatrix.getHeight();
    BufferedImage image = insertContentIntoBarCode(getBarCode(bitMatrix), number, width, height, fontSize);
    Graphics2D g = image.createGraphics();
    g.drawImage(image, null, null);
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    ImageIO.write(image, "png", byteArrayOutputStream);
    g.dispose();
    byte[] pngBytes = byteArrayOutputStream.toByteArray();
    byteArrayOutputStream.close();
    return pngBytes;
  }

  private static BitMatrix deleteWhite(BitMatrix matrix) {
    int[] rec = matrix.getEnclosingRectangle();
    int resWidth = rec[2] + 1;
    int resHeight = rec[3] + 1;
    BitMatrix resMatrix = new BitMatrix(resWidth, resHeight);
    resMatrix.clear();
    for (int i = 0; i < resWidth; i++) {
      for (int j = 0; j < resHeight; j++) {
        if (matrix.get(i + rec[0], j + rec[1])) {
          resMatrix.set(i, j);
        }
      }
    }
    return resMatrix;
  }

  /**
   * 生成二维码内容<br>
   *
   * @param matrix
   * @return
   */
  private static BufferedImage toBufferedImage(BitMatrix matrix) {
    int width = matrix.getWidth();
    int height = matrix.getHeight();
    BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
    for (int x = 0; x < width; x++) {
      for (int y = 0; y < height; y++) {
        image.setRGB(x, y, matrix.get(x, y) == true ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
      }
    }
    return image;
  }

  /**
   * 设置抗锯齿属性
   *
   * @param g2 画笔
   */
  private static void setGraphics2D(Graphics2D g2) {
    g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
    g2.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_DEFAULT);
    Stroke s = new BasicStroke(1, BasicStroke.CAP_ROUND, BasicStroke.JOIN_MITER);
    g2.setStroke(s);
  }

  /**
   * 设置条形码图片背景色为白色
   *
   * @param g2d 画笔
   */
  private static void setColorWhite(Graphics2D g2d) {
    g2d.setColor(Color.WHITE);
    // 填充整个屏幕
    g2d.fillRect(0, 0, 1000, 1000);
    // 设置笔刷
    g2d.setColor(Color.BLACK);
  }

  /**
   * 生成条形码
   *
   * @param matrix 二进制
   * @return 条形码
   */
  private static BufferedImage getBarCode(BitMatrix matrix) {
    int width = matrix.getWidth();
    int height = matrix.getHeight();
    BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
    for (int x = 0; x < width; x++) {
      for (int y = 0; y < height; y++) {
        image.setRGB(x, y, matrix.get(x, y) ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
      }
    }
    return image;
  }

  /**
   * 插入数字下标
   *
   * @param bufferedImage 条形码
   * @param number        数字
   * @param width         宽度
   * @param height        高度
   * @param fontSize      字体大小
   * @return 条形码
   */
  private static BufferedImage insertContentIntoBarCode(BufferedImage bufferedImage, String number, int width, int height, int fontSize) {
    if (!StringUtils.isEmpty(number)) { // 插入条形码内容
      int contentHeight = height + fontSize;
      BufferedImage outImage = new BufferedImage(width, contentHeight, BufferedImage.TYPE_INT_RGB);
      Graphics2D g2d = outImage.createGraphics();
      setGraphics2D(g2d); // 设置抗锯齿
      setColorWhite(g2d); // 设置白色
      g2d.drawImage(bufferedImage, 0, 0, bufferedImage.getWidth(), bufferedImage.getHeight(), null); // 画条形码
      g2d.setColor(Color.BLACK);
      g2d.setFont(new Font("黑体", Font.PLAIN, fontSize));
      int wordStartY = height + fontSize;
      String word = number.substring(1, number.length() - 1);
      int inputWidth = new Code128Writer().encode(word).length;
      int fullWidth = inputWidth + 1;
      int outPutWidth = Math.max(width, fullWidth);
      int multiple = outPutWidth / fullWidth;
      int leftPadding = (outPutWidth - (inputWidth * multiple)) / 2;
      int x = leftPadding;

      String[] split = number.split("");
      int len = split.length;
      int step = Math.round(width - leftPadding * 2) / number.length();
      for (int i = 0; i < len; i++) {
        g2d.drawString(split[i], x, wordStartY);
        x += step;
      }
      outImage.flush();
      return outImage;
    }
    return null;

  }

  public static void main(String[] args) throws IOException {
    String number = "www.baidu.com";
    Map<EncodeHintType, Object> hints = new Hashtable<>();
    hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
    hints.put(EncodeHintType.MARGIN, 1);
    byte[] barCodes = encodeByCodeModel(number, "", "BarCode");
    IOUtils.write(barCodes, new FileOutputStream(new File("barCode.png")));
  }
}
