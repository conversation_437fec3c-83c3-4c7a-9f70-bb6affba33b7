package com.facishare.fsc.common.utils;

import com.github.autoconf.base.ProcessInfo;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FSCUtils {

    private static final String DEFAULT_PORT = "80";
    public static String getNativeIpPort(){
        ProcessInfo processInfo = ConfigHelper.getProcessInfo();
        if (Strings.isNullOrEmpty(processInfo.getPort())) {
            processInfo.setPort(DEFAULT_PORT);
            log.warn("ProcessInfo port is null or empty, using default port: {}", DEFAULT_PORT);
        }
        return Joiner.on(":").join(processInfo.getIp(), processInfo.getPort());
    }
}
