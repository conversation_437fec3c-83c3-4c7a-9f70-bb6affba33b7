package com.facishare.fsc.common.utils;

import com.google.common.base.Strings;
import com.google.common.collect.Sets;

import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by <PERSON> on 16/5/20.
 */
public class HTMLUtils {

    public static Set<String> getRichTextImages(String html) {
        Set<String> images = Sets.newTreeSet();
        Pattern pattern = Pattern.compile("<img\\b[^<>]*?\\bsrc[\\s\\t\\r\\n]*=[\\s\\t\\r\\n]*[\"\"']?[\\s\\t\\r\\n]*([^\\s\\t\\r\\n'<>]*)[^<>]*?/?[\\s\\t\\r\\n]*>");
        Matcher matcher = pattern.matcher(html);
        while (matcher.find()) {
            String url = matcher.group(1);
            if (url.contains("/FSC/EM")) {
                images.add(url);
            }
        }
        return images;
    }


    public static String getContent(String str, String startTag, String endTag) {
        String result = null;
        String pattern = "(?<=" + startTag + ")[\\s\\S]*?(?=" + endTag + ")";
        Pattern reg = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher match = reg.matcher(str);
        if (match.find()) {
            result = match.group(0);
        }
        return result;
    }

    public static String replaceAll(String input, String regex, String replacement, int flags) {
        Pattern regexScript1 = Pattern.compile(regex, flags);
        Matcher matcher = regexScript1.matcher(input);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String filterHtml(String html) {
        if (Strings.isNullOrEmpty(html))
            return "";
        //过滤 script
        int flags = Pattern.CASE_INSENSITIVE;
        html = replaceAll(html, "(<script[//s//S]*?///script//s*>)", "", flags);
        html = replaceAll(html, "(<(script[//s//S]*?)>|<(/script[//s//S]*?)>)", "", flags);
        //过滤 <iframe> 标签
        html = replaceAll(html, "(<iframe[//s//S]*?///iframe//s*>)", "", flags);
        html = replaceAll(html, "(<(iframe[//s//S]*?)>|<(/iframe[//s//S]*?)>)", "", flags);
        //过滤 <frameset> 标签
        html = replaceAll(html, "(<frameset [//s//S]+<frameset //s*>)", "", flags);
        html = replaceAll(html, "(<(frameset[//s//S]*?)>|<(/frameset[//s//S]*?)>)", "", flags);
        //过滤 <frame> 标签
        html = replaceAll(html, "(<frame[//s//S]+<frame //s*>)", "", flags);
        html = replaceAll(html, "(<(frame[//s//S]*?)>|<(/frame[//s//S]*?)>)", "", flags);
        //过滤 <form> 标签
        html = replaceAll(html, "(<(form [//s//S]*?)>)", "", flags);
        html = replaceAll(html, "(<(form[//s//S]*?)>|<(/form[//s//S]*?)>)", "", flags);
        //过滤 on: 的事件
        //过滤on 带单引号的 过滤on  带双引号的 过滤on 不带有引号的
        html = replaceAll(html, "<[//s//S]+ (on)[a-zA-Z]{4,20} *= *[//S ]{3,}>", "", flags);
        html = replaceAll(html, "((on)[a-zA-Z]{4,20} *= *'[^']{3,}')|((on)[a-zA-Z]{4,20} *= */\"\"[^/\"\"]{3,}/\"\")|((on)[a-zA-Z]{4,20} *= *[^>/ ]{3,})", "", flags);
        //过滤 javascript: 的事件
        html = replaceAll(html, "<[//s//S]+ (href|src|background|url|dynsrc|expression|codebase) *= *[ /\"\"/']? *(javascript:)[//S]{1,}>", "", flags);
        html = replaceAll(html, "(' *(javascript|vbscript):([//S^'])*')|(/\"\" *(javascript|vbscript):[//S^/\"\"]*/\"\")|([^=]*(javascript|vbscript):[^/> ]*)", "", flags);
        return html;
    }

    public static void main(String[] args) {
//        String content="{\"content\":\"<p style=\\\"margin: 0px;\\\">11111</p><p style=\\\"margin: 0px;\\\"><br/></p><p style=\\\"margin: 0px;\\\">2222剧——猿人、原始人、古人、近代人、现代人、未来人出现在同一画面，迥异的画风相映成趣。毫无悬念地登上各大网络媒体头条，挑起网友热议……</p><p style=\\\"margin: 0px;\\\"><br/></p><p style=\\\"margin: 0px;\\\">2020-06-0214:15:31</p><p style=\\\"margin: 0px;\\\"><img type=\\\"l\\\" src=\\\"/FSC/EM/File/ViewTempImg?TempFileName=TN_584ac94d067c484a9f39bd02e291deae&TempFileExt=jpg\\\" style=\\\"max-width:450px;width: 100%;\\\"/></p>\",\"title\":\"\",\"isFreeView\":true}";
//        content = HTMLUtils.filterHtml(content);
//        Set<String> images = HTMLUtils.getRichTextImages(content);

    }
}
