package com.facishare.fsc.common.utils;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.File;
import java.io.FileInputStream;
import java.util.List;

/**
 * Created by <PERSON> on 16/5/17.
 */
@Slf4j
public class IOSLuaVersionUtils {

    public static String needLuaFileUpdate(String iOSVersionPath, String appVersion, String luaVersion) {
        String pathResult;
        int lastVersion = -1;
        int mode = 0;
        int _luaVersion = Strings.isNullOrEmpty(luaVersion) ? 0 : Integer.parseInt(StringUtils.trim(luaVersion));
        try {
            String path = iOSVersionPath;
            String versionXml = IOUtils.toString(new FileInputStream(path + File.separator + "Version.xml"));
            Document dom = DocumentHelper.parseText(versionXml);
            //获取根节点
            Element root = dom.getRootElement();
            //获取根节点下的所有元素
            List<Element> children = root.elements();
            //循环所有子元素
            if (children != null && children.size() > 0) {
                for (Element e : children) {
                    if (e.attribute("AppVersion").getValue().equalsIgnoreCase(appVersion)) {
                        lastVersion = Integer.parseInt(e.attribute("LastLuaVersion").getValue());
                        if (_luaVersion < lastVersion) {
                            for (Element e2 : (List<Element>) e.elements()) {
                                if (e2.attribute("LuaVersion").getValue().equalsIgnoreCase(lastVersion + "")) {
                                    pathResult = e2.attribute("Path").getValue();
                                    if (lastVersion > -1) {
                                        mode = Strings.isNullOrEmpty(pathResult) ? -1 : 1;
                                        if( Strings.isNullOrEmpty(pathResult)){
                                            mode=-1;
                                        }else{
                                            File file=new File(pathResult);
                                            if(file.exists()||file.isFile()){
                                                mode=-1;
                                            }else{
                                                mode=1;
                                            }
                                        }
                                        break;
                                    } else {
                                        mode = 0;
                                    }
                                    break;
                                }
                            }
                            if(lastVersion>-1){
                                break;
                            }
                        }
                    }
                }

            }


            return JsonUtils.toJson(new Result(mode, lastVersion));
        } catch (Exception e) {
            log.error("needLuaFileUpdate:{},{},{}", iOSVersionPath, appVersion, luaVersion, e);
            return JsonUtils.toJson(new Result(mode, _luaVersion));
        }


    }

    static class Result {
        public Result(int mode, int lastestVersion) {
            Mode = mode;
            LastestVersion = lastestVersion;
        }

        public int Mode;
        public int LastestVersion;
    }
}
