package com.facishare.fsc.common.utils;

import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/10/14  16:00
 */
@UtilityClass
public class DomainUtils {
  private List<String> allowDomains = Lists.newArrayList();

  static {
    ConfigFactory.getConfig("fs-fsc-switch-config", config -> {
      allowDomains = Splitter.on(",").splitToList(config.get("allow.domain"));
    });
  }

  public boolean allowCrossDomain(String host){
    return allowDomains.stream().anyMatch(item -> host.endsWith(item) || Objects.equals("*", item));
  }






}
