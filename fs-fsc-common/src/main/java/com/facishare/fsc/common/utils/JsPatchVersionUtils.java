package com.facishare.fsc.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by liuq on 2017/1/4.
 */
@Slf4j
public class JsPatchVersionUtils {
    public static int getLatestJsPatchVersion(Map<String, List<Integer>> appJsPatchVersions, String appVersion, int jspatchVersion) {
        log.info("appJsPathVersions:{},appVersion:{},jspatchVersion:{}",JSONObject.toJSON(appJsPatchVersions),appVersion,jspatchVersion);
        int latestJsPatchVersion = -1;//表示没有最新的
        List<Integer> jspatchVersions = appJsPatchVersions.get(appVersion);
        if (jspatchVersions != null) {
            int maxJspatchVersions = Collections.max(jspatchVersions);//取最大的版本号
            if (jspatchVersion < maxJspatchVersions) {
                latestJsPatchVersion = maxJspatchVersions;
            }
        }
        log.info("get latestJsPatchVersion:{}",latestJsPatchVersion);
        return latestJsPatchVersion;
    }

    public static String getResponseJson(Map<String, String> jspatchUrls, String appVersion, int jspatchVersion, int latestJsPatchVersion) {
        String result;
        if (latestJsPatchVersion > 0) {
            String key = appVersion + "_" + latestJsPatchVersion + "_url";
            if (jspatchUrls.containsKey(key)) {
                String jspatchUrl = jspatchUrls.get(key);
                result = createResponseJson(appVersion, jspatchVersion, latestJsPatchVersion, jspatchUrl);
            } else {
                result = createResponseJson(appVersion, jspatchVersion, jspatchVersion, "");
            }
        } else {
            result = createResponseJson(appVersion, jspatchVersion, jspatchVersion, "");
        }
        return result;
    }

    private static String createResponseJson(String appVersion, int jspatchVersion, int latestJsPatchVersion, String jspatchUrl) {
        Map<String, String> map = new HashMap<>();
        map.put("appVersion", appVersion);
        map.put("jspatchVersion", Integer.toString(jspatchVersion));
        map.put("latestJsPatchVersion", Integer.toString(latestJsPatchVersion));
        map.put("jspatchUrl", jspatchUrl);
        return JSONObject.toJSONString(map);
    }


    public static Map<String, List<Integer>> createAppJsPathVersions(Map<String, List<Integer>> appJsPathVersions, String appVersion, int jspatchVersion) {
        if (appJsPathVersions.containsKey(appVersion)) {
            List<Integer> jspatchVersions = appJsPathVersions.get(appVersion);
            if (!jspatchVersions.contains(jspatchVersion)) {
                jspatchVersions.add(jspatchVersion);
            }
            appJsPathVersions.put(appVersion, jspatchVersions);
        } else {
            List<Integer> tempList = Lists.newArrayList();
            tempList.add(jspatchVersion);
            appJsPathVersions.put(appVersion, tempList);
        }
        return appJsPathVersions;
    }
}
