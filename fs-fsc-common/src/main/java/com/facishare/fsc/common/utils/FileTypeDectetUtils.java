package com.facishare.fsc.common.utils;

import ch.qos.logback.classic.Level;
import eu.medsea.mimeutil.MimeUtil;
import eu.medsea.mimeutil.MimeUtil2;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by zhaifj on 2017/4/14.
 */
@Slf4j
public class FileTypeDectetUtils {

  static {
    try {
      MimeUtil.registerMimeDetector("eu.medsea.mimeutil.detector.MagicMimeMimeDetector");
      Logger logger = LoggerFactory.getLogger(MimeUtil2.class);
      if (logger.isDebugEnabled()) {
        ch.qos.logback.classic.Logger
            logbackLogger =
            (ch.qos.logback.classic.Logger) logger;
        logbackLogger.setLevel(Level.toLevel("INFO"));
      }
    } catch (Exception e) {
      log.error("set mime util fail", e);
    }
  }
  public static String getMimeType(byte[] data,String defaultStream){
    try{
//      return MimeUtil.getMimeTypes(data).toString();
      return defaultStream;
    }catch (Exception e){
      log.error("fileMimeTypeCheckFail...",e);
      return defaultStream;
    }

  }
}
