package com.facishare.fsc.common.utils;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.container.ContainerRequestContext;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * Created by <PERSON> on 16/5/13.
 */
@Slf4j
public class BrowserUtils {

  private BrowserUtils() {
  }

  private static boolean isSafiri(ContainerRequestContext request) {
    if (request == null) {
      return false;
    }
    try {
      String ua = request.getHeaderString("User-Agent");
      if (Strings.isNullOrEmpty(ua)) {
        return false;
      }
      return ua.toLowerCase().contains("safari") && !ua.toLowerCase().contains("chrome");
    } catch (Exception e) {
      return false;
    }

  }

  /**
   * <pre>
   * 浏览器下载文件时需要在服务端给出下载的文件名，当文件名是ASCII字符时没有问题
   * 当文件名有非ASCII字符时就有可能出现乱码
   *
   * 这里的实现方式参考这篇文章
   * http://blog.robotshell.org/2012/deal-with-http-header-encoding-for-file-download/
   *
   * 最终设置的response header是这样:
   *
   * Content-Disposition: attachment;
   *                      filename="encoded_text";
   *                      filename*=utf-8''encoded_text
   *
   * 其中encoded_text是经过RFC 3986的“百分号URL编码”规则处理过的文件名
   * </pre>
   */
  public static String getFullDispositionName(ContainerRequestContext request, String filename) {
    boolean isSafiri = isSafiri(request);
    String headerValue = "attachment;";
    headerValue +=
      "filename=\"" + (isSafiri ? encodeURIComponentForSafiri(filename) : encodeURIComponent(filename)) + "\";";
    headerValue += "filename*=utf-8''" + encodeURIComponent(filename);
    return headerValue;
  }

  public static String getFullDispositionName(ContainerRequestContext request, String filename, boolean preview) {
    boolean isSafiri = isSafiri(request);
    String headerValue = preview ? "inline;" : "attachment;";
    headerValue +=
      "filename=\"" + (isSafiri ? encodeURIComponentForSafiri(filename) : encodeURIComponent(filename)) + "\";";
    headerValue += "filename*=utf-8''" + encodeURIComponent(filename);
    return headerValue;
  }

  /**
   * <pre>
   * 符合 RFC 3986 标准的“百分号URL编码”
   * 在这个方法里，空格会被编码成%20，而不是+
   * 和浏览器的encodeURIComponent行为一致
   * </pre>
   */
  private static String encodeURIComponent(String value) {
    try {
      return URLEncoder.encode(value, "UTF-8").replaceAll("\\+", "%20");
    } catch (UnsupportedEncodingException e) {
      log.error("encodeURIComponent exception:{}", value, e);
      return null;
    }
  }

  private static String encodeURIComponentForSafiri(String value) {
    return new String(value.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
  }
}
