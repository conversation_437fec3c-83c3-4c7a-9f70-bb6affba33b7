package com.facishare.fsc.common.document;

import org.dom4j.Document;
import org.dom4j.DocumentFactory;
import org.dom4j.Element;
import org.dom4j.dom.DOMElement;

import java.util.List;

/**
 * Created by <PERSON> on 16/5/20.
 */
public class BatchDownloadDocumentUtils {
    public static String getSingleFileDocumentStr(List<BatchFileEntity> docList) {
        Document document = DocumentFactory.getInstance().createDocument("UTF-8");
        Element root = new DOMElement("Document");
        document.setRootElement(root);
        addChildFile(root,docList);
        return document.asXML();
    }

    private static void addChildFile(Element parent,List<BatchFileEntity> docList) {
        docList.forEach(entity -> {
            parent.add(new DOMElement("F").addAttribute("N", entity.Name).addAttribute("NP", entity.Path));
        });
    }

    public static String getFileDocumentStr(BatchFileDocument docList) {
        Document document = DocumentFactory.getInstance().createDocument("UTF-8");
        Element root = new DOMElement("Document");
        document.setRootElement(root);
        addChildFile(root,docList.getFiles());
        addBatchFileDocument(root,docList.getChilds());
        return document.asXML();
    }

    private static void addBatchFileDocument(Element root, List<BatchFileDocument> childs) {
        childs.forEach(child->{
            Element childElement = new DOMElement("D");
            childElement.addAttribute("N",child.getDirectoryName());
            root.add(childElement);
            addChildFile(childElement,child.getFiles());
            addBatchFileDocument(childElement,child.getChilds());
        });
    }


}
