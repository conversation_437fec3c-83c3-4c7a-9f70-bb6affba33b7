package com.facishare.fsc.common.utils;

public class AvatarPath {
    public String ea;
    public int employeeId;
    public String path;

    public AvatarPath(String ea, String path) {
        this.ea = ea;
        this.path = path;
    }

    public AvatarPath(String ea, int employeeId, String path) {
        this.ea = ea;
        this.employeeId = employeeId;
        this.path = path;
    }

    @Override
    public String toString() {
        return "AvatarPath{" +
                "ea='" + ea + '\'' +
                "employeeId='" + employeeId + '\'' +
                ", path='" + path + '\'' +
                '}';
    }
}