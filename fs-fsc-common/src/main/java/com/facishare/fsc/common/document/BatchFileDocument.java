package com.facishare.fsc.common.document;

import com.google.common.collect.Lists;

import java.util.List;

public class BatchFileDocument {
    //文件系统类型
    private String wt;
    //目录名称
    private String directoryName;
    //文件列表 平铺
    private List<BatchFileEntity> files;
    //文件列表 结构 递归
    private List<BatchFileDocument> childs;

    public BatchFileDocument() {

    }

    public String getWt() {
        return wt;
    }

    public void setWt(String wt) {
        this.wt = wt;
    }

    public List<BatchFileEntity> getFiles() {
        if(files==null){
            return Lists.newArrayList();
        }
        return files;
    }

    public void setFiles(List<BatchFileEntity> files) {
        this.files = files;
    }

    public List<BatchFileDocument> getChilds() {
        if(childs==null){
            return Lists.newArrayList();
        }
        return childs;
    }

    public void setChilds(List<BatchFileDocument> childs) {
        this.childs = childs;
    }

    public String getDirectoryName() {
        return directoryName;
    }

    public void setDirectoryName(String directoryName) {
        this.directoryName = directoryName;
    }

    @Override
    public String toString() {
        return "BatchFileDocument{" +
                "directoryName='" + directoryName + '\'' +
                ", files=" + files +
                ", childs=" + childs +
                '}';
    }
}