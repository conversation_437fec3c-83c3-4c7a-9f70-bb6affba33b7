package com.facishare.fsc.common.utils;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ParameterUtils {
  /**
   * 获取过期时间
   * @param expireDayStr 过期时间
   * @return 过期时间
   */
  public static int getExpireDay(String expireDayStr){
    int expireDay = 3;
    if (!Strings.isNullOrEmpty(expireDayStr)){
      try {
        expireDay = Integer.parseInt(expireDayStr);
        if(expireDay>30){
          expireDay=30;
          log.warn("The expiration time set is invalid :{}, reset to the maximum default value:{}",expireDayStr,expireDay);
        }
      }catch (Exception e){
        log.warn("Invalid  expireDayStr:{}",expireDayStr);
        return expireDay;
      }
    }
    return expireDay;
  }

  public static int getEmployeeId(int employeeId){
    if (employeeId <= 0){
      return -10000;
    }
    return employeeId;
  }

  public static int getTotalLength(String totalLengthStr){
    int totalLength = -1;
    if (!Strings.isNullOrEmpty(totalLengthStr)){
      try {
        totalLength = Integer.parseInt(totalLengthStr);
      }catch (Exception e){
        return totalLength;
      }
    }
    return totalLength;
  }
}
