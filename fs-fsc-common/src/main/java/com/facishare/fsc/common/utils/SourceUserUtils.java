package com.facishare.fsc.common.utils;

import com.google.common.base.CharMatcher;
import com.google.common.base.Strings;
import lombok.experimental.UtilityClass;

import java.util.regex.Pattern;

@UtilityClass
public class SourceUserUtils {
    final Pattern INTEGER_PATTERN = Pattern.compile("^[-+]?[\\d]*$");

    public Integer getEmployeeId(String user) {
        if (!Strings.isNullOrEmpty(user)) {
            int startSub = user.lastIndexOf('.');
            String employeeId = startSub > 0 ? user.substring(startSub + 1) : user;
            if (!Strings.isNullOrEmpty(employeeId) && INTEGER_PATTERN.matcher(employeeId).matches()) {
                return Integer.valueOf(employeeId);
            }
        }
        return null;
    }

    public String getEa(String user) {
        String ea = null;
        if (!Strings.isNullOrEmpty(user)) {
            int splitCharCount = CharMatcher.is('.').countIn(user);
            if (splitCharCount == 1) {
                ea = user.substring(0, user.indexOf('.'));
            } else if (splitCharCount > 1) {
                ea = user.substring(user.indexOf('.') + 1, user.lastIndexOf('.'));
            }
        }
        return ea;
    }
}
