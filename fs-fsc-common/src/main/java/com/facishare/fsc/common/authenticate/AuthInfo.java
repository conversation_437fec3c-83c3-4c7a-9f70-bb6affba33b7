package com.facishare.fsc.common.authenticate;

/**
 * Created by <PERSON> on 16/4/20.
 */
public class AuthInfo {
    private String enterpriseAccount;
    private int enterpriseId;
    private int employeeId;
    private String employeeAccount;
    private String employeeName = "";
    private String employeeFullName;
    private String mobile;
    private String clientSource;
    private String sessionToken;
    private String deviceId = "";
    private String sessionId;
    //    带有标示的UserId
    private String ticketUserId;

    public String getTicketUserId() {
        return ticketUserId;
    }

    public void setTicketUserId(String ticketUserId) {
        this.ticketUserId = ticketUserId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public int getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(int enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public int getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(int employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeAccount() {
        return employeeAccount;
    }

    public void setEmployeeAccount(String employeeAccount) {
        this.employeeAccount = employeeAccount;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmployeeFullName() {
        return employeeFullName;
    }

    public void setEmployeeFullName(String employeeFullName) {
        this.employeeFullName = employeeFullName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getClientSource() {
        return clientSource;
    }

    public void setClientSource(String clientSource) {
        this.clientSource = clientSource;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    /**
     * 获取用户身份
     * @return ea.employeeId
     */
    public String getSourceUser(){
        return enterpriseAccount + "." + employeeId;
    }

    @Override
    public String toString() {
        return "AuthInfo{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", enterpriseId=" + enterpriseId +
                ", employeeId=" + employeeId +
                ", employeeAccount='" + employeeAccount + '\'' +
                ", employeeName='" + employeeName + '\'' +
                ", employeeFullName='" + employeeFullName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", clientSource='" + clientSource + '\'' +
                ", sessionToken='" + sessionToken + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", ticketUserId='" + ticketUserId + '\'' +
                '}';
    }
}
