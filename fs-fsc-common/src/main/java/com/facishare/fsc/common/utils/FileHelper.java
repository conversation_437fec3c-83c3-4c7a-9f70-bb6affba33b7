package com.facishare.fsc.common.utils;

import com.google.common.base.Preconditions;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.Base64Utils;

import java.util.HashMap;
import java.util.Map;

public class FileHelper {
  // 初始化MimeTypeMapping
  private static final Map<String, String> Mappings =  iniMimeTypeMapping();

  public static Map<String, String> iniMimeTypeMapping(){
    Map<String, String> mimeTypeMapping =new HashMap<>();
    mimeTypeMapping.put(".ez", "application/andrew-inset");
    mimeTypeMapping.put(".anx", "application/annodex");
    mimeTypeMapping.put(".atom", "application/atom+xml");
    mimeTypeMapping.put(".atomcat", "application/atomcat+xml");
    mimeTypeMapping.put(".atomsrv", "application/atomserv+xml");
    mimeTypeMapping.put(".lin", "application/bbolin");
    mimeTypeMapping.put(".cap", "application/cap");
    mimeTypeMapping.put(".pcap", "application/cap");
    mimeTypeMapping.put(".cu", "application/cu-seeme");
    mimeTypeMapping.put(".davmount", "application/davmount+xml");
    mimeTypeMapping.put(".tsp", "application/dsptype");
    mimeTypeMapping.put(".es", "application/ecmascript");
    mimeTypeMapping.put(".spl", "application/futuresplash");
    mimeTypeMapping.put(".hta", "application/hta");
    mimeTypeMapping.put(".jar", "application/java-archive");
    mimeTypeMapping.put(".ser", "application/java-serialized-object");
    mimeTypeMapping.put(".class", "application/java-vm");
    mimeTypeMapping.put(".js", "application/javascript");
    mimeTypeMapping.put(".m3g", "application/m3g");
    mimeTypeMapping.put(".hqx", "application/mac-binhex40");
    mimeTypeMapping.put(".cpt", "application/mac-compactpro");
    mimeTypeMapping.put(".nb", "application/mathematica");
    mimeTypeMapping.put(".nbp", "application/mathematica");
    mimeTypeMapping.put(".mdb", "application/msaccess");
    mimeTypeMapping.put(".doc", "application/msword");
    mimeTypeMapping.put(".dot", "application/msword");
    mimeTypeMapping.put(".mxf", "application/mxf");
    mimeTypeMapping.put(".bin", "application/octet-stream");
    mimeTypeMapping.put(".oda", "application/oda");
    mimeTypeMapping.put(".ogx", "application/ogg");
    mimeTypeMapping.put(".pdf", "application/pdf");
    mimeTypeMapping.put(".key", "application/pgp-keys");
    mimeTypeMapping.put(".pgp", "application/pgp-signature");
    mimeTypeMapping.put(".prf", "application/pics-rules");
    mimeTypeMapping.put(".ps", "application/postscript");
    mimeTypeMapping.put(".ai", "application/postscript");
    mimeTypeMapping.put(".eps", "application/postscript");
    mimeTypeMapping.put(".epsi", "application/postscript");
    mimeTypeMapping.put(".epsf", "application/postscript");
    mimeTypeMapping.put(".eps2", "application/postscript");
    mimeTypeMapping.put(".eps3", "application/postscript");
    mimeTypeMapping.put(".rar", "application/rar");
    mimeTypeMapping.put(".rdf", "application/rdf+xml");
    mimeTypeMapping.put(".rss", "application/rss+xml");
    mimeTypeMapping.put(".rtf", "application/rtf");
    mimeTypeMapping.put(".smi", "application/smil");
    mimeTypeMapping.put(".smil", "application/smil");
    mimeTypeMapping.put(".xhtml", "application/xhtml+xml");
    mimeTypeMapping.put(".xht", "application/xhtml+xml");
    mimeTypeMapping.put(".xml", "application/xml");
    mimeTypeMapping.put(".xsl", "application/xml");
    mimeTypeMapping.put(".xsd", "application/xml");
    mimeTypeMapping.put(".xspf", "application/xspf+xml");
    mimeTypeMapping.put(".zip", "application/zip");
    mimeTypeMapping.put(".apk", "application/vnd.android.package-archive");
    mimeTypeMapping.put(".cdy", "application/vnd.cinderella");
    mimeTypeMapping.put(".kml", "application/vnd.google-earth.kml+xml");
    mimeTypeMapping.put(".kmz", "application/vnd.google-earth.kmz");
    mimeTypeMapping.put(".xul", "application/vnd.mozilla.xul+xml");
    mimeTypeMapping.put(".xls", "application/vnd.ms-excel");
    mimeTypeMapping.put(".xlb", "application/vnd.ms-excel");
    mimeTypeMapping.put(".xlt", "application/vnd.ms-excel");
    mimeTypeMapping.put(".cat", "application/vnd.ms-pki.seccat");
    mimeTypeMapping.put(".stl", "application/vnd.ms-pki.stl");
    mimeTypeMapping.put(".ppt", "application/vnd.ms-powerpoint");
    mimeTypeMapping.put(".pps", "application/vnd.ms-powerpoint");
    mimeTypeMapping.put(".odc", "application/vnd.oasis.opendocument.chart");
    mimeTypeMapping.put(".odb", "application/vnd.oasis.opendocument.database");
    mimeTypeMapping.put(".odf", "application/vnd.oasis.opendocument.formula");
    mimeTypeMapping.put(".odg", "application/vnd.oasis.opendocument.graphics");
    mimeTypeMapping.put(".otg", "application/vnd.oasis.opendocument.graphics-template");
    mimeTypeMapping.put(".odi", "application/vnd.oasis.opendocument.image");
    mimeTypeMapping.put(".odp", "application/vnd.oasis.opendocument.presentation");
    mimeTypeMapping.put(".otp", "application/vnd.oasis.opendocument.presentation-template");
    mimeTypeMapping.put(".ods", "application/vnd.oasis.opendocument.spreadsheet");
    mimeTypeMapping.put(".ots", "application/vnd.oasis.opendocument.spreadsheet-template");
    mimeTypeMapping.put(".odt", "application/vnd.oasis.opendocument.text");
    mimeTypeMapping.put(".odm", "application/vnd.oasis.opendocument.text-master");
    mimeTypeMapping.put(".ott", "application/vnd.oasis.opendocument.text-template");
    mimeTypeMapping.put(".oth", "application/vnd.oasis.opendocument.text-web");
    mimeTypeMapping.put(".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    mimeTypeMapping.put(".xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template");
    mimeTypeMapping.put(".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
    mimeTypeMapping.put(".ppsx", "application/vnd.openxmlformats-officedocument.presentationml.slideshow");
    mimeTypeMapping.put(".potx", "application/vnd.openxmlformats-officedocument.presentationml.template");
    mimeTypeMapping.put(".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    mimeTypeMapping.put(".dotx", "application/vnd.openxmlformats-officedocument.wordprocessingml.template");
    mimeTypeMapping.put(".cod", "application/vnd.rim.cod");
    mimeTypeMapping.put(".mmf", "application/vnd.smaf");
    mimeTypeMapping.put(".sdc", "application/vnd.stardivision.calc");
    mimeTypeMapping.put(".sds", "application/vnd.stardivision.chart");
    mimeTypeMapping.put(".sda", "application/vnd.stardivision.draw");
    mimeTypeMapping.put(".sdd", "application/vnd.stardivision.impress");
    mimeTypeMapping.put(".sdf", "application/vnd.stardivision.math");
    mimeTypeMapping.put(".sdw", "application/vnd.stardivision.writer");
    mimeTypeMapping.put(".sgl", "application/vnd.stardivision.writer-global");
    mimeTypeMapping.put(".sxc", "application/vnd.sun.xml.calc");
    mimeTypeMapping.put(".stc", "application/vnd.sun.xml.calc.template");
    mimeTypeMapping.put(".sxd", "application/vnd.sun.xml.draw");
    mimeTypeMapping.put(".std", "application/vnd.sun.xml.draw.template");
    mimeTypeMapping.put(".sxi", "application/vnd.sun.xml.impress");
    mimeTypeMapping.put(".sti", "application/vnd.sun.xml.impress.template");
    mimeTypeMapping.put(".sxm", "application/vnd.sun.xml.math");
    mimeTypeMapping.put(".sxw", "application/vnd.sun.xml.writer");
    mimeTypeMapping.put(".sxg", "application/vnd.sun.xml.writer.global");
    mimeTypeMapping.put(".stw", "application/vnd.sun.xml.writer.template");
    mimeTypeMapping.put(".sis", "application/vnd.symbian.install");
    mimeTypeMapping.put(".vsd", "application/vnd.visio");
    mimeTypeMapping.put(".wbxml", "application/vnd.wap.wbxml");
    mimeTypeMapping.put(".wmlc", "application/vnd.wap.wmlc");
    mimeTypeMapping.put(".wmlsc", "application/vnd.wap.wmlscriptc");
    mimeTypeMapping.put(".wpd", "application/vnd.wordperfect");
    mimeTypeMapping.put(".wp5", "application/vnd.wordperfect5.1");
    mimeTypeMapping.put(".wk", "application/x-123");
    mimeTypeMapping.put(".7z", "application/x-7z-compressed");
    mimeTypeMapping.put(".abw", "application/x-abiword");
    mimeTypeMapping.put(".dmg", "application/x-apple-diskimage");
    mimeTypeMapping.put(".bcpio", "application/x-bcpio");
    mimeTypeMapping.put(".torrent", "application/x-bittorrent");
    mimeTypeMapping.put(".cab", "application/x-cab");
    mimeTypeMapping.put(".cbr", "application/x-cbr");
    mimeTypeMapping.put(".cbz", "application/x-cbz");
    mimeTypeMapping.put(".cdf", "application/x-cdf");
    mimeTypeMapping.put(".cda", "application/x-cdf");
    mimeTypeMapping.put(".vcd", "application/x-cdlink");
    mimeTypeMapping.put(".pgn", "application/x-chess-pgn");
    mimeTypeMapping.put(".cpio", "application/x-cpio");
    mimeTypeMapping.put(".csh", "application/x-csh");
    mimeTypeMapping.put(".deb", "application/x-debian-package");
    mimeTypeMapping.put(".udeb", "application/x-debian-package");
    mimeTypeMapping.put(".dcr", "application/x-director");
    mimeTypeMapping.put(".dir", "application/x-director");
    mimeTypeMapping.put(".dxr", "application/x-director");
    mimeTypeMapping.put(".dms", "application/x-dms");
    mimeTypeMapping.put(".wad", "application/x-doom");
    mimeTypeMapping.put(".dvi", "application/x-dvi");
    mimeTypeMapping.put(".rhtml", "application/x-httpd-eruby");
    mimeTypeMapping.put(".pfa", "application/x-font");
    mimeTypeMapping.put(".pfb", "application/x-font");
    mimeTypeMapping.put(".gsf", "application/x-font");
    mimeTypeMapping.put(".pcf", "application/x-font");
    mimeTypeMapping.put(".pcf.Z", "application/x-font");
    mimeTypeMapping.put(".mm", "application/x-freemind");
    mimeTypeMapping.put(".gnumeric", "application/x-gnumeric");
    mimeTypeMapping.put(".sgf", "application/x-go-sgf");
    mimeTypeMapping.put(".gcf", "application/x-graphing-calculator");
    mimeTypeMapping.put(".gtar", "application/x-gtar");
    mimeTypeMapping.put(".tgz", "application/x-gtar");
    mimeTypeMapping.put(".taz", "application/x-gtar");
    mimeTypeMapping.put(".hdf", "application/x-hdf");
    mimeTypeMapping.put(".phtml", "application/x-httpd-php");
    mimeTypeMapping.put(".pht", "application/x-httpd-php");
    mimeTypeMapping.put(".php", "application/x-httpd-php");
    mimeTypeMapping.put(".phps", "application/x-httpd-php-source");
    mimeTypeMapping.put(".php3", "application/x-httpd-php3");
    mimeTypeMapping.put(".php3p", "application/x-httpd-php3-preprocessed");
    mimeTypeMapping.put(".php4", "application/x-httpd-php4");
    mimeTypeMapping.put(".php5", "application/x-httpd-php5");
    mimeTypeMapping.put(".ica", "application/x-ica");
    mimeTypeMapping.put(".info", "application/x-info");
    mimeTypeMapping.put(".ins", "application/x-internet-signup");
    mimeTypeMapping.put(".isp", "application/x-internet-signup");
    mimeTypeMapping.put(".iii", "application/x-iphone");
    mimeTypeMapping.put(".iso", "application/x-iso9660-image");
    mimeTypeMapping.put(".jam", "application/x-jam");
    mimeTypeMapping.put(".jnlp", "application/x-java-jnlp-file");
    mimeTypeMapping.put(".jmz", "application/x-jmol");
    mimeTypeMapping.put(".chrt", "application/x-kchart");
    mimeTypeMapping.put(".kil", "application/x-killustrator");
    mimeTypeMapping.put(".skp", "application/x-koan");
    mimeTypeMapping.put(".skd", "application/x-koan");
    mimeTypeMapping.put(".skt", "application/x-koan");
    mimeTypeMapping.put(".skm", "application/x-koan");
    mimeTypeMapping.put(".kpr", "application/x-kpresenter");
    mimeTypeMapping.put(".kpt", "application/x-kpresenter");
    mimeTypeMapping.put(".ksp", "application/x-kspread");
    mimeTypeMapping.put(".kwd", "application/x-kword");
    mimeTypeMapping.put(".kwt", "application/x-kword");
    mimeTypeMapping.put(".latex", "application/x-latex");
    mimeTypeMapping.put(".lha", "application/x-lha");
    mimeTypeMapping.put(".lyx", "application/x-lyx");
    mimeTypeMapping.put(".lzh", "application/x-lzh");
    mimeTypeMapping.put(".lzx", "application/x-lzx");
    mimeTypeMapping.put(".frm", "application/x-maker");
    mimeTypeMapping.put(".maker", "application/x-maker");
    mimeTypeMapping.put(".frame", "application/x-maker");
    mimeTypeMapping.put(".fm", "application/x-maker");
    mimeTypeMapping.put(".fb", "application/x-maker");
    mimeTypeMapping.put(".book", "application/x-maker");
    mimeTypeMapping.put(".fbdoc", "application/x-maker");
    mimeTypeMapping.put(".mif", "application/x-mif");
    mimeTypeMapping.put(".wmd", "application/x-ms-wmd");
    mimeTypeMapping.put(".wmz", "application/x-ms-wmz");
    mimeTypeMapping.put(".com", "application/x-msdos-program");
    mimeTypeMapping.put(".exe", "application/x-msdos-program");
    mimeTypeMapping.put(".bat", "application/x-msdos-program");
    mimeTypeMapping.put(".dll", "application/x-msdos-program");
    mimeTypeMapping.put(".msi", "application/x-msi");
    mimeTypeMapping.put(".nc", "application/x-netcdf");
    mimeTypeMapping.put(".pac", "application/x-ns-proxy-autoconfig");
    mimeTypeMapping.put(".dat", "application/x-ns-proxy-autoconfig");
    mimeTypeMapping.put(".nwc", "application/x-nwc");
    mimeTypeMapping.put(".o", "application/x-object");
    mimeTypeMapping.put(".oza", "application/x-oz-application");
    mimeTypeMapping.put(".p7r", "application/x-pkcs7-certreqresp");
    mimeTypeMapping.put(".crl", "application/x-pkcs7-crl");
    mimeTypeMapping.put(".pyc", "application/x-python-code");
    mimeTypeMapping.put(".pyo", "application/x-python-code");
    mimeTypeMapping.put(".qgs", "application/x-qgis");
    mimeTypeMapping.put(".shp", "application/x-qgis");
    mimeTypeMapping.put(".shx", "application/x-qgis");
    mimeTypeMapping.put(".qtl", "application/x-quicktimeplayer");
    mimeTypeMapping.put(".rpm", "application/x-redhat-package-manager");
    mimeTypeMapping.put(".rb", "application/x-ruby");
    mimeTypeMapping.put(".sh", "application/x-sh");
    mimeTypeMapping.put(".shar", "application/x-shar");
    mimeTypeMapping.put(".swf", "application/x-shockwave-flash");
    mimeTypeMapping.put(".swfl", "application/x-shockwave-flash");
    mimeTypeMapping.put(".scr", "application/x-silverlight");
    mimeTypeMapping.put(".sit", "application/x-stuffit");
    mimeTypeMapping.put(".sitx", "application/x-stuffit");
    mimeTypeMapping.put(".sv4cpio", "application/x-sv4cpio");
    mimeTypeMapping.put(".sv4crc", "application/x-sv4crc");
    mimeTypeMapping.put(".tar", "application/x-tar");
    mimeTypeMapping.put(".tcl", "application/x-tcl");
    mimeTypeMapping.put(".gf", "application/x-tex-gf");
    mimeTypeMapping.put(".pk", "application/x-tex-pk");
    mimeTypeMapping.put(".texinfo", "application/x-texinfo");
    mimeTypeMapping.put(".texi", "application/x-texinfo");
    mimeTypeMapping.put(".~", "application/x-trash");
    mimeTypeMapping.put(".%", "application/x-trash");
    mimeTypeMapping.put(".bak", "application/x-trash");
    mimeTypeMapping.put(".old", "application/x-trash");
    mimeTypeMapping.put(".sik", "application/x-trash");
    mimeTypeMapping.put(".t", "application/x-troff");
    mimeTypeMapping.put(".tr", "application/x-troff");
    mimeTypeMapping.put(".roff", "application/x-troff");
    mimeTypeMapping.put(".man", "application/x-troff-man");
    mimeTypeMapping.put(".me", "application/x-troff-me");
    mimeTypeMapping.put(".ms", "application/x-troff-ms");
    mimeTypeMapping.put(".ustar", "application/x-ustar");
    mimeTypeMapping.put(".src", "application/x-wais-source");
    mimeTypeMapping.put(".wz", "application/x-wingz");
    mimeTypeMapping.put(".crt", "application/x-x509-ca-cert");
    mimeTypeMapping.put(".xcf", "application/x-xcf");
    mimeTypeMapping.put(".fig", "application/x-xfig");
    mimeTypeMapping.put(".xpi", "application/x-xpinstall");
    mimeTypeMapping.put(".amr", "audio/amr");
    mimeTypeMapping.put(".awb", "audio/amr-wb");
    mimeTypeMapping.put(".axa", "audio/annodex");
    mimeTypeMapping.put(".au", "audio/basic");
    mimeTypeMapping.put(".snd", "audio/basic");
    mimeTypeMapping.put(".flac", "audio/flac");
    mimeTypeMapping.put(".mid", "audio/midi");
    mimeTypeMapping.put(".midi", "audio/midi");
    mimeTypeMapping.put(".kar", "audio/midi");
    mimeTypeMapping.put(".mpga", "audio/mpeg");
    mimeTypeMapping.put(".mpega", "audio/mpeg");
    mimeTypeMapping.put(".mp2", "audio/mpeg");
    mimeTypeMapping.put(".mp3", "audio/mpeg");
    mimeTypeMapping.put(".m4a", "audio/mpeg");
    mimeTypeMapping.put(".m3u", "audio/mpegurl");
    mimeTypeMapping.put(".oga", "audio/ogg");
    mimeTypeMapping.put(".ogg", "audio/ogg");
    mimeTypeMapping.put(".spx", "audio/ogg");
    mimeTypeMapping.put(".sid", "audio/prs.sid");
    mimeTypeMapping.put(".aif", "audio/x-aiff");
    mimeTypeMapping.put(".aiff", "audio/x-aiff");
    mimeTypeMapping.put(".aifc", "audio/x-aiff");
    mimeTypeMapping.put(".gsm", "audio/x-gsm");
    mimeTypeMapping.put(".wma", "audio/x-ms-wma");
    mimeTypeMapping.put(".wax", "audio/x-ms-wax");
    mimeTypeMapping.put(".ra", "audio/x-pn-realaudio");
    mimeTypeMapping.put(".rm", "audio/x-pn-realaudio");
    mimeTypeMapping.put(".ram", "audio/x-pn-realaudio");
    mimeTypeMapping.put(".pls", "audio/x-scpls");
    mimeTypeMapping.put(".sd2", "audio/x-sd2");
    mimeTypeMapping.put(".wav", "audio/x-wav");
    mimeTypeMapping.put(".alc", "chemical/x-alchemy");
    mimeTypeMapping.put(".cac", "chemical/x-cache");
    mimeTypeMapping.put(".cache", "chemical/x-cache");
    mimeTypeMapping.put(".csf", "chemical/x-cache-csf");
    mimeTypeMapping.put(".cbin", "chemical/x-cactvs-binary");
    mimeTypeMapping.put(".cascii", "chemical/x-cactvs-binary");
    mimeTypeMapping.put(".ctab", "chemical/x-cactvs-binary");
    mimeTypeMapping.put(".cdx", "chemical/x-cdx");
    mimeTypeMapping.put(".cer", "chemical/x-cerius");
    mimeTypeMapping.put(".c3d", "chemical/x-chem3d");
    mimeTypeMapping.put(".chm", "chemical/x-chemdraw");
    mimeTypeMapping.put(".cif", "chemical/x-cif");
    mimeTypeMapping.put(".cmdf", "chemical/x-cmdf");
    mimeTypeMapping.put(".cml", "chemical/x-cml");
    mimeTypeMapping.put(".cpa", "chemical/x-compass");
    mimeTypeMapping.put(".bsd", "chemical/x-crossfire");
    mimeTypeMapping.put(".csml", "chemical/x-csml");
    mimeTypeMapping.put(".csm", "chemical/x-csml");
    mimeTypeMapping.put(".ctx", "chemical/x-ctx");
    mimeTypeMapping.put(".cxf", "chemical/x-cxf");
    mimeTypeMapping.put(".cef", "chemical/x-cxf");
    mimeTypeMapping.put(".emb", "chemical/x-embl-dl-nucleotide");
    mimeTypeMapping.put(".embl", "chemical/x-embl-dl-nucleotide");
    mimeTypeMapping.put(".spc", "chemical/x-galactic-spc");
    mimeTypeMapping.put(".inp", "chemical/x-gamess-input");
    mimeTypeMapping.put(".gam", "chemical/x-gamess-input");
    mimeTypeMapping.put(".gamin", "chemical/x-gamess-input");
    mimeTypeMapping.put(".fch", "chemical/x-gaussian-checkpoint");
    mimeTypeMapping.put(".fchk", "chemical/x-gaussian-checkpoint");
    mimeTypeMapping.put(".cub", "chemical/x-gaussian-cube");
    mimeTypeMapping.put(".gau", "chemical/x-gaussian-input");
    mimeTypeMapping.put(".gjc", "chemical/x-gaussian-input");
    mimeTypeMapping.put(".gjf", "chemical/x-gaussian-input");
    mimeTypeMapping.put(".gal", "chemical/x-gaussian-log");
    mimeTypeMapping.put(".gcg", "chemical/x-gcg8-sequence");
    mimeTypeMapping.put(".gen", "chemical/x-genbank");
    mimeTypeMapping.put(".hin", "chemical/x-hin");
    mimeTypeMapping.put(".istr", "chemical/x-isostar");
    mimeTypeMapping.put(".ist", "chemical/x-isostar");
    mimeTypeMapping.put(".jdx", "chemical/x-jcamp-dx");
    mimeTypeMapping.put(".dx", "chemical/x-jcamp-dx");
    mimeTypeMapping.put(".kin", "chemical/x-kinemage");
    mimeTypeMapping.put(".mcm", "chemical/x-macmolecule");
    mimeTypeMapping.put(".mmd", "chemical/x-macromodel-input");
    mimeTypeMapping.put(".mmod", "chemical/x-macromodel-input");
    mimeTypeMapping.put(".mol", "chemical/x-mdl-molfile");
    mimeTypeMapping.put(".rd", "chemical/x-mdl-rdfile");
    mimeTypeMapping.put(".rxn", "chemical/x-mdl-rxnfile");
    mimeTypeMapping.put(".sd", "chemical/x-mdl-sdfile");
    mimeTypeMapping.put(".tgf", "chemical/x-mdl-tgf");
    mimeTypeMapping.put(".mcif", "chemical/x-mmcif");
    mimeTypeMapping.put(".mol2", "chemical/x-mol2");
    mimeTypeMapping.put(".b", "chemical/x-molconn-Z");
    mimeTypeMapping.put(".gpt", "chemical/x-mopac-graph");
    mimeTypeMapping.put(".mop", "chemical/x-mopac-input");
    mimeTypeMapping.put(".mopcrt", "chemical/x-mopac-input");
    mimeTypeMapping.put(".mpc", "chemical/x-mopac-input");
    mimeTypeMapping.put(".zmt", "chemical/x-mopac-input");
    mimeTypeMapping.put(".moo", "chemical/x-mopac-out");
    mimeTypeMapping.put(".mvb", "chemical/x-mopac-vib");
    mimeTypeMapping.put(".asn", "chemical/x-ncbi-asn1");
    mimeTypeMapping.put(".prt", "chemical/x-ncbi-asn1-ascii");
    mimeTypeMapping.put(".ent", "chemical/x-ncbi-asn1-ascii");
    mimeTypeMapping.put(".val", "chemical/x-ncbi-asn1-binary");
    mimeTypeMapping.put(".aso", "chemical/x-ncbi-asn1-binary");
    mimeTypeMapping.put(".pdb", "chemical/x-pdb");
    mimeTypeMapping.put(".ros", "chemical/x-rosdal");
    mimeTypeMapping.put(".sw", "chemical/x-swissprot");
    mimeTypeMapping.put(".vms", "chemical/x-vamas-iso14976");
    mimeTypeMapping.put(".vmd", "chemical/x-vmd");
    mimeTypeMapping.put(".xtel", "chemical/x-xtel");
    mimeTypeMapping.put(".xyz", "chemical/x-xyz");
    mimeTypeMapping.put(".bmp", "image/bmp");
    mimeTypeMapping.put(".gif", "image/gif");
    mimeTypeMapping.put(".ief", "image/ief");
    mimeTypeMapping.put(".jpeg", "image/jpeg");
    mimeTypeMapping.put(".jpg", "image/jpeg");
    mimeTypeMapping.put(".webp", "image/webp");
    mimeTypeMapping.put(".jpe", "image/jpeg");
    mimeTypeMapping.put(".pcx", "image/pcx");
    mimeTypeMapping.put(".png", "image/png");
    mimeTypeMapping.put(".svg", "image/svg+xml");
    mimeTypeMapping.put(".svgz", "image/svg+xml");
    mimeTypeMapping.put(".tiff", "image/tiff");
    mimeTypeMapping.put(".tif", "image/tiff");
    mimeTypeMapping.put(".djvu", "image/vnd.djvu");
    mimeTypeMapping.put(".djv", "image/vnd.djvu");
    mimeTypeMapping.put(".wbmp", "image/vnd.wap.wbmp");
    mimeTypeMapping.put(".cr2", "image/x-canon-cr2");
    mimeTypeMapping.put(".crw", "image/x-canon-crw");
    mimeTypeMapping.put(".ras", "image/x-cmu-raster");
    mimeTypeMapping.put(".cdr", "image/x-coreldraw");
    mimeTypeMapping.put(".pat", "image/x-coreldrawpattern");
    mimeTypeMapping.put(".cdt", "image/x-coreldrawtemplate");
    mimeTypeMapping.put(".erf", "image/x-epson-erf");
    mimeTypeMapping.put(".ico", "image/x-icon");
    mimeTypeMapping.put(".art", "image/x-jg");
    mimeTypeMapping.put(".jng", "image/x-jng");
    mimeTypeMapping.put(".nef", "image/x-nikon-nef");
    mimeTypeMapping.put(".orf", "image/x-olympus-orf");
    mimeTypeMapping.put(".psd", "image/x-photoshop");
    mimeTypeMapping.put(".pnm", "image/x-portable-anymap");
    mimeTypeMapping.put(".pbm", "image/x-portable-bitmap");
    mimeTypeMapping.put(".pgm", "image/x-portable-graymap");
    mimeTypeMapping.put(".ppm", "image/x-portable-pixmap");
    mimeTypeMapping.put(".rgb", "image/x-rgb");
    mimeTypeMapping.put(".xbm", "image/x-xbitmap");
    mimeTypeMapping.put(".xpm", "image/x-xpixmap");
    mimeTypeMapping.put(".xwd", "image/x-xwindowdump");
    mimeTypeMapping.put(".eml", "message/rfc822");
    mimeTypeMapping.put(".igs", "model/iges");
    mimeTypeMapping.put(".iges", "model/iges");
    mimeTypeMapping.put(".msh", "model/mesh");
    mimeTypeMapping.put(".mesh", "model/mesh");
    mimeTypeMapping.put(".silo", "model/mesh");
    mimeTypeMapping.put(".wrl", "model/vrml");
    mimeTypeMapping.put(".vrml", "model/vrml");
    mimeTypeMapping.put(".x3dv", "model/x3d+vrml");
    mimeTypeMapping.put(".x3d", "model/x3d+xml");
    mimeTypeMapping.put(".x3db", "model/x3d+binary");
    mimeTypeMapping.put(".manifest", "text/cache-manifest");
    mimeTypeMapping.put(".ics", "text/calendar");
    mimeTypeMapping.put(".icz", "text/calendar");
    mimeTypeMapping.put(".css", "text/css");
    mimeTypeMapping.put(".csv", "text/csv");
    mimeTypeMapping.put(".323", "text/h323");
    mimeTypeMapping.put(".html", "text/html");
    mimeTypeMapping.put(".htm", "text/html");
    mimeTypeMapping.put(".shtml", "text/html");
    mimeTypeMapping.put(".uls", "text/iuls");
    mimeTypeMapping.put(".mml", "text/mathml");
    mimeTypeMapping.put(".asc", "text/plain");
    mimeTypeMapping.put(".txt", "text/plain");
    mimeTypeMapping.put(".text", "text/plain");
    mimeTypeMapping.put(".pot", "text/plain");
    mimeTypeMapping.put(".brf", "text/plain");
    mimeTypeMapping.put(".rtx", "text/richtext");
    mimeTypeMapping.put(".sct", "text/scriptlet");
    mimeTypeMapping.put(".wsc", "text/scriptlet");
    mimeTypeMapping.put(".tm", "text/texmacs");
    mimeTypeMapping.put(".ts", "text/texmacs");
    mimeTypeMapping.put(".tsv", "text/tab-separated-values");
    mimeTypeMapping.put(".jad", "text/vnd.sun.j2me.app-descriptor");
    mimeTypeMapping.put(".wml", "text/vnd.wap.wml");
    mimeTypeMapping.put(".wmls", "text/vnd.wap.wmlscript");
    mimeTypeMapping.put(".bib", "text/x-bibtex");
    mimeTypeMapping.put(".boo", "text/x-boo");
    mimeTypeMapping.put(".h++", "text/x-c++hdr");
    mimeTypeMapping.put(".hpp", "text/x-c++hdr");
    mimeTypeMapping.put(".hxx", "text/x-c++hdr");
    mimeTypeMapping.put(".hh", "text/x-c++hdr");
    mimeTypeMapping.put(".c++", "text/x-c++src");
    mimeTypeMapping.put(".cpp", "text/x-c++src");
    mimeTypeMapping.put(".cxx", "text/x-c++src");
    mimeTypeMapping.put(".cc", "text/x-c++src");
    mimeTypeMapping.put(".h", "text/x-chdr");
    mimeTypeMapping.put(".htc", "text/x-component");
    mimeTypeMapping.put(".c", "text/x-csrc");
    mimeTypeMapping.put(".d", "text/x-dsrc");
    mimeTypeMapping.put(".diff", "text/x-diff");
    mimeTypeMapping.put(".patch", "text/x-diff");
    mimeTypeMapping.put(".hs", "text/x-haskell");
    mimeTypeMapping.put(".java", "text/x-java");
    mimeTypeMapping.put(".lhs", "text/x-literate-haskell");
    mimeTypeMapping.put(".moc", "text/x-moc");
    mimeTypeMapping.put(".p", "text/x-pascal");
    mimeTypeMapping.put(".pas", "text/x-pascal");
    mimeTypeMapping.put(".gcd", "text/x-pcs-gcd");
    mimeTypeMapping.put(".pl", "text/x-perl");
    mimeTypeMapping.put(".pm", "text/x-perl");
    mimeTypeMapping.put(".py", "text/x-python");
    mimeTypeMapping.put(".scala", "text/x-scala");
    mimeTypeMapping.put(".etx", "text/x-setext");
    mimeTypeMapping.put(".tk", "text/x-tcl");
    mimeTypeMapping.put(".tex", "text/x-tex");
    mimeTypeMapping.put(".ltx", "text/x-tex");
    mimeTypeMapping.put(".sty", "text/x-tex");
    mimeTypeMapping.put(".cls", "text/x-tex");
    mimeTypeMapping.put(".vcs", "text/x-vcalendar");
    mimeTypeMapping.put(".vcf", "text/x-vcard");
    mimeTypeMapping.put(".3gp", "video/3gpp");
    mimeTypeMapping.put(".axv", "video/annodex");
    mimeTypeMapping.put(".dl", "video/dl");
    mimeTypeMapping.put(".dif", "video/dv");
    mimeTypeMapping.put(".dv", "video/dv");
    mimeTypeMapping.put(".fli", "video/fli");
    mimeTypeMapping.put(".gl", "video/gl");
    mimeTypeMapping.put(".mpeg", "video/mpeg");
    mimeTypeMapping.put(".mpg", "video/mpeg");
    mimeTypeMapping.put(".mpe", "video/mpeg");
    mimeTypeMapping.put(".mp4", "video/mp4");
    mimeTypeMapping.put(".qt", "video/quicktime");
    mimeTypeMapping.put(".mov", "video/quicktime");
    mimeTypeMapping.put(".ogv", "video/ogg");
    mimeTypeMapping.put(".mxu", "video/vnd.mpegurl");
    mimeTypeMapping.put(".flv", "video/x-flv");
    mimeTypeMapping.put(".lsf", "video/x-la-asf");
    mimeTypeMapping.put(".lsx", "video/x-la-asf");
    mimeTypeMapping.put(".mng", "video/x-mng");
    mimeTypeMapping.put(".asf", "video/x-ms-asf");
    mimeTypeMapping.put(".asx", "video/x-ms-asf");
    mimeTypeMapping.put(".wm", "video/x-ms-wm");
    mimeTypeMapping.put(".wmv", "video/x-ms-wmv");
    mimeTypeMapping.put(".wmx", "video/x-ms-wmx");
    mimeTypeMapping.put(".wvx", "video/x-ms-wvx");
    mimeTypeMapping.put(".avi", "video/x-msvideo");
    mimeTypeMapping.put(".movie", "video/x-sgi-movie");
    mimeTypeMapping.put(".mpv", "video/x-matroska");
    mimeTypeMapping.put(".mkv", "video/x-matroska");
    mimeTypeMapping.put(".ice", "x-conference/x-cooltalk");
    mimeTypeMapping.put(".sisx", "x-epoc/x-sisx-app");
    mimeTypeMapping.put(".vrm", "x-world/x-vrml");
    return mimeTypeMapping;
  }

  public static DocumentFormat getDocumentFormat(String extension,boolean parse) {
    if (extension.indexOf("\\.") == -1) {
      extension = "." + extension;
    }
    switch (extension.toLowerCase()) {
      case "msg":
      case ".msg":
        return DocumentFormat.Msg;
      case ".doc":
      case "doc":
      case "docx":
      case ".docx":
        return DocumentFormat.Word;
      case ".xls":
      case ".xlsx":
      case "xls":
      case "xlsx":
        return DocumentFormat.Excel;
      case ".ppt":
      case ".pptx":
      case "ppt":
      case "pptx":
        return DocumentFormat.Ppt;
      case ".pdf":
      case "pdf":
        return DocumentFormat.Pdf;
      case ".txt":
      case "txt":
      case "csv":
      case ".csv":
        return DocumentFormat.Text;
      case "mp3":
      case ".mp3":
        return DocumentFormat.Mp3;

    }
    String mime = Mappings.get(extension.toLowerCase());
    if (mime == null) {
      return DocumentFormat.Unknown;
    }
    int index = mime.indexOf('/');
    if (index < 0) {
      return DocumentFormat.Unknown;
    }
    String prefix = mime.substring(0, index);
    switch (prefix) {
      case "audio":
        return DocumentFormat.Audio;
      case "image":
        if (mime.equalsIgnoreCase("image/bmp") || mime.equalsIgnoreCase("image/gif") ||
          mime.equalsIgnoreCase("image/jpeg") || mime.equalsIgnoreCase("image/png") ||
          mime.equalsIgnoreCase("image/webp") || mime.equalsIgnoreCase("image/svg+xml")) {
          return DocumentFormat.Image;
        }
        break;
      case "text":
        if(mime.contains("html") && parse){
          return DocumentFormat.Html;
        }
        return DocumentFormat.Text;
      case "video":
        return DocumentFormat.Video;
    }
    return DocumentFormat.Unknown;
  }

  public static String getBasePath(String path) {
    Preconditions.checkNotNull(path);
    int index = path.indexOf(".");
    if (path.contains("thumb")) {
      index = path.lastIndexOf(".");
    }
    if (index > 0) {
      return path.substring(0, index);
    } else {
      return path;
    }
  }

  public static String getFileExt(String path) {
    Preconditions.checkNotNull(path);
    int index = path.lastIndexOf(".");
    if (index > 0) {
      return path.substring(index + 1);
    } else {
      return "";
    }
  }

  public static String getMime(String extension) {
    if (extension.indexOf("\\.") == -1) {
      extension = "." + extension;
    }
    String mime = Mappings.get(extension.toLowerCase());
    return mime;
  }

  public static String getMD5Fast(byte[] data) {
    //文件按2*1024*1024 （2M）分片，按顺序取每片的前1024字节（最后一片不足1024则取全部），合并起来作为md5的输入，取md5值
    int chunkSize = 1024 * 1024 * 2;
    int lastSize = data.length % chunkSize;
    byte[] bytes = new byte[(data.length / chunkSize) * 1024 + Math.min(1024, lastSize)];
    int offset = 0, count = 0;
    do {
      int len = Math.min(1024, data.length - offset);
      System.arraycopy(data, chunkSize * count, bytes, 1024 * count, len);
      count++;
      offset += chunkSize;
    } while (offset < data.length);
    return getMD5(bytes);
  }

  public static String getThumbBaseName(String path) {
    return path.substring(0, path.indexOf("."));
  }

  public static int getThumbIndex(String path) {
    return Integer.valueOf(path.split("\\.")[2]);
  }


  public static String getSha256(byte[] data) {
    return Base64Utils.encodeToString(DigestUtils.sha256(data));
  }

  public static String getMD5(byte[] data) {
    return DigestUtils.md5Hex(data);
  }

  public static String getSHA1(byte[] data) {
    return Base64Utils.encodeToString(DigestUtils.sha(data));
  }

}