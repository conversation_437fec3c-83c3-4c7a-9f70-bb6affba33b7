package com.facishare.fsc.common.utils;

import com.fxiaoke.common.crypto.HexUtil;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * Created by <PERSON> on 16/7/4.
 */
public class AES256Utils {
    public static final String aesKey = "nirtHUNF/Ct8J7sf40VaIQui0N5r8gcbxGXKxRhu1C4=";
    public static final String aesIv = "jwNz4Ia8OHVpPyEXIQjJ2g==";
    public static final Cipher decrypt_c;
    public static final Cipher encrypt_c;

    static {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(aesKey);
            byte[] ivBytes = Base64.getDecoder().decode(aesIv);
            IvParameterSpec iv = new IvParameterSpec(ivBytes);
            decrypt_c = Cipher.getInstance("AES/CBC/PKCS5Padding");
            encrypt_c = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
            decrypt_c.init(Cipher.DECRYPT_MODE, key, iv, new SecureRandom(keyBytes));
            encrypt_c.init(Cipher.ENCRYPT_MODE, key, iv, new SecureRandom(keyBytes));
        } catch (Exception e) {
            throw new IllegalArgumentException(e);
        }
    }

    public static String encode(String source) {
        try {
            byte[] resultByte = encrypt_c.doFinal(source.getBytes(StandardCharsets.UTF_8));
            return HexUtil.bytes2HexStr(resultByte);
        } catch (Exception e) {
            throw new IllegalArgumentException(source, e);
        }
    }

    public static String decode(String source) {
        String result;
        try {
            byte[] resultByte = decrypt_c.doFinal(HexUtil.hexStr2Bytes(source));
            result = new String(resultByte, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new IllegalArgumentException(source, e);
        }
        return result;
    }
}
